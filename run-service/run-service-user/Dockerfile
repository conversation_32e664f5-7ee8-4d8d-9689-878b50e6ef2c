FROM amazoncorretto:11
MAINTAINER xiaoyu

#时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai'>/etc/timezone

#字体
RUN yum install -y fontconfig
RUN yum install -y urw-fonts
RUN fc-cache –fv

#设置字符集
#ENV LANG en_GB.utf8
ENV LANG C.UTF-8

ADD /target/run-service-user-*.jar app.jar
ENTRYPOINT java -server --illegal-access=deny --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED -Djdk.attach.allowAttachSelf=true -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/oom_dump.log -XX:ErrorFile=java_error.log -Djava.security.egd=file:/dev/./urandom -jar /app.jar

EXPOSE 30001
