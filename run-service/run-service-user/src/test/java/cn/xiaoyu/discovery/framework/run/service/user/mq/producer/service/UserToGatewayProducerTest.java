package cn.xiaoyu.discovery.framework.run.service.user.mq.producer.service;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.service.user.mq.producer.gateway.MoreServiceSendUserWsMsgToGatewayProducer;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @since 2023/7/20 21:30
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserToGatewayProducerTest {
    @Resource
    private MoreServiceSendUserWsMsgToGatewayProducer moreServiceSendUserWsMsgToGatewayProducer;

    @Test
    public void sendServiceMsg() {
        log.info("》》》》》》》》》》发送业务消息消息start《《《《《《《《《《《《《《《");
        PlatformIdContextHolder.setPlatformId("2000");
        // userToGatewayProducer.sendWebsocketMessage(WebSocketEventTypeEum.ACCOUNT_FOR_FORCED_OFFLINE, null,
        // "zhangsan");
        log.info("》》》》》》》》》》发送业务消息消息消息end《《《《《《《《《《《《《《《");
    }
}
