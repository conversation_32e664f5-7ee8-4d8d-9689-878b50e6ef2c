package cn.xiaoyu.discovery.framework.run.service.user.job.agent;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @since 2023/4/12 23:25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class AgentRebateCommissionHandlerTest {

    @Resource
    private AgentRebateCommissionHandler agentRebateCommissionHandler;

    @Test
    void execute() {
        agentRebateCommissionHandler.execute();
    }
}
