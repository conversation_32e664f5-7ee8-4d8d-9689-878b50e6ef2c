package cn.xiaoyu.discovery.framework.run.service.user.mq.producer;

import java.math.BigDecimal;

import javax.annotation.Resource;

import cn.xiaoyu.discovery.framework.run.service.user.mq.producer.pay.UserServiceUpdateUserBookProducer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserServiceUpdateUserBookProducerTest {
    @Resource
    private UserServiceUpdateUserBookProducer userServiceUpdateUserBookProducer;

    @Test
    public void sendUserChangeBookMsg() {
        log.info("》》》》》》》》》》发送用户改变账本消息start《《《《《《《《《《《《《《《");
        PlatformIdContextHolder.setPlatformId("0");
        userServiceUpdateUserBookProducer.sendUserToFinanceWashMessage(1L, BigDecimal.ZERO, BigDecimal.ZERO, null);
        log.info("》》》》》》》》》》发送用户改变账本消息end《《《《《《《《《《《《《《《");
    }
}
