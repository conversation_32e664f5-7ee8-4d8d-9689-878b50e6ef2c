package cn.xiaoyu.discovery.framework.run.service.user.mq.producer;

import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * @since 2023/4/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserSendUserMsgToUserProducer1Test {
//    @Resource
//    private UserSendUserMsgToUserProducer1 userSendUserMsgToUserProducer1;
//
//    @Test
//    public void sendServiceMsg() {
//        log.info("》》》》》》》》》》发送业务消息消息start《《《《《《《《《《《《《《《");
//        PlatformIdContextHolder.setPlatformId("0");
//        // userSendServiceMessageProducer.sendUserToUserNoticeActivityMessage(1L, 1L, "测试标题", "测试内容", "附加信息", "跳转链接",
//        // IUserToUserNoticeMessage.UserToUserNoticeActivityMessage.ACTIVITY_SYSTEM_MESSAGE_TYPE);
//        log.info("》》》》》》》》》》发送业务消息消息消息end《《《《《《《《《《《《《《《");
//    }
}
