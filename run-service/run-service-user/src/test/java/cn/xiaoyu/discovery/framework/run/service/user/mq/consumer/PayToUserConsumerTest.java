package cn.xiaoyu.discovery.framework.run.service.user.mq.consumer;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/17 14:49
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class PayToUserConsumerTest {

    // @Resource
    // private UserLevelUpDownService userLevelUpDownService;
    //
    // @Test
    // void execute() {
    // userLevelUpDownService.computeUserLevelByRecharge(1000L, 10048L, BigDecimal.TEN);
    // }
}
