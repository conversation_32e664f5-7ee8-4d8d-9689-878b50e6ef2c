package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("返佣比例Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentRebateGameRateResp implements IResp {
    private static final long serialVersionUID = 2249916014307765820L;

    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;

    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;

    @ApiModelProperty(value = "有效投注起")
    private BigDecimal betAmountStart;

    @ApiModelProperty(value = "有效投注止")
    private BigDecimal betAmountEnd;

    @ApiModelProperty(value = "赢起")
    private BigDecimal winAmountStart;

    @ApiModelProperty(value = "赢止")
    private BigDecimal winAmountEnd;

    @ApiModelProperty(value = "输起")
    private BigDecimal loseAmountStart;

    @ApiModelProperty(value = "输止")
    private BigDecimal loseAmountEnd;

    @ApiModelProperty(value = "返佣比例0.0001格式")
    @JacksonAmountFmtIgnore
    private BigDecimal receiveRebateRate;

}
