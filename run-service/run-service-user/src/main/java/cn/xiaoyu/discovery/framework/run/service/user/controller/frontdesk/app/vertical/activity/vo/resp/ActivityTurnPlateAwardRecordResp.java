package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.desensitize.annotation.JacksonSlideDesensitize;
import cn.xiaoyu.discovery.framework.run.common.desensitize.eum.SlideDesensitizationTypeEum;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TurnPlateAwardTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("转盘奖品记录Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTurnPlateAwardRecordResp implements IResp {
    private static final long serialVersionUID = -2857013346513693147L;

    @JacksonSlideDesensitize(type = SlideDesensitizationTypeEum.CUSTOM, leftPlainTextLen = 1, rightPlainTextLen = 1)
    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty(value = "奖品类型")
    private TurnPlateAwardTypeEum awardType;

    @ApiModelProperty(value = "实物奖品")
    private String realAward;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "奖品数量")
    private Integer awardNum;

    @ApiModelProperty(value = "是否实物")
    private Boolean isReal;

    @ApiModelProperty(value = "领奖时间")
    @JacksonZoneId
    private Long createTime;
}
