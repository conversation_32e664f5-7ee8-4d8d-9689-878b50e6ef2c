package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
@ApiModel("玩家洗码批次汇总Resp")
@Data
public class WashUserTotalResp implements IResp {
    private static final long serialVersionUID = 2766184174915468342L;

    @ApiModelProperty(value = "洗码总金额")
    private BigDecimal washTotalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "总投注额")
    private BigDecimal betTotalAmount = BigDecimal.ZERO;
}
