package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通知未读信息Resp
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("通知未读信息Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeUnReadInfoResp implements IResp {

    private static final long serialVersionUID = -5842675445628235998L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    private NoticeTypeEum noticeTypeEum;

    /**
     * 未读消息数
     */
    @ApiModelProperty(value = "未读消息数")
    private Long unReadCount;

    /**
     * 是否显示小红点
     */
    @ApiModelProperty(value = "是否显示小红点")
    private Boolean readPoint;
}
