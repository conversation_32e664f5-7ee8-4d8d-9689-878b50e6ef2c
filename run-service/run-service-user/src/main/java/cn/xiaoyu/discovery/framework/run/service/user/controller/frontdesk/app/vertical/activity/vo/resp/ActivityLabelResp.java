package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/5/9
 */

@ApiModel("活动标签Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityLabelResp extends IdResp {
    private static final long serialVersionUID = 2537527229281105580L;

    @ApiModelProperty(value = "标签名")
    private String labelName;

}
