package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.member.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 14:16
 */
@ApiModel("代理成员信息Req")
@Data
public class AgentMemberInfoReq extends CreateTimeReq {

    private static final long serialVersionUID = 7456082061976540772L;
    @ApiModelProperty("成员-会员ID")
    private Long userId;
    @ApiModelProperty("成员-会员编码")
    private Long userCode;
}
