package cn.xiaoyu.discovery.framework.run.service.user.lockexecutor;

import java.lang.reflect.Method;

import org.springframework.stereotype.Component;

import com.baomidou.lock.LockFailureStrategy;

import cn.xiaoyu.discovery.framework.run.common.exception.BizException;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.system.exception.SystemCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.constant.UserServiceConstant;

/**
 * <AUTHOR>
 * @date 2023/9/22
 * @description @Lock4j 自定义抛出异常
 **/
@Component
public class UserServiceLockExecutor implements LockFailureStrategy {
    @Override
    public void onLockFailure(String key, Method method, Object[] arguments) {
        if (key.contains(UserServiceConstant.RedisKey.USER_REGISTER)) {
            // 注册
            throw new BizException(SystemCodeException.RESUBMIT);
        } else {
            throw new BizException(CommonCodeException.LOCK_FAILED);
        }

    }
}
