package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.resp;

import cn.xiaoyu.discovery.framework.run.service.user.entity.task.TaskAchieveGoalsRecordEntity;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/7/14
 */

@Data
public class TaskAchieveRecordActivityResp extends TaskAchieveGoalsRecordEntity {
    private static final long serialVersionUID = 3812415090498449669L;

    @ApiModelProperty(value = "任务类型")
    private TaskTypeEum taskTypeEum;
}
