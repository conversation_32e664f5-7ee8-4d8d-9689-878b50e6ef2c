package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/7/17
 */

@Data
@Builder
public class LuckyEggEnableResp implements IResp {
    private static final long serialVersionUID = 2861109114273478367L;

    @ApiModelProperty(value = "金蛋可砸次数")
    private Integer goldPlayNum;

    @ApiModelProperty(value = "银蛋可砸次数")
    private Integer silverPlayNum;

    @ApiModelProperty(value = "铜蛋可砸次数")
    private Integer copperPlayNum;
}
