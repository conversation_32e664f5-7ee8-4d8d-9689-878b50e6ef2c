package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动奖励领取记录Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityReceiveAwardRecordResp implements IResp {
    private static final long serialVersionUID = -3745203809825366522L;

    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "领取金额")
    private BigDecimal receiveAmount;

    @ApiModelProperty(value = "领取时间")
    private Long receiveTime;

}
