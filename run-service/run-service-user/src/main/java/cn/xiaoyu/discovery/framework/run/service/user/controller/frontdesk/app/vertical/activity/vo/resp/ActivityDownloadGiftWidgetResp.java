package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.activity.vo.resp.ActivityDownloadGiftAmountItemResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ActivityDownloadGiftWidgetResp implements IResp {
    private static final long serialVersionUID = 7865032929184074563L;

    @ApiModelProperty(value = "app弹窗图片地址")
    @ReplaceDomainName
    private String appImgUrl;

    @ApiModelProperty(value = "总奖励金额")
    private BigDecimal totalAmount;

}
