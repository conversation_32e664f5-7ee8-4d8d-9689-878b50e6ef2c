package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.desensitize.annotation.JacksonRegexDesensitize;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.DownloadGiftStatusEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.DownloadGiftTaskTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ActivityDownloadGiftItemResp extends IdResp {
    private static final long serialVersionUID = -8375908717305400066L;

    @ApiModelProperty(value = "第几天")
    private Integer sequence;

    @ApiModelProperty(value = "目标金额")
    private BigDecimal targetAmount;

    @ApiModelProperty(value = "已完成金额")
    private BigDecimal completeAmount;

    @ApiModelProperty(value = "完成进度")
    @JacksonAmountFmtIgnore
    private BigDecimal completeRate;

    @ApiModelProperty(value = "时间区间")
    private String taskDate;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "下载有礼活动完成状态")
    private DownloadGiftStatusEum downloadGiftStatusEum;

    @ApiModelProperty(value = "下载有礼任务类型")
    private DownloadGiftTaskTypeEum downloadGiftTaskTypeEum;
}
