package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.label.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.label.EffectEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.label.EffectiveTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.label.LabelTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.label.QualityTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 前端-用户标签信息
 * @date 2024/1/22
 */
@ApiModel("用户标签信息Resp")
@Data
public class UserLabelResp implements IResp {

    /**
     * 用户标签主键ID
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 标签ID/标签编码
     */
    @ApiModelProperty(value = "标签ID/标签编码")
    private Long labelCode;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String labelName;

    /**
     * 标签类型
     */
    @ApiModelProperty(value = "标签类型")
    private LabelTypeEum labelTypeEum;

    /**
     * 标签图标地址
     */
    @ApiModelProperty(value = "标签图标地址")
    private String labelImgUrl;

    /**
     * 有效类型
     */
    @ApiModelProperty(value = "有效类型")
    private EffectiveTypeEum effectiveTypeEum;

    /**
     * 有效时间
     */
    @ApiModelProperty(value = "有效时间")
    @JacksonZoneId
    private Long effectiveTime;

    /**
     * 品质编码
     */
    @ApiModelProperty(value = "品质类型")
    private QualityTypeEum qualityTypeEum;

    /**
     * 标签作用
     */
    @ApiModelProperty(value = "标签作用")
    private EffectEum effectEum;

    /**
     * 作用值
     */
    @ApiModelProperty(value = "作用值")
    private BigDecimal effectValue;
}
