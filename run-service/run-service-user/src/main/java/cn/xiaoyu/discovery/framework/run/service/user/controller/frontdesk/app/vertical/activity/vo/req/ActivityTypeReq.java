package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动分类请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTypeReq extends PageReq {
    private static final long serialVersionUID = -2724273749082186502L;
    @ApiModelProperty(name = "活动标签id，不传返回全部")
    private Long labelId;

}
