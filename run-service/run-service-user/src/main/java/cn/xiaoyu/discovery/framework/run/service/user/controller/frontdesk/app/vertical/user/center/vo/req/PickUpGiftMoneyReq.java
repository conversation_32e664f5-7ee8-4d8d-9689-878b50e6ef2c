package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.GiftMoneyTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/4/11 15:38
 */
@ApiModel("领取礼金Req")
@Data
public class PickUpGiftMoneyReq implements IReq {
    private static final long serialVersionUID = -2632414344267735326L;
    @ApiModelProperty("领取的等级枚举")
    private LevelEum levelEum;
    @ApiModelProperty("领取的礼金类型")
    private GiftMoneyTypeEum giftMoneyTypeEum;
}
