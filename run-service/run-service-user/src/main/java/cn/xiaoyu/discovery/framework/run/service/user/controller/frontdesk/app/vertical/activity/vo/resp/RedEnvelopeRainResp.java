package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("红包雨Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedEnvelopeRainResp extends IdResp {

    private static final long serialVersionUID = -6033047658091612987L;

    @ApiModelProperty(value = "红包标题")
    private String title;

    @ApiModelProperty(value = "活动开始时间-时间戳")
    private Long beginTime;

    @ApiModelProperty(value = "结束时间/红包失效时间-时间戳")
    private Long remainingTime;

    @ApiModelProperty(value = "活动开始时间 - HH:mm:ss")
    private String beginTimeStr;

    @ApiModelProperty(value = "结束时间/红包失效时间")
    private String remainingTimeStr;

	@ApiModelProperty(value = "期数")
	private Integer frequency;

}
