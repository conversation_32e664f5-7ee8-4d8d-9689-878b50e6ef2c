package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskStatusEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("任务列表请求Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskItemResp extends IdResp {

    private static final long serialVersionUID = -3649177244322514314L;
    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "任务图标")
    private String taskIcon;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "金额目标")
    private BigDecimal targetAmount;

    @ApiModelProperty(value = "已完成金额")
    private BigDecimal completeAmount;

    @ApiModelProperty(value = "进度")
    @JacksonAmountFmtIgnore
    private BigDecimal progressRate;

    @ApiModelProperty(value = "任务类型")
    private TaskTypeEum taskTypeEum;

    @ApiModelProperty(value = "是否展示指定游戏按钮")
    private Boolean showLimitGame;

    @ApiModelProperty(value = "任务完成状态")
    private TaskStatusEum taskStatusEum;

}
