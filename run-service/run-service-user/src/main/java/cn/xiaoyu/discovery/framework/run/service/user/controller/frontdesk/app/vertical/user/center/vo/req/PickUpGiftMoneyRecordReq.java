package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/4/11 15:38
 */
@ApiModel("领取礼金Req")
@Data
public class PickUpGiftMoneyRecordReq extends CreateTimePageReq {
    private static final long serialVersionUID = -7394113161616371804L;
    @ApiModelProperty(value = "用户Id", hidden = true)
    private Long userId;
}
