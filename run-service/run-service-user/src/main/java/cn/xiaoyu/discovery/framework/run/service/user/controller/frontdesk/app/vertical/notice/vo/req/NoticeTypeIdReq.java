package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 单一类型通知读取Req
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("单一类型通知读取Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NoticeTypeIdReq extends IdReq {

    private static final long serialVersionUID = 6107697307524504726L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    @NotNull(message = "通知类型不能为空")
    private NoticeTypeEum noticeTypeEum;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "平台ID", hidden = true)
    private Long platformId;
}
