package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/7/25
 */

@ApiModel("活动申请请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityApplyReq implements IReq {
    private static final long serialVersionUID = 7175164645733593839L;

    @ApiModelProperty(name = "活动id")
    private Long activityId;

    @ApiModelProperty(name = "活动报名参数")
    private String applyParam;
}
