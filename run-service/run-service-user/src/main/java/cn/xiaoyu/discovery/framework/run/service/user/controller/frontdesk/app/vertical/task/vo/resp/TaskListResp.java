package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.resp;

import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/6/7
 */

@Data
public class TaskListResp implements IResp {
    private static final long serialVersionUID = -91675436701548334L;

    @ApiModelProperty(value = "活动标题")
    private List<TaskItemResp> list;

    @ApiModelProperty(value = "任务规则")
    private String rules;

    @ApiModelProperty(value = "任务规则")
    private Long total;
}
