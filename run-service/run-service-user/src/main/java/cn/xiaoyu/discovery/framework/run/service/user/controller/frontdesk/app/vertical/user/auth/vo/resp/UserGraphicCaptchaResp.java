package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("验证码Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserGraphicCaptchaResp implements IResp {
    private static final long serialVersionUID = 6981665471393743237L;
    @ApiModelProperty("图形验证码的base64编码")
    private String base64Captcha;

    @ApiModelProperty("Authorization 一个随机的uuid加上sessionId")
    private String authorization;

}
