package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.UserUnderDirectlyEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/27 19:27
 * @Description
 */
@Data
@ApiModel("佣金结算记录Resp")
public class FindPageSettlementRecordReq extends PageReq {
    private static final long serialVersionUID = -2388012679024681327L;
    @ApiModelProperty("创建开始时间")
    @NotNull(message = "开始时间不能为空")
    private Long startCreateTime;
    @ApiModelProperty("创建结束时间")
    @NotNull(message = "结束时间不能为空")
    private Long endCreateTime;

    @ApiModelProperty("直属、非直属、全部")
    @NotNull(message = "是否直属用户不能为空")
    private UserUnderDirectlyEum userUnderDirectlyEum;
}
