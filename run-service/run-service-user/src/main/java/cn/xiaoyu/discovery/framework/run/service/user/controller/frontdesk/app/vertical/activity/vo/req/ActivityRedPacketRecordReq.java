package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/5/12
 */

@ApiModel("红包记录Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRedPacketRecordReq extends PageReq {
    private static final long serialVersionUID = 3264116751466223737L;

    @ApiModelProperty(name = "用户id")
    private Long userId;
}
