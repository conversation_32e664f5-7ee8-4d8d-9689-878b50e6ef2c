package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.AnnounceLevelEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公告通知Resp
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("公告通知Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnnounceBaseResp implements IResp {

    private static final long serialVersionUID = 5821172607756085604L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 公告等级
     */
    @ApiModelProperty(value = "公告等级")
    private AnnounceLevelEum announceLevelEum;
}
