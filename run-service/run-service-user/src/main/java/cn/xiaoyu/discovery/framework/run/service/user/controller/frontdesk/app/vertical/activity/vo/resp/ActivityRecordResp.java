package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.ApplyStatusTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动进度查询Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRecordResp implements IResp {
    private static final long serialVersionUID = -323425914436750696L;

    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "报名时间")
    @JacksonZoneId
    private Long applyDate;

    @ApiModelProperty(value = "处理状态")
    private ApplyStatusTypeEum applyStatus;

}
