package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.activity.ActivityTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskStatusEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityStatusItemResp implements IResp {
    private static final long serialVersionUID = 7297530612956417087L;

    @ApiModelProperty(value = "绑定活动枚举")
    private ActivityTypeEum bindActivityEum;

    @ApiModelProperty(value = "绑定活动状态")
    private TaskStatusEum taskStatusEum;
}
