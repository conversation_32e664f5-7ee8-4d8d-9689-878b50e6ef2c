package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.prizepool;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollectionUtil;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.prizepool.vo.req.FindPagePrizePoolDetailReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.prizepool.vo.resp.PrizePoolPeriodDetailResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req.PrizePoolAwardRecordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req.PrizePoolEntryPrizeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req.PrizePoolRankReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp.PrizePoolAwardRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp.PrizePoolPeriodListResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp.PrizePoolRuleResp;
import cn.xiaoyu.discovery.framework.run.service.user.entity.prizepool.control.PrizePoolRuleControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.PrizePoolBizService;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.control.PrizePoolRuleControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.period.PrizePoolPeriodService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/12/6
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@RequestMapping(PrizePoolPCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.奖池-奖池期数", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + PrizePoolPCController.MODULE_PREFIX + "模块-竖版app-奖池-奖池期数")
public class PrizePoolPCController implements IModulePrefix {

    private final PrizePoolBizService prizePoolBizService;
    private final PrizePoolRuleControlService prizePoolRuleControlService;
    private final PrizePoolPeriodService prizePoolPeriodService;

    @PostMapping("/getPeriodList")
    @ApiOperation("1.获取可参加奖池列表")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<List<PrizePoolPeriodListResp>> getPeriodList() {
        return R.success(prizePoolBizService.getPeriodList());
    }

    @PostMapping("/getPeriodDetail")
    @ApiOperation("2.获取奖池详情")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PrizePoolPeriodListResp> getPeriodDetail(@RequestBody IdReq req) {
        return R.success(prizePoolBizService.getPeriodDetail(req));
    }

    @PostMapping("/entryPrizePool")
    @ApiOperation("3.参与奖池")
    @ApiOperationSupport(order = 3)
    @Encrypt
    @Decrypt
    public R<Boolean> entryPrizePool(@RequestBody PrizePoolEntryPrizeReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        Long platformId = Long.valueOf(PlatformIdContextHolder.getPlatformId());
        return R.success(prizePoolBizService.entryPrizePool(userId, platformId, req));
    }

    @PostMapping("/getLastPrizePoolRecords")
    @ApiOperation("4.上期奖池中奖记录")
    @ApiOperationSupport(order = 4)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PrizePoolAwardRecordResp> getLastPrizePoolRecords(@RequestBody PrizePoolAwardRecordReq req) {
        return R.success(prizePoolBizService.getLastPrizePoolRecords(req));
    }

    @PostMapping("/getPrizePoolRank")
    @ApiOperation("5.查看奖金池榜单")
    @ApiOperationSupport(order = 5)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PrizePoolAwardRecordResp> getPrizePoolRank(@RequestBody @Validated PrizePoolRankReq req) {
        return R.success(prizePoolBizService.getPrizePoolRank(req));
    }

    @PostMapping("/getPrizePoolRule")
    @ApiOperation("6.获取奖金池规则")
    @ApiOperationSupport(order = 6)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PrizePoolRuleResp> getPrizePoolRule() {
        List<PrizePoolRuleControlEntity> list = prizePoolRuleControlService.findAll();
        if (CollectionUtil.isNotEmpty(list)) {
            PrizePoolRuleControlEntity control = list.get(0);
            return R.success(PrizePoolRuleResp.builder().rule(control.getRule()).build());
        }
        return R.success(PrizePoolRuleResp.builder().build());
    }

    @PostMapping("/queryPrizePoolPeriodDetail")
    @ApiOperation("7.奖池参与记录")
    @ApiOperationSupport(order = 7)
    @Encrypt
    @Decrypt
    public R<PageResp<PrizePoolPeriodDetailResp>> globalCoinChangeRecord(@RequestBody FindPagePrizePoolDetailReq req) {
        req.setUserId(FrontdeskUserInfoUtils.getUserId());
        return R.success(prizePoolPeriodService.queryPage(req));
    }

}
