package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("用户忘记密码手机验证码校验Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserForgetPhoneVerifyReq implements IReq {
    private static final long serialVersionUID = 1629898911284587414L;

    @ApiModelProperty(value = "手机号前缀", required = true)
    @NotNull(message = UserCodeException.PHONE_PREFIX_IS_NULL_I18N_CODE)
    private String phonePrefix;

    @ApiModelProperty(value = "手机号", required = true)
    @NotNull(message = UserCodeException.PHONE_IS_NULL_I18N_CODE)
    private String phone;

    @ApiModelProperty(value = "手机验证码", required = true)
    @NotNull(message = UserCodeException.PHONE_CODE_IS_NULL_I18N_CODE)
    private String phoneCode;
}
