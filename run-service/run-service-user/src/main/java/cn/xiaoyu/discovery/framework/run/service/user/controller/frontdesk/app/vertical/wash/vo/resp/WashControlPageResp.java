package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WashControlPageResp implements IResp {
    private static final long serialVersionUID = -419614601997358666L;

    @ApiModelProperty(value = "数据list")
    private List<WashControlResp> list = new ArrayList<>();

    @ApiModelProperty(value = "数据总数量")
    private Long total = 0L;

    @ApiModelProperty(value = "洗码总金额")
    private BigDecimal washTotalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "最后一次洗码时间")
    @JacksonZoneId
    private Long lastWashTime;

}
