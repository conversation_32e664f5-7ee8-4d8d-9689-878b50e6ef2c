package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.ruleconfig.vo.req;

import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.ruleconfig.RuleTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 查询规则配置Req
 * @date 2024/2/26
 */
@ApiModel("查询规则配置Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FindRuleTypeReq implements IReq {

    private static final long serialVersionUID = -2234711809128185612L;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private List<RuleTypeEum> ruleTypeEumList;
}
