package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth;

import java.util.Objects;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.context.utils.ContextDTOUtils;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.eum.SystemTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.TerminalTypeEum;
import cn.xiaoyu.discovery.framework.run.common.exception.BizException;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserEmailCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserForgetPasswordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserPhoneCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.auth.UserForgetPasswordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.captcha.UserCaptchaService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 忘记密码
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 3)
@RequestMapping(UserRegisVerticalController.MODULE_PREFIX)
@Api(value = "3.忘记密码", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + UserRegisVerticalController.MODULE_PREFIX + "模块-竖版app忘记密码操作")
public class UserForgetPasswordVerticalController implements IModulePrefix {

    private final UserForgetPasswordService userForgetPasswordService;

    private final UserCaptchaService userCaptchaService;

    private final UserService userService;

    @PostMapping("/sendForgetEmailCaptcha")
    @ApiOperation("1.发送邮箱验证码")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Void> sendEmailCaptcha(@RequestBody @Valid UserEmailCaptchaReq req) {
        if (!Validator.isEmail(req.getEmail())) {
            throw new BizException(UserCodeException.EMAIL_FORMAT_IS_NOT_CORRECT);
        }
        if (CollectionUtil.isEmpty(userService.findByEmail(req.getEmail()))) {
            // 邮箱不存在-未绑定
            throw new BizException(UserCodeException.EMAIL_IS_NOT_BINDING);
        }
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        this.userCaptchaService.sendForgetPasswordEmailCaptcha(req.getEmail(), terminalTypeEum, systemTypeEum);
        return R.success();
    }

    @PostMapping("/sendForgetPhoneCaptcha")
    @ApiOperation("2.发送手机验证码")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Void> sendPhoneCaptcha(@RequestBody @Valid UserPhoneCaptchaReq req) {
        if (Objects.isNull(userService.findByPhone(req.getPhonePrefix(), req.getPhone()))) {
            // 手机号码不存在-未注册
            throw new BizException(UserCodeException.PHONE_IS_NOT_BINDING);
        }
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        this.userCaptchaService.sendForgetPasswordPhoneCaptcha(req.getPhonePrefix(), req.getPhone(), terminalTypeEum,
            systemTypeEum);
        return R.success();
    }

    // @PostMapping("/forgetEmailCaptchaVerify")
    // @ApiOperation("3.忘记密码邮箱验证")
    // @ApiOperationSupport(order = 3)
    // @SaIgnore
    // public R<Void> forgetEmailCaptchaVerify(@RequestBody @Valid UserForgetEmailVerifyReq req) {
    // TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
    // SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
    // this.userCaptchaService.verifyForgetPasswordEmailCaptcha(req.getEmail(), req.getEmailCode(), terminalTypeEum,
    // systemTypeEum);
    // return R.success();
    // }
    //
    // @PostMapping("/forgetPhoneCaptchaVerify")
    // @ApiOperation("4.忘记密码手机验证")
    // @ApiOperationSupport(order = 4)
    // @SaIgnore
    // public R<Void> forgetPhoneCaptchaVerify(@RequestBody @Valid UserForgetPhoneVerifyReq req) {
    // TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
    // SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
    // this.userCaptchaService.verifyForgetPasswordPhoneCaptcha(req.getPhonePrefix(), req.getPhone(),
    // req.getPhoneCode(), terminalTypeEum, systemTypeEum);
    // return R.success();
    // }

    @PostMapping("/forgetPasswordUpdate")
    @ApiOperation("3.忘记密码进行密码修改")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Boolean> forgetPasswordUpdate(@RequestBody @Valid UserForgetPasswordReq req) {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        return R.success(this.userForgetPasswordService.forgetPassword(req, terminalTypeEum, systemTypeEum));
    }

}
