package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@ApiModel("签到配置Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySignConfigResp implements IResp {

	private static final long serialVersionUID = -7862297684998963010L;

	@ApiModelProperty("第一天签到金币最大值")
	private BigDecimal day1Max;

	@ApiModelProperty("第二天签到金币最大值")
	private BigDecimal day2Max;

	@ApiModelProperty("第三天签到金币最大值")
	private BigDecimal day3Max;

	@ApiModelProperty("第四天签到金币最大值")
	private BigDecimal day4Max;

	@ApiModelProperty("第五天签到金币最大值")
	private BigDecimal day5Max;

	@ApiModelProperty("第六天签到金币最大值")
	private BigDecimal day6Max;

	@ApiModelProperty("第七天签到金币最大值")
	private BigDecimal day7Max;

	@ApiModelProperty("连续签到天数（0-7）")
	private Integer signDays;

	@ApiModelProperty("今日是否签到（0：否，1：是）")
	private Integer todaySign;
	/**
	 * 活动规则文案
	 */
	@ApiModelProperty(value = "活动规则文案")
	private String activityRule;
}
