package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/4/29
 */

@ApiModel("抢红包请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedPacketTypeReq implements IReq {
    private static final long serialVersionUID = -3199534000775909107L;

    @ApiModelProperty(name = "红包id")
    private Long redId;
}
