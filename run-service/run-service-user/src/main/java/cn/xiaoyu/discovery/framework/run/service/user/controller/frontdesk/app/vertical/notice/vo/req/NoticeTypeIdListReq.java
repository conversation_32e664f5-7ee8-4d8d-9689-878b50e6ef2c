package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdListReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单一类型通知ID集合读取Req
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("单一类型通知ID集合读取Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NoticeTypeIdListReq extends IdListReq {

    private static final long serialVersionUID = -7575958637737711414L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    @NotNull(message = "通知类型不能为空")
    private NoticeTypeEum noticeTypeEum;
}
