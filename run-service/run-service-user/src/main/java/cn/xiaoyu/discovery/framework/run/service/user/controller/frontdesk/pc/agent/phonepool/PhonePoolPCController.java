package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.phonepool;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.phonepool.vo.req.FindPhonePoolInfoReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.phonepool.vo.resp.PhonePoolInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.phone.AgentPhoneShareConfigService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 前端-PC号码池信息
 * @date 2024/3/6
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@RequestMapping(PhonePoolPCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.规则配置信息",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + PhonePoolPCController.MODULE_PREFIX + "模块-PC号码池信息")
public class PhonePoolPCController implements IModulePrefix {
    private final AgentPhoneShareConfigService agentPhoneShareConfigService;

    @PostMapping("/getPhonePoolConfig")
    @ApiOperation("1.获取号码分享控制以及号码池信息")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PhonePoolInfoResp> getPhonePoolConfig(@RequestBody FindPhonePoolInfoReq req) {
        return R.success(agentPhoneShareConfigService.getPhonePoolConfig(req));
    }
}
