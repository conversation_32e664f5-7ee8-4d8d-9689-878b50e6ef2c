package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.system.exception.SystemCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.ChangePasswordTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("用户忘记密码修改Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserForgetPasswordReq implements IReq {
    private static final long serialVersionUID = 2438560107219732037L;

    @ApiModelProperty(value = "密码修改方式", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private ChangePasswordTypeEum changePasswordTypeEum;

    @ApiModelProperty(value = "邮箱", notes = "密码修改方式为邮箱时必传")
    private String email;

    @ApiModelProperty(value = "邮箱验证码")
    private String emailCode;

    @ApiModelProperty(value = "手机号前缀", required = true)
    private String phonePrefix;

    @ApiModelProperty(value = "手机号", notes = "密码修改方式为手机号时必传")
    private String phone;

    @ApiModelProperty(value = "手机验证码")
    private String phoneCode;

    @ApiModelProperty(value = "密码", required = true)
    @NotEmpty(message = SystemCodeException.PASSWORD_IS_EMPTY_I18N_CODE)
    private String password;

}
