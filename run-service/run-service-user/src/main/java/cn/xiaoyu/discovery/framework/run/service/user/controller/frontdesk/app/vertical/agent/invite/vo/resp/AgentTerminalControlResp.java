package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 教程链接
 */
@ApiModel("教程链接信息")
@Data
public class AgentTerminalControlResp implements IResp {

    private static final long serialVersionUID = -2237251357562584094L;

    @ApiModelProperty(value = "推广说明图地址")
    @ReplaceDomainName
    private String promoteImgUrl;

    @ApiModelProperty("邀请有礼-文本说明")
    private String inviteText;

}
