package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/15 21:22
 */
@Data
@ApiModel("佣金账本摘要信息Resp")
public class AgentCommissionInfoResp extends IdResp {
    private static final long serialVersionUID = 5387172672987241100L;

    @ApiModelProperty(value = "待领取佣金")
    private BigDecimal treatReceiveRebateAmount;
    @ApiModelProperty(value = "已领取佣金总额")
    private BigDecimal receiveRebateTotalAmount;
    /**
     * 待邀请有礼金额
     */
    @ApiModelProperty(value = "待邀请有礼金额")
    private BigDecimal treatReceiveInviteAmount;
    /**
     * 已邀请有礼金额
     */
    @ApiModelProperty(value = "已邀请有礼金额")
    private BigDecimal receiveRebateInviteAmount;
    @ApiModelProperty(value = "最后一次领取佣金金额")
    private BigDecimal lastReceiveRebateAmount;
}
