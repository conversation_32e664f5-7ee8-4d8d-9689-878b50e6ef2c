package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.member.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@ApiModel("分页直属成员列表信息Req")
@Data
public class FindPageDirectMemberInfoReq extends CreateTimePageReq {

    private static final long serialVersionUID = 2531347536021441138L;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    @NotNull(message = "父级ID不能为空")
    private Long parentId;

    /**
     * 用户code
     */
    @ApiModelProperty("用户code")
    private Long userCode;

    @ApiModelProperty("用户ID")
    private Long userId;
}
