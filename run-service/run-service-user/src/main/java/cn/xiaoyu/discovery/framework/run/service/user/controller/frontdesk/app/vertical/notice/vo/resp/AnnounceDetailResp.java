package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.AnnounceLevelEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公告通知详细Resp
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("公告通知详细Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnnounceDetailResp extends IdResp {

    private static final long serialVersionUID = 3003107484161282563L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    private NoticeTypeEum noticeTypeEum;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 公告等级
     */
    @ApiModelProperty(value = "公告等级")
    private AnnounceLevelEum announceLevelEum;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    /**
     * 跳转链接
     */
    @ApiModelProperty(value = "跳转链接")
    private String linkUrl;

    /**
     * 定时发送时间
     */
    @ApiModelProperty(value = "定时发送时间")
    @JacksonZoneId
    private Long sendTime;

    /**
     * 到期时间
     */
    @ApiModelProperty(value = "到期时间")
    @JacksonZoneId
    private Long expireTime;

    /**
     * 是否显示小红点
     */
    @ApiModelProperty(value = "是否显示小红点")
    private Boolean redPoint;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", hidden = true)
    private Integer sort;
}
