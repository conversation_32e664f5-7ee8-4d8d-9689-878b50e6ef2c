package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "VIP等级对照信息")
@Data
public class UserLevelResp implements IResp {

    private static final long serialVersionUID = 8041062506017873062L;
    /**
     * 等级枚举
     * <p>
     * 枚举 {@link LevelEum}
     */
    @ApiModelProperty(value = "等级枚举")
    private LevelEum levelEum;
    /**
     * 等级图标地址
     */
    @ApiModelProperty(value = "等级图标地址")
    @ReplaceDomainName
    private String levelImgUrl;
    /**
     * 等级附件地址
     */
    @ApiModelProperty(value = "等级附件地址-达标图标")
    @ReplaceDomainName
    private String levelAttachmentUrl;
    /**
     * 等级附件地址
     */
    @ApiModelProperty(value = "等级附件地址-未达标图标")
    @ReplaceDomainName
    private String levelAttachmentUrl2;
    /**
     * 是否默认
     */
    @ApiModelProperty(value = "是否默认")
    private Boolean defaulter;
    /**
     * 打码量升级起
     */
    @ApiModelProperty(value = "打码量升级起")
    private BigDecimal betUpLevelAmountStart;
    /**
     * 打码量升级止
     */
    @ApiModelProperty(value = "打码量升级止")
    private BigDecimal betUpLevelAmountEnd;
    /**
     * 充值量升级起
     */
    @ApiModelProperty(value = "充值量升级起")
    private BigDecimal rechargeUpLevelAmountStart;
    /**
     * 充值量升级止
     */
    @ApiModelProperty(value = "充值量升级止")
    private BigDecimal rechargeUpLevelAmountEnd;
    /**
     * 保留不降级的打码量
     */
    @ApiModelProperty(value = "保留不降级的打码量")
    private BigDecimal betDownLevelAmount;
    /**
     * 保留不降级的充值量
     */
    @ApiModelProperty(value = "保留不降级的充值量")
    private BigDecimal rechargeDownLevelAmount;
    /**
     * 是否可领晋级礼金
     */
    @cn.zhxu.bs.bean.DbField(value = "promotioner")
    @ApiModelProperty(value = "是否可领晋级礼金")
    private Boolean promotioner;
    /**
     * 可领晋级礼金金额
     */
    @ApiModelProperty(value = "可领晋级礼金金额")
    private BigDecimal promotionAmount;
    /**
     * 可领晋级礼金金额的需求打码倍数
     */
    @ApiModelProperty(value = "可领晋级礼金金额的需求打码倍数")
    private BigDecimal promotionBetMultiple;
    /**
     * 是否可领日礼金
     */
    @ApiModelProperty(value = "是否可领日礼金")
    private Boolean dayer;
    /**
     * 可领日礼金金额
     */
    @ApiModelProperty(value = "可领日礼金金额")
    private BigDecimal dayAmount;
    /**
     * 可领日礼金金额的需求打码倍数
     */
    @ApiModelProperty(value = "可领日礼金金额的需求打码倍数")
    private BigDecimal dayBetMultiple;
    /**
     * 是否可领周礼金
     */
    @ApiModelProperty(value = "是否可领周礼金")
    private Boolean weeker;
    /**
     * 可领周礼金金额
     */
    @ApiModelProperty(value = "可领周礼金金额")
    private BigDecimal weekAmount;
    /**
     * 可领周礼金金额的需求打码倍数
     */
    @ApiModelProperty(value = "可领周礼金金额的需求打码倍数")
    private BigDecimal weekBetMultiple;
    @ApiModelProperty(name = "周礼金可领取时间", notes = "1-7：周一到周日")
    private Integer weekPickupTime;
    /**
     * 是否可领月礼金
     */
    @ApiModelProperty(value = "是否可领月礼金")
    private Boolean monther;
    /**
     * 可领月礼金金额
     */
    @ApiModelProperty(value = "可领月礼金金额")
    private BigDecimal monthAmount;
    /**
     * 可领月礼金金额的需求打码倍数
     */
    @ApiModelProperty(value = "可领月礼金金额的需求打码倍数")
    private BigDecimal monthBetMultiple;
    @ApiModelProperty(name = "月礼金可领取时间", notes = "1-31;一个月的开始到结束日期")
    private Integer monthPickupTime;
    /**
     * 需要完成的任务数-用于刷单类系统
     */
    @ApiModelProperty(value = "需要完成的任务数-用于刷单类系统")
    private Integer taskCount;
    /**
     * 是否开启每日首充赠送
     */
    @ApiModelProperty(value = "是否开启每日首充赠送")
    private Boolean dayFirstRechargeDiscounter;
    /**
     * 每日首充赠送比例
     */
    @ApiModelProperty(value = "每日首充赠送比例")
    @JacksonAmountFmtIgnore
    private BigDecimal dayFirstRechargeDiscountRate;
    /**
     * 每日首充赠送金额的需求打码倍数
     */
    @ApiModelProperty(value = "每日首充赠送金额的需求打码倍数")
    private BigDecimal dayFirstRechargeDiscountBetMultiple;
    /**
     * 是否开启每笔充值赠送
     */
    @ApiModelProperty(value = "是否开启每笔充值赠送")
    private Boolean dayEveryRechargeDiscounter;
    /**
     * 每笔充值赠送比例
     */
    @ApiModelProperty(value = "每笔充值赠送比例")
    @JacksonAmountFmtIgnore
    private BigDecimal dayEveryRechargeDiscountRate;
    /**
     * 每笔充值赠送送金额的需求打码倍数
     */
    @ApiModelProperty(value = " 每笔充值赠送送金额的需求打码倍数")
    private BigDecimal dayEveryRechargeDiscountBetMultiple;
    /**
     * 是否开启提现手续费
     */
    @ApiModelProperty(value = "是否开启提现手续费")
    private Boolean withdrawFeeer;
    /**
     * 提现手续费比例
     */
    @ApiModelProperty(value = "提现手续费比例")
    @JacksonAmountFmtIgnore
    private BigDecimal withdrawFeeRate;
    /**
     * 提现单笔最大限制 空或者0不限制
     */
    @ApiModelProperty(value = "提现单笔最大限制 空或者0不限制")
    private BigDecimal withdrawSingleMaxLimit;
    /**
     * 每日提现汇总最大限制 空或者0不限制
     */
    @ApiModelProperty(value = "每日提现汇总最大限制 空或者0不限制")
    private BigDecimal dayWithdrawTotalMaxLimit;

    // 20230419新增
    /**
     * 每日首充赠送上限
     */
    @ApiModelProperty(value = "每日首充赠送上限")
    private BigDecimal dayFirstRechargeDiscountMax;
    /**
     * 每笔充值赠送上限
     */
    @ApiModelProperty(value = "每笔充值赠送上限")
    private BigDecimal dayEveryRechargeDiscountMax;

    // 20231123 以下字段用于VIP中心显示升级进度条等信息
    @ApiModelProperty("累计打码金额")
    private BigDecimal totalBetAmount = BigDecimal.ZERO;

    @ApiModelProperty("累计充值金额")
    private BigDecimal totalRechargeAmount = BigDecimal.ZERO;

    @ApiModelProperty("升级到下一等级还需要充值量")
    private BigDecimal nextLevelNeedRechargeAmount;
    @ApiModelProperty("升级到下一等级需要打码量")
    private BigDecimal nextLevelNeedBetAmount;

    @ApiModelProperty("升级到下一等级需要的充值百分比")
    @JacksonAmountFmtIgnore
    private BigDecimal nextLevelRechargePercent;
    @ApiModelProperty("升级到下一等级需要的投注百分比")
    @JacksonAmountFmtIgnore
    private BigDecimal nextLevelBetPercent;

}
