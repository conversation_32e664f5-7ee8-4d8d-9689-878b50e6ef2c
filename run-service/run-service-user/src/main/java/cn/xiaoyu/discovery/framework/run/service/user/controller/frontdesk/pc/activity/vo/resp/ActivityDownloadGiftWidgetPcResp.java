package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ActivityDownloadGiftWidgetPcResp implements IResp {
    private static final long serialVersionUID = -2217634233187400492L;

    @ApiModelProperty(value = "奖励列表")
    private List<ActivityDownloadGiftAmountItemResp> list;

    @ApiModelProperty(value = "应用下载名")
    private String appName;

    @ApiModelProperty(value = "h5弹窗图片地址")
    @ReplaceDomainName
    private String h5ImgUrl;

    @ApiModelProperty(value = "app弹窗图片地址")
    @ReplaceDomainName
    private String appImgUrl;

    @ApiModelProperty(value = "总奖励金额")
    private BigDecimal totalAmount;

}
