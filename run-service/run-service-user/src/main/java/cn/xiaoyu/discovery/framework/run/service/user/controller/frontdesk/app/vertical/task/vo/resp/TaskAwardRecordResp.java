package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("任务奖励领取记录请求Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskAwardRecordResp implements IResp {
    private static final long serialVersionUID = 5451642660777383296L;

    @ApiModelProperty(value = "任务标题")
    private String title;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal receiveAmount;

    @ApiModelProperty(value = "领取时间")
    @JacksonZoneId
    private Long createTime;

}
