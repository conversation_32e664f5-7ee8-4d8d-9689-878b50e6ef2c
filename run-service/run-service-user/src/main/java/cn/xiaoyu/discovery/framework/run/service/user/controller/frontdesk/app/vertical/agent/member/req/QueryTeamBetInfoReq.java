package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询团队打码信息Req
 * <AUTHOR>
 * @since 2024/2/27
 */
@ApiModel("查询团队打码信息Req")
@Data
public class QueryTeamBetInfoReq extends CreateTimePageReq {

    private static final long serialVersionUID = -8570184396603135084L;

    @ApiModelProperty("是否直属")
    private Boolean directer;
}
