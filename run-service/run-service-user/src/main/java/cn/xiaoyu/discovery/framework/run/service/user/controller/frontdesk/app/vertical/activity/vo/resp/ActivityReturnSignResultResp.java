package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("回归奖励领取Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityReturnSignResultResp implements IResp {
    private static final long serialVersionUID = 3179734045189850614L;

    @ApiModelProperty("签到金额")
    private BigDecimal signAmount;

    @ApiModelProperty("周期内签到天数")
    private Integer signDays;
}
