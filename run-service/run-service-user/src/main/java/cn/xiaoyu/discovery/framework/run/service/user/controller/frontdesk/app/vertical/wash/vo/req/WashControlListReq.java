package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/9
 */
@ApiModel("洗码对照表Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WashControlListReq implements IReq {

    @ApiModelProperty(value = "等级")
    private LevelEum levelEum;
}
