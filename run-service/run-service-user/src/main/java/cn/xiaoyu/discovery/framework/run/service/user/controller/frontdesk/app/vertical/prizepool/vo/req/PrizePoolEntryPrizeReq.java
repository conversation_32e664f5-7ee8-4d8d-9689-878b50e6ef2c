package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("用户投币请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizePoolEntryPrizeReq extends IdReq {
	private static final long serialVersionUID = -5233111558028133584L;

	@ApiModelProperty(value = "用户输入的金币",required = true)
	@NotNull(message = UserCodeException.COIN_NOT_EXITS_I18N_CODE)
	private BigDecimal entryCoin;

}
