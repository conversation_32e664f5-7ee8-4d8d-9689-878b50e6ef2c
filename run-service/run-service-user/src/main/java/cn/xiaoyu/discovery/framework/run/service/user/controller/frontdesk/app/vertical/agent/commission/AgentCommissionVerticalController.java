package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.collection.CollectionUtil;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.req.AgentRebateTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.req.AgentReceiveRebateRateReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.req.FindPageSettlementRecordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp.AgentCommissionInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp.AgentRebateGameRateResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp.AgentSettlementRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.agent.AgentRebateBookConvert;
import cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentRebateControlSettingsEntity;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.commission.AgentCommissionService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.domain.AgentPopularizeService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateBookService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateControlSettingsService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateGameSettingService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/15 17:28
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@RequestMapping(AgentCommissionVerticalController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.推广-佣金信息", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + AgentCommissionVerticalController.MODULE_PREFIX + "模块-竖版app-推广-佣金信息")
public class AgentCommissionVerticalController implements IModulePrefix {
    private final AgentPopularizeService agentPopularizeService;
    private final AgentRebateGameSettingService agentRebateGameSettingService;
    private final AgentRebateControlSettingsService agentRebateControlSettingsService;
    private final AgentRebateBookService agentRebateBookService;

    private final AgentCommissionService agentCommissionService;

    @PostMapping("/getAgentCommission")
    @ApiOperation("1.获取佣金相关信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    public R<AgentCommissionInfoResp> getAgentCommission() {
        return R.success(AgentRebateBookConvert.INSTANCE
            .convertEntityToResp(agentRebateBookService.findByUserId(FrontdeskUserInfoUtils.getUserId())));
    }

    @PostMapping("/receiveCommissions")
    @ApiOperation("2.领取佣金")
    @ApiOperationSupport(order = 2)
    @Encrypt
    public R<Boolean> receiveCommissions() {
        return R.success(agentPopularizeService.receiveCommissions(FrontdeskUserInfoUtils.getUserId()));
    }

    @PostMapping("/getRebateRateType")
    @ApiOperation("3.获取返佣比例查询类型")
    @ApiOperationSupport(order = 3)
    @Encrypt
    @Decrypt
    public R<PageResp<List>> getRebateRateType(@RequestBody AgentRebateTypeReq req) {
        List list = agentRebateGameSettingService.getRebateRateType(req);
        if (CollectionUtil.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return R.success(new PageResp<>(list, (long)list.size()));
    }

    @PostMapping("/receiveRebateRate")
    @ApiOperation("4.获取返佣比例信息")
    @ApiOperationSupport(order = 4)
    @Decrypt
    @Encrypt
    public R<PageResp<AgentRebateGameRateResp>> receiveRebateRate(@RequestBody AgentReceiveRebateRateReq req) {
        List<AgentRebateGameRateResp> list = agentRebateGameSettingService.receiveRebateRate(req);
        Long total = agentRebateGameSettingService.getRebateRateTotal(req);
        return R.success(new PageResp<>(list, total));
    }

    @PostMapping("/getRebateControl")
    @ApiOperation("5.获取返佣比例设置")
    @ApiOperationSupport(order = 5)
    @Encrypt
    public R<AgentRebateControlSettingsEntity> getRebateControl() {
        List<AgentRebateControlSettingsEntity> list = agentRebateControlSettingsService.findAll();
        if (CollectionUtil.isNotEmpty(list)) {
            return R.success(list.get(0));
        }
        return R.success(null);
    }

    @PostMapping("/getSettlementRecords")
    @ApiOperation("6.获取佣金结算记录")
    @ApiOperationSupport(order = 6)
    @Encrypt
    @Decrypt
    public R<PageResp<AgentSettlementRecordResp>>
        getSettlementRecords(@Valid @RequestBody FindPageSettlementRecordReq req) {
        return R.success(agentCommissionService.getSettlementRecords(req));
    }

}
