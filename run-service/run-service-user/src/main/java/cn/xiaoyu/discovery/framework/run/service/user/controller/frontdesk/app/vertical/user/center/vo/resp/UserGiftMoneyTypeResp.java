package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.GiftMoneyStatusEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/2 11:48
 */
@ApiModel("礼金信息Resp")
@Data
public class UserGiftMoneyTypeResp implements IResp {
    private static final long serialVersionUID = 4745652546404925256L;

    // @ApiModelProperty("礼金类型")
    // private GiftMoneyTypeEum giftMoneyTypeEum;

    @ApiModelProperty("可领取的金额")
    private BigDecimal availableGiftMoney = BigDecimal.ZERO;

    @ApiModelProperty("礼金状态")
    private GiftMoneyStatusEum giftMoneyStatusEum = GiftMoneyStatusEum.NOT_AVAILABLE;

    @ApiModelProperty("领取时间 -- 当礼金类型为周、月时才会有值，周为1-7，月为1-31")
    private Integer pickupTime;

}
