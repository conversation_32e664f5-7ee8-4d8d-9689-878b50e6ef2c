package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/2/6 11:42
 */
@ApiModel("用户登录信息Resp")
@Data
public class UserLoginResp implements IResp {

    private static final long serialVersionUID = -4459758218738266895L;

    @ApiModelProperty("用户相关基础信息")
    private UserInfoResp userInfoResp;

    @ApiModelProperty(value = "token名称", required = true)
    public String tokenName;

    @ApiModelProperty(value = "token", required = true)
    private String tokenValue;
}
