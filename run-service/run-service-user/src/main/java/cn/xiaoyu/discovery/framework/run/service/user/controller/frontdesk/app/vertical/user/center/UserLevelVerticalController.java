package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/1 21:21
 */

import java.util.List;
import java.util.Objects;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.exception.BizException;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req.PickUpGiftMoneyRecordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req.PickUpGiftMoneyReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req.UserGiftMoneyLevelReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp.UserGiftMoneyResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp.UserLevelResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp.UserUpLevelResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.UserLevelConvert;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserDataTotalEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserLevelEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserLevelPickUpMoneyRecordEntity;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.center.UserCenterLevelService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.center.UserGiftMoneyService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.level.UserLevelPickUpMoneyRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.level.UserLevelService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.total.UserDataTotalService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 2)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(UserLevelVerticalController.MODULE_PREFIX)
@Api(value = "2.用户VIP信息", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + UserLevelVerticalController.MODULE_PREFIX + "模块-竖版app用户VIP操作")
public class UserLevelVerticalController implements IModulePrefix {

    private final UserLevelService userLevelService;
    private final UserService userService;
    private final UserGiftMoneyService userGiftMoneyService;
    private final UserDataTotalService userDataTotalService;
    private final UserCenterLevelService userCenterLevelService;

    private final UserLevelPickUpMoneyRecordService userLevelPickUpMoneyRecordService;

    @PostMapping("/getUserLevelInfo")
    @ApiOperation("1.获取用户VIP信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    public R<UserLevelResp> getUserLevelInfo() {
        return R.success(userCenterLevelService.getUserLevelInfo(FrontdeskUserInfoUtils.getUserId()));
    }

    @PostMapping("/getAllUserLevelInfos")
    @ApiOperation("2.获取用户VIP等级对照表")
    @ApiOperationSupport(order = 2)
    @Encrypt
    @SaIgnore
    public R<List<UserLevelResp>> getAllUserLevelInfos() {
        return R.success(userCenterLevelService.getAllUserLevelInfos(FrontdeskUserInfoUtils.getUserId()));
    }

    @PostMapping("/getUserCanPickUpGiftMoney")
    @ApiOperation("3.获取用户可领取礼金的VIP级别列表")
    @ApiOperationSupport(order = 3)
    @Encrypt
    public R<List<UserLevelResp>> getUserCanPickUpGiftMoney() {
        UserEntity userEntity = userService.findById(FrontdeskUserInfoUtils.getUserId());

        // 获取用户当前VIP级别及以下列表
        UserLevelEntity userLevelEntity = userLevelService.findById(userEntity.getLevelId());
        List<UserLevelEntity> userLevelEntityList = userLevelService.findAllByCache();
        List<UserLevelEntity> respEntityList = Lists.newArrayList();
        if (Objects.nonNull(userLevelEntityList)) {
            userLevelEntityList.forEach(item -> {
                if (item.getLevelEum().getValue() < userLevelEntity.getLevelEum().getValue()) {
                    respEntityList.add(item);
                }
            });
        }
        respEntityList.add(userLevelEntity);

        return R.success(UserLevelConvert.INSTANCE.convertEntityToResp(respEntityList));
    }

    @PostMapping("/getUserGiftMoneyInfoByLevel")
    @ApiOperation("4.根据VIP级别获取用户可领的礼金")
    @ApiOperationSupport(order = 4)
    @Encrypt
    @Decrypt
    public R<UserGiftMoneyResp> getUserGiftMoneyInfoByLevel(@RequestBody UserGiftMoneyLevelReq req) {

        return R.success(
            userGiftMoneyService.getUserCanPickUpGiftMoney(FrontdeskUserInfoUtils.getUserId(), req.getLevelEum()));
    }

    @PostMapping("/pickUpGiftMoney")
    @ApiOperation("5.领取VIP礼金")
    @ApiOperationSupport(order = 4)
    @Encrypt
    @Decrypt
    public R<Boolean> pickUpGiftMoney(@RequestBody PickUpGiftMoneyReq req) {
        if (Objects.isNull(req.getLevelEum())) {
            throw new BizException(UserCodeException.USER_LEVEL_NULL);
        }
        if (Objects.isNull(req.getGiftMoneyTypeEum())) {
            throw new BizException(UserCodeException.GIFT_MONEY_TYPE_EUM_IS_NULL);
        }
        UserLevelEntity userLevelEntity = userLevelService.findByLevelEum(req.getLevelEum());
        return R.success(userGiftMoneyService.pickUpGiftMoney(FrontdeskUserInfoUtils.getUserId(), userLevelEntity,
            req.getGiftMoneyTypeEum()));
    }

    @PostMapping("/getUserUpLevelInfo")
    @ApiOperation("6.用户升级需要的打码和充值信息")
    @ApiOperationSupport(order = 4)
    @Encrypt
    public R<UserUpLevelResp> getUserUpLevelInfo() {
        UserUpLevelResp upLevelResp = new UserUpLevelResp();
        // 查询用户累计充值
        // R<RechargeTotalResp> rechargeTotalResp = rechargeApi.getTotalByUser(FrontdeskUserInfoUtils.getUserId(),
        // null);
        // if (rechargeTotalResp.isSuccess()) {
        // upLevelResp.setTotalRechargeAmount(Optional.ofNullable(rechargeTotalResp.getData())
        // .map(RechargeTotalResp::getTotalAmount).orElse(BigDecimal.ZERO));
        // }
        //
        // // 查询用户累计投注金额
        // R<BigDecimal> userTotalBetAmountResp =
        // gameOrderInfoApi.getUserTotalBetAmount(FrontdeskUserInfoUtils.getUserId(),
        // Long.valueOf(PlatformIdContextHolder.getPlatformId()));
        // if (userTotalBetAmountResp.isSuccess()) {
        // upLevelResp.setTotalBetAmount(userTotalBetAmountResp.getData());
        // }

        UserDataTotalEntity userDataTotalEntity = userDataTotalService.getByUserId(FrontdeskUserInfoUtils.getUserId());
        // 用户VIP中心，使用的是当前充值和打码-当前充值和打码降级时会变动
        upLevelResp.setTotalBetAmount(userDataTotalEntity.getNowBetAmount());
        upLevelResp.setTotalRechargeAmount(userDataTotalEntity.getNowRechargeAmount());
        return R.success(upLevelResp);
    }

    @PostMapping("/getPickUpGiftMoneyRecord")
    @ApiOperation("7.获取领取VIP礼金记录")
    @ApiOperationSupport(order = 7)
    @Encrypt
    @Decrypt
    public R<PageResp<UserLevelPickUpMoneyRecordEntity>>
        getPickUpGiftMoneyRecord(@RequestBody PickUpGiftMoneyRecordReq req) {
        UserEntity userEntity = userService.findById(FrontdeskUserInfoUtils.getUserId());
        req.setUserId(userEntity.getId());
        req.setStartCreateTime(userEntity.getCreateTime());
        req.setEndCreateTime(System.currentTimeMillis());
        PageResp<UserLevelPickUpMoneyRecordEntity> pageByEntity =
            userLevelPickUpMoneyRecordService.findPageByEntity(req);
        return R.success(pageByEntity);
    }

}
