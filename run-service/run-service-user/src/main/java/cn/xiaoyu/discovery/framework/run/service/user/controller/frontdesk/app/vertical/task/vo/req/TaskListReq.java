package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskAgingTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TaskListReq implements IReq {
    private static final long serialVersionUID = -4204558017298176620L;

    @ApiModelProperty(name = "任务时效类型", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private TaskAgingTypeEum taskAgingTypeEum;

    @ApiModelProperty(name = "任务类型", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private TaskTypeEum taskTypeEum;

}
