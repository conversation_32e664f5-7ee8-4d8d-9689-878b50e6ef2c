package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@ApiModel("直属成员列表信息Resp")
@Data
@AllArgsConstructor
public class AgentDirectMemberInfoResp implements IResp {

    private static final long serialVersionUID = -7085315032651970446L;

    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("用户编码")
    private Long userCode;
    /**
     * 前台显示账户名
     */
    @ApiModelProperty("用户名-账号名称")
    private String username;

    /**
     * 上级编码
     */
    @ApiModelProperty("上级编码")
    private Long parentId;

    /**
     * 充值金额
     */
    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmount;

    /**
     * 投注金额
     */
    @ApiModelProperty("投注金额")
    private BigDecimal betAmount;

    /**
     * 注册时间
     */
    @ApiModelProperty("注册时间")
    @JacksonZoneId
    private Long createTime;
}
