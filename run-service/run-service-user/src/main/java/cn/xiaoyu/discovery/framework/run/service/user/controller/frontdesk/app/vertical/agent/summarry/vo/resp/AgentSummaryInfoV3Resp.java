package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/27 10:25
 * @Description
 */
@Data
public class AgentSummaryInfoV3Resp implements IResp {
    private static final long serialVersionUID = -2242307152709436892L;
    private AgentNewSummaryInfoResp today;
    private AgentNewSummaryInfoResp total;
}
