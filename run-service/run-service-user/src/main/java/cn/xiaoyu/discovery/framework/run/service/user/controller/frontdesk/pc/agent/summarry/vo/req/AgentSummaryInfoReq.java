package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.summarry.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 14:16
 */
@ApiModel("代理成员汇总信息Req")
@Data
public class AgentSummaryInfoReq extends CreateTimeReq {
    private static final long serialVersionUID = 7522299721779596210L;
    @ApiModelProperty("用户ID")
    private Long userId;
}
