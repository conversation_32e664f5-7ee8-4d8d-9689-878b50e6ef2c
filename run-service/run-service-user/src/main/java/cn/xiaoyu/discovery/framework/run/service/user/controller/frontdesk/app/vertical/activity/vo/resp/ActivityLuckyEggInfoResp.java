package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("幸运金蛋界面信息Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityLuckyEggInfoResp implements IResp {
    private static final long serialVersionUID = -951011908873058802L;

    @ApiModelProperty(value = "当日已充值")
    private BigDecimal todayRechargeAmount;

    @ApiModelProperty(value = "当日已投注")
    private BigDecimal todayBetAmount;

    @ApiModelProperty(value = "金蛋规则描述")
    private String goldRulesDesc;

    @ApiModelProperty(value = "银蛋规则描述")
    private String silverRulesDesc;

    @ApiModelProperty(value = "铜蛋规则描述")
    private String copperRulesDesc;
}
