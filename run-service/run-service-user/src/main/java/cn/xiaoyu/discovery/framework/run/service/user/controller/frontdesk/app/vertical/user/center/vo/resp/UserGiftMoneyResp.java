package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/2 11:54
 */
@ApiModel("用户礼金领取礼金Resp")
@Data
public class UserGiftMoneyResp implements IResp {
    private static final long serialVersionUID = -2276529262582370123L;

    @ApiModelProperty("晋级礼金")
    private UserGiftMoneyTypeResp promotion;
    @ApiModelProperty("日礼金")
    private UserGiftMoneyTypeResp day;
    @ApiModelProperty("周礼金")
    private UserGiftMoneyTypeResp week;
    @ApiModelProperty("月礼金")
    private UserGiftMoneyTypeResp month;

	public UserGiftMoneyResp() {
		promotion = new UserGiftMoneyTypeResp();
		day = new UserGiftMoneyTypeResp();
		week = new UserGiftMoneyTypeResp();
		month = new UserGiftMoneyTypeResp();
	}

}
