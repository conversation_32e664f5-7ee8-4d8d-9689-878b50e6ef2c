package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @since 2023/8/10 22:04
 */
@Data
@ApiModel("三方登录授权信息Resp")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuthorizeInfoResp implements IResp {
    private static final long serialVersionUID = -2603919176672980123L;

    @ApiModelProperty("state-用户于获取用户登录信息")
    private String state;
    @ApiModelProperty("授权请求地址")
    private String authorizeUrl;
}
