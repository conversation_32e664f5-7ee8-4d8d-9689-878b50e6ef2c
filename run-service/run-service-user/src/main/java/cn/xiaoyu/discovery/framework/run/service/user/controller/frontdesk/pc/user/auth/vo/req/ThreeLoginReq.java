package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.threelogin.ThreeLoginOpenType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/8/15 21:26
 */
@Data
@ApiModel("获取授权地址Req")
public class ThreeLoginReq implements IReq {
    private static final long serialVersionUID = -7120314422306613300L;

    @ApiModelProperty(value = "三方登录类型【google,facebook】", required = true)
    @NotNull
    private String source;
    @ApiModelProperty("登录跳转类型【弹框或者整站跳转】")
    private ThreeLoginOpenType threeLoginOpenType;
    @ApiModelProperty("跳转地址【登录成功后跳转该地址，并将在链接地址后拼接accessToken参数】")
    private String redirectUrl;
}
