package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.event;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.event.vo.req.UserEventReq;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.UserEventConvert;
import cn.xiaoyu.discovery.framework.run.service.user.service.event.UserEventService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/12/21 17:59
 * @Description
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(UserEventReportController.MODULE_PREFIX)
@Api(value = "1.用户",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + UserEventReportController.MODULE_PREFIX + "模块-竖版事件上报")
public class UserEventReportController implements IModulePrefix {
    private final UserEventService userEventService;

    @PostMapping("/saveUserEvent")
    @ApiOperation("1.保存用户事件")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @SaIgnore
    public R<Boolean> saveUserEvent(@RequestBody UserEventReq req) {
        return R.success(userEventService.createEntity(UserEventConvert.INSTANCE.convertReqToEntity(req)));
    }
}
