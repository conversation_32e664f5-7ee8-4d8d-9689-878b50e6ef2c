package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.agent.PromoteShareTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("分享信息Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentShareInfoResp implements IResp {
    private static final long serialVersionUID = 7454311859890499139L;

    @ApiModelProperty(value = "类型枚举")
    private PromoteShareTypeEum promoteShareTypeEum;

    @ApiModelProperty(value = "分享枚举类型对应的图片地址")
    @ReplaceDomainName
    private String typeImgUrl;

    @ApiModelProperty(value = "分享打开三方app的链接地址")
    private String shareOpenAppUrl;

    @ApiModelProperty(value = "分享的推广url")
    private String agentUrl;

    @ApiModelProperty(value = "分享描述-分享的文本信息")
    private String shareDesc;

}
