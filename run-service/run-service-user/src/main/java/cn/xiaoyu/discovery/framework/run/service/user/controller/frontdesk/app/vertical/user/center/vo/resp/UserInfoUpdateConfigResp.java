package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 用户信息修改开关控制Resp
 * <AUTHOR>
 * @date 2023/8/22
 */
@ApiModel("获取用户信息修改开关控制Resp")
@Data
public class UserInfoUpdateConfigResp implements IResp {

    private static final long serialVersionUID = -8631748441129488255L;

    /** ========================用户中心绑定 是否开启绑定邮箱和手机号start======================== */
    /**
     * 修改用户信息是否开启绑定邮箱-默认开启
     */
    @ApiModelProperty(value = "用户中心是否开启绑定邮箱-默认关闭")
    private Boolean bindingEmail = false;

    /**
     * 用户中心是否开启绑定手机号-默认开启
     */
    @ApiModelProperty(value = "用户中心是否开启绑定手机号-默认关闭")
    private Boolean bindingPhone = false;

    /** ========================用户中心绑定 是否开启绑定邮箱和手机号end======================== */
}
