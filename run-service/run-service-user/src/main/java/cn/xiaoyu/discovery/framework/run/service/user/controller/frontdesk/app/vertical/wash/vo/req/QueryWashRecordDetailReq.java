package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/15
 */
@Data
public class QueryWashRecordDetailReq extends CreateTimeReq {

    private static final long serialVersionUID = 1475257348725568013L;
    @NotNull
    @ApiModelProperty(value = "洗码批次ID")
    private Long batchId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private String field;
}
