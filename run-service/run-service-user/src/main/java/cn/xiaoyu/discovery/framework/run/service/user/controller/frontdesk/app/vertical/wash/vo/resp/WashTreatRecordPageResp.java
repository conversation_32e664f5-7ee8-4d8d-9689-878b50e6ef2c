package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;
import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 洗码列表
 *
 * <AUTHOR>
 */
@ApiModel("洗码列表及首页洗码统计Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WashTreatRecordPageResp implements IResp {
    private static final long serialVersionUID = 4687096574106336804L;

    @ApiModelProperty(value = "待洗码列表")
    private List<WashTreatWashRecordResp> list;

    @ApiModelProperty(value = "总洗码金额")
    private BigDecimal washTotalAmount;

    @ApiModelProperty(value = "总投注金额")
    private BigDecimal betTotalAmount;

    @ApiModelProperty(value = "本次洗码总投注额")
    private BigDecimal betAmount;

    @ApiModelProperty(value = "本次洗码总额")
    private BigDecimal washAmount;

    @ApiModelProperty(value = "最后一次洗码时间")
    @JacksonZoneId
    private Long lastWashTime;

}
