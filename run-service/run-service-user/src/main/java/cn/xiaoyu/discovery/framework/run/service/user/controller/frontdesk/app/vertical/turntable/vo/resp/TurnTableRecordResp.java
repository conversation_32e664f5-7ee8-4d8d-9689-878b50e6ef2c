package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @since 2024/2/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TurnTableRecordResp implements IResp {

    private static final long serialVersionUID = -2934447926631002693L;

    /**
     * 中奖用户编码
     */
    @ApiModelProperty(value = "中奖用户编码")
    private Long userCode;

    /**
     * 中奖用户名
     */
    @ApiModelProperty(value = "中奖用户名")
    private String username;

    /**
     * 中奖金额
     */
    @ApiModelProperty(value = "中奖金额")
    private BigDecimal lotteryAmount;
}