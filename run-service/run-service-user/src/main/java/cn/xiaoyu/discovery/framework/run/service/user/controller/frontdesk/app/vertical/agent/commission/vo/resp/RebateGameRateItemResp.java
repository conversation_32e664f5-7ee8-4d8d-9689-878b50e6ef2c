package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 前端-游戏返佣比例ItemResp
 * @date 2024/3/1
 */
@ApiModel("游戏返佣比例ItemResp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RebateGameRateItemResp implements IResp {
    private static final long serialVersionUID = 2249916014307765820L;

    @ApiModelProperty(value = "有效投注起")
    private BigDecimal betAmountStart;

    @ApiModelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;

}
