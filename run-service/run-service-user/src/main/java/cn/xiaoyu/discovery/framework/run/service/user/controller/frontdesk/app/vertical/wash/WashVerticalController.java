package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash;

import java.util.List;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req.QueryWashRecordDetailReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req.QueryWashRecordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req.QueryWashTotalReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req.WashControlListReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req.WashControlReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashBatchPageResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashControlResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashTotalResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashTreatRecordPageResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashTreatWashRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashUserTotalResp;
import cn.xiaoyu.discovery.framework.run.service.user.entity.wash.WashControlSettingsEntity;
import cn.xiaoyu.discovery.framework.run.service.user.service.wash.WashVerticalService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(WashVerticalController.MODULE_PREFIX)
@Api(value = "1.洗码",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + WashVerticalController.MODULE_PREFIX + "模块-竖版app洗码操作")
public class WashVerticalController implements IModulePrefix {

    private final WashVerticalService washVerticalService;

    @PostMapping("/getWashSetting")
    @ApiOperation("1.获取洗码配置")
    @SaIgnore
    @ApiOperationSupport(order = 1)
    @Encrypt
    public R<WashControlSettingsEntity> getWashSetting() {
        return R.success(washVerticalService.getWashSetting());
    }

    @PostMapping("/getWashInfo")
    @ApiOperation("2.获取洗码首页信息")
    @ApiOperationSupport(order = 2)
    @Encrypt
    @Decrypt
    public R<WashTreatRecordPageResp> getWashInfo(@RequestBody(required = false) WashControlReq req) {
        return R.success(washVerticalService.queryWashTreatRecord(req));
    }

    @PostMapping("/getWashDetail")
    @ApiOperation("3.洗码详情")
    @ApiOperationSupport(order = 3)
    @Encrypt
    @Decrypt
    public R<List<WashTreatWashRecordResp>> getWashDetail(@RequestBody WashControlReq req) {
        return R.success(washVerticalService.queryWashTreatDetail(req));
    }

    @PostMapping("/executeWash")
    @ApiOperation("4.手动洗码")
    @ApiOperationSupport(order = 4)
    @Encrypt
    @Decrypt
    public R<WashResp> executeWash(@RequestBody(required = false) WashControlReq req) {
        return R.success(washVerticalService.executeWash(req));
    }

    @PostMapping("/findPageWashBatch")
    @ApiOperation("5.历史记录")
    @ApiOperationSupport(order = 5)
    @Encrypt
    @Decrypt
    public R<PageResp<WashBatchPageResp>> findPageWashBatch(@RequestBody @Valid QueryWashRecordReq req) {
        return R.success(washVerticalService.queryWashBatchPage(req));
    }

    @PostMapping("/findPageWashRecordInfos")
    @ApiOperation("6.历史记录详情")
    @ApiOperationSupport(order = 6)
    @Encrypt
    @Decrypt
    public R<List<WashRecordResp>> findPageWashRecordInfos(@RequestBody @Valid QueryWashRecordDetailReq req) {
        return R.success(washVerticalService.queryPageWashRecordInfos(req));
    }

    @PostMapping("/queryUserWashTotal")
    @ApiOperation("7.会员洗码汇总")
    @ApiOperationSupport(order = 7)
    @Encrypt
    public R<WashTotalResp> queryUserWashTotal() {
        return R.success(washVerticalService.queryUserWashTotal());
    }

    @PostMapping("/queryWashControlList")
    @ApiOperation("8.洗码对照表")
    @ApiOperationSupport(order = 8)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<List<WashControlResp>> queryWashControlList(@RequestBody WashControlListReq req) {
        return R.success(washVerticalService.queryWashControlList(req));
    }

    @PostMapping("/queryUserWashRecordTotal")
    @ApiOperation("9.会员洗码记录汇总")
    @ApiOperationSupport(order = 9)
    @Encrypt
    @Decrypt
    public R<WashUserTotalResp> queryUserWashRecordTotal(@RequestBody QueryWashTotalReq req) {
        return R.success(washVerticalService.queryUserWashTotal(req));
    }

}
