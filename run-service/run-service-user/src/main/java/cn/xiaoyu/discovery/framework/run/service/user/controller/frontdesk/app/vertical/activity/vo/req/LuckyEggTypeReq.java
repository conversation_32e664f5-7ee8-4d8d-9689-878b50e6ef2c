package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.EggAwardTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/5/25
 */

@ApiModel("金蛋开始请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyEggTypeReq implements IReq {
    private static final long serialVersionUID = -6219306573189238478L;

    @ApiModelProperty(name = "金蛋类型", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private EggAwardTypeEum eggAwardTypeEum;
}
