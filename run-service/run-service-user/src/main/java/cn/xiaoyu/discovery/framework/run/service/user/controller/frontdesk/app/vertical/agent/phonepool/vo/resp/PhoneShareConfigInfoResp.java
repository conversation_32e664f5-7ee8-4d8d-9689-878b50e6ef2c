package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.phonepool.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.agent.PhoneShareTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 前端-号码分享控制Resp
 * @date 2024/3/6
 */
@ApiModel("号码分享控制Resp")
@Data
public class PhoneShareConfigInfoResp implements IResp {

    private static final long serialVersionUID = -479174634395881619L;

    /**
     * 类型枚举
     */
    @ApiModelProperty(value = "分享类型枚举")
    private PhoneShareTypeEum phoneShareTypeEum;

    /**
     * icon图片地址
     */
    @ApiModelProperty(value = "icon图片地址")
    private String typeImgUrl;

    /**
     * 是否开启
     */
    @ApiModelProperty(value = "开启or关闭")
    private Boolean whetherOpen;

    /**
     * 分享描述
     */
    @ApiModelProperty(value = "分享描述")
    private String shareDesc;

    /**
     * 是否群发（默认单发）
     */
    @ApiModelProperty(value = "是否群发（默认单发）默认:false")
    private Boolean whetherBatchSend;

}
