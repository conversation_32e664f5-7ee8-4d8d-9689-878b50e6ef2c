package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.member;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.req.AgentMemberInfoReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.resp.AgentDirectMemberInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.resp.QueryTeamBetInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateTeamService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/15 22:23
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@PcController
@RestController
@ApiSort(value = 1)
@RequestMapping(AgentMemberPCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.推广-直属成员", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + AgentMemberPCController.MODULE_PREFIX + "模块-PC-推广-直属成员")
public class AgentMemberPCController implements IModulePrefix {

    private final AgentRebateTeamService teamService;

    @PostMapping("/getAgentMemberInfos")
    @ApiOperation("1.获取推广-直属成员信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @Decrypt
    public R<PageResp<AgentDirectMemberInfoResp>> getAgentMemberInfos(@RequestBody AgentMemberInfoReq req) {
        return R.success(teamService.getDirectMemberPageInfo(req));
    }

    @PostMapping("/queryTeamBetInfo")
    @ApiOperation("2.获取团队打码佣金信息")
    @ApiOperationSupport(order = 2)
    @Encrypt
    @Decrypt
    public R<PageResp<QueryTeamBetInfoResp>> queryTeamBetInfo(@RequestBody CreateTimePageReq req) {
        return R.success(teamService.queryTeamBetInfo(req));
    }
}
