package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.phonepool.vo.resp;

import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 前端-号码池Resp
 * @date 2024/3/6
 */
@ApiModel("号码池Resp")
@Data
public class PhonePoolInfoResp implements IResp {

    private static final long serialVersionUID = -5586667005443590301L;

    /**
     * 号码分享控制集合
     */
    @ApiModelProperty(value = "号码分享控制集合")
    private List<PhoneShareConfigInfoResp> shareConfigList;

    /**
     * 号码池集合
     */
    @ApiModelProperty(value = "号码池集合(当“shareConfigList” 为空时，这个也是空的)")
    private List<String> phonePoolList;

}
