package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("用户忘记密码邮箱验证码校验Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserForgetEmailVerifyReq implements IReq {
    private static final long serialVersionUID = -4891160592537805699L;

    @ApiModelProperty(value = "邮箱", required = true)
    @NotNull(message = UserCodeException.EMAIL_IS_NULL_I18N_CODE)
    private String email;

    @ApiModelProperty(value = "邮箱验证码", required = true)
    @NotNull(message = UserCodeException.EMAIL_CODE_IS_NULL_I18N_CODE)
    private String emailCode;
}
