package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.event.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.event.EventTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/12/21 18:00
 * @Description
 */
@Data
@ApiModel("用户事件上报Req")
public class UserEventReq implements IReq {
    private static final long serialVersionUID = -7433562798521308968L;
    @ApiModelProperty(value = "用户ID", notes = "")
    private Long userId;
    /** 用户编码 */
    @ApiModelProperty(value = "用户编码", notes = "")
    private Long userCode;
    /** 用户名 */
    @ApiModelProperty(value = "用户名", notes = "")
    private String username;
    /** app打包序号 */
    @ApiModelProperty(value = "app打包序号", notes = "")
    private Long serialNumber;
    /** 事件类型 */
    @ApiModelProperty(value = "事件类型", notes = "")
    private EventTypeEum eventTypeEum;

    /** 像素类型 */
    @ApiModelProperty(value = "像素类型", notes = "")
    private String pixelType;

    @ApiModelProperty(value = "像素ID", notes = "fbid")
    private String pixelId;
}
