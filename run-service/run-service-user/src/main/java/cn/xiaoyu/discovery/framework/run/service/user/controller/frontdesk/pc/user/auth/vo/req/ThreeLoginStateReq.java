package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description
 * <AUTHOR>
 * @date 2023/9/26
 */
@Data
@ApiModel("根据state获取登录信息")
public class ThreeLoginStateReq implements IReq {

    private static final long serialVersionUID = 8172974870992636015L;

    @ApiModelProperty("state状态")
    private String state;
}
