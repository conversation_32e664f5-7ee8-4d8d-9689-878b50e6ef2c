package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.label;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.label.vo.resp.UserLabelResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.label.LabelConvert;
import cn.xiaoyu.discovery.framework.run.service.user.service.label.UserLabelService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 前端-用户标签信息
 * @date 2024/1/22
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@RequestMapping(LabelVerticalController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.用户标签",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + LabelVerticalController.MODULE_PREFIX + "模块-用户标签")
public class LabelVerticalController implements IModulePrefix {
    private final UserLabelService userLabelService;

    @PostMapping("/getUserLabel")
    @ApiOperation("1.获取用户标签信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    public R<List<UserLabelResp>> getUserLabel() {
        return R.success(LabelConvert.INSTANCE
            .convertUserLabelRespList(this.userLabelService.getUserLabelInfo(FrontdeskUserInfoUtils.getUserId())));
    }
}
