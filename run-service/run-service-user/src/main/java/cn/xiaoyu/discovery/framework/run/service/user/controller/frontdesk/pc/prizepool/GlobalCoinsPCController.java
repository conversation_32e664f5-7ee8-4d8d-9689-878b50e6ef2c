package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.prizepool;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.system.utils.AssertUtil;
import cn.xiaoyu.discovery.framework.run.service.system.utils.CommonUtil;
import cn.xiaoyu.discovery.framework.run.service.user.api.user.vo.resp.GlobalCoinExchangeBalanceResp;
import cn.xiaoyu.discovery.framework.run.service.user.constant.PrizePoolConstant;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.prizepool.vo.resp.GlobalCoinRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req.FindUserGlobalCoinRecordPageReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req.GlobalCoinsExchangeBalanceReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp.GlobalCoinsExchangeControlResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.coin.GlobalCoinExchangeControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.coin.GlobalCoinRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.coin.GlobalCoinService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@RequestMapping(GlobalCoinsPCController.COINS_MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.奖池-全球币", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + GlobalCoinsPCController.COINS_MODULE_PREFIX + "模块-pc-奖池-全球币")
public class GlobalCoinsPCController implements IModulePrefix {

    private final GlobalCoinService globalCoinService;
    private final GlobalCoinExchangeControlService globalCoinExchangeControlService;
    private final GlobalCoinRecordService globalCoinRecordService;

    @PostMapping("/getGlobalCoinsExchangeInfo")
    @ApiOperation("1.获取全球币兑换控制信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @SaIgnore
    public R<GlobalCoinsExchangeControlResp> getGlobalCoinsExchangeInfo() {
        GlobalCoinsExchangeControlResp control = globalCoinExchangeControlService.getExchangeControl();
        if (control != null && StringUtils.isNotBlank(control.getOriginExchangeRatio())) {
            String[] split = control.getOriginExchangeRatio().split(PrizePoolConstant.COLON);
            control.setGlobalCurrencyRatio(split[0]);
            control.setDefaultCurrencyRatio(split[1]);
        }
        return R.success(control);
    }

    @PostMapping("/exchangeBalance")
    @ApiOperation("2.全球币兑换--提交")
    @ApiOperationSupport(order = 2)
    @Encrypt
    @Decrypt
    public R<Boolean> submitExchangeBalance(@RequestBody GlobalCoinsExchangeBalanceReq req) {
        if (globalCoinExchangeControlService.validatedExchangeLimit(req.getExchangeNumber())) {
            GlobalCoinExchangeBalanceResp resp = globalCoinService.submitExchangeBalance(
                FrontdeskUserInfoUtils.getUserId(), req.getExchangeNumber(), req.getGameSubTypeEum());
            // 发送财务账变消息
            globalCoinService.sendUserToPayFinanceGlobalCoinsExchangeMessage(resp);
        }
        return R.success(true);
    }

    @PostMapping("/findGlobalCoinChangeRecord")
    @ApiOperation("3.奖池币改变记录分页列表")
    @ApiOperationSupport(order = 3)
    @Encrypt
    @Decrypt
    public R<PageResp<GlobalCoinRecordResp>>
        findGlobalCoinChangeRecord(@RequestBody FindUserGlobalCoinRecordPageReq req) {
        AssertUtil.assertTrue(
            CommonUtil.longIsNotEmpty(req.getStartCreateTime()) && CommonUtil.longIsNotEmpty(req.getEndCreateTime()),
            CommonCodeException.TIME_IS_NULL);
        return R.success(globalCoinRecordService.findGlobalCoinChangeRecord(req));
    }

    @SaIgnore
    @PostMapping("/globalCoinExchangeSwitch")
    @ApiOperation("4.奖池币/全球币便捷兑换开关")
    @ApiOperationSupport(order = 4)
    @Encrypt
    public R<Boolean> globalCoinExchangeSwitch() {
        return R.success(globalCoinExchangeControlService.globalCoinExchangeSwitch());
    }
}
