package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp;

import java.math.BigDecimal;
import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.BookRecordCategoryEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("全球币兑换控制resp")
public class GlobalCoinsExchangeControlResp implements IResp {

    private static final long serialVersionUID = -6729938832599318720L;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "币种图标")
    private String currencyIcon;

    @ApiModelProperty(value = "兑换比例")
    private String originExchangeRatio;
    private Double exchangeRatio = 0D;
    // 前端要求后端分割比例后返回 globalCurrencyRatio 、defaultCurrencyRatio
    @ApiModelProperty(value = "后台配置的原始比例数值")
    private String globalCurrencyRatio;

    @ApiModelProperty(value = "后台配置的原始比例数值")
    private String defaultCurrencyRatio;

    @ApiModelProperty(value = "当日剩余兑换额度")
    private BigDecimal todayExchangeLimit = BigDecimal.ZERO;

    @ApiModelProperty(value = "打码倍数")
    private BigDecimal betMultiple;

    @ApiModelProperty(value = "兑换福利规则")
    private List<ExchangeBounsRule> exchangeBonusRules;

    @ApiModelProperty(value = "全球币兑换类型")
    private BookRecordCategoryEum bookRecordCategoryEum;

    @ApiModelProperty(value = "有效天数, 当到账类型为[TO_DISCOUNT_WALLET]的设置选项，非必填（默认：0）")
    private Integer effectiveDays;

    @ApiModelProperty(value = "兑换总额度", hidden = true)
    private BigDecimal exchangeLimit;
}
