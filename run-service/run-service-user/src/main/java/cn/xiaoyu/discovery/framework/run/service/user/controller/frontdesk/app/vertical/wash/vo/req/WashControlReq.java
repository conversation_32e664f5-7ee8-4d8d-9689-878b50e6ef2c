package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.eum.StatusEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("洗码信息Req")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WashControlReq implements IReq {

    private static final long serialVersionUID = 4346251925892447279L;
    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;

    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;

    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;

    @ApiModelProperty(value = "等级")
    private LevelEum levelEum;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private StatusEum statusEum;

    @ApiModelProperty(hidden = true)
    private String field;

    @ApiModelProperty(hidden = true)
    private String orderBy;

    @ApiModelProperty("创建开始时间")
    private Long startCreateTime;
    @ApiModelProperty("创建结束时间")
    private Long endCreateTime;

    @ApiModelProperty(hidden = true)
    private String settingType;

    @ApiModelProperty(hidden = true)
    private Integer subTypeEumValue;

}
