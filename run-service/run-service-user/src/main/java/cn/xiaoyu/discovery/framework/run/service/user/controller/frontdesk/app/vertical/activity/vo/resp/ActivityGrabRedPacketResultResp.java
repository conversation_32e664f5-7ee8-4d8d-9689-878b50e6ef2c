package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.BookRecordCategoryEum;
import cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityRedPacketPickUpRecordEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("抢红包Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityGrabRedPacketResultResp implements IResp {
    private static final long serialVersionUID = -4710621371017474143L;
    @ApiModelProperty(value = "抢到的金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "失败原因", hidden = true)
    private String failureMsg;

    @ApiModelProperty(value = "code", hidden = true)
    private Integer code;

    /**
     * 事务外面发消息需要用到这个对象和下面几个属性
     */
    @ApiModelProperty(hidden = true)
    private ActivityRedPacketPickUpRecordEntity pickUpRecord;

    @ApiModelProperty(value = "到账类型", hidden = true)
    private BookRecordCategoryEum bookRecordCategoryEum;

    @ApiModelProperty(value = "有效天数", hidden = true)
    private Integer effectiveDays;

}
