package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.UserUnderDirectlyEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 14:16
 */
@ApiModel("代理汇总信息Req")
@Data
public class AgentSummaryInfoReq extends CreateTimeReq {

    private static final long serialVersionUID = 1517673952731838012L;
    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "用户类型-直属、非直属", notes = "如果为空，则查询所有用户")
    private UserUnderDirectlyEum userUnderDirectlyEum;

}
