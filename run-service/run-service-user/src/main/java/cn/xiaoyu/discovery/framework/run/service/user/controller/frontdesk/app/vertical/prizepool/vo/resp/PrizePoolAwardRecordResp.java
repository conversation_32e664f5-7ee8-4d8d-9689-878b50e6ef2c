package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrizePoolAwardRecordResp implements IResp {
    private static final long serialVersionUID = 4106935575285268997L;

    @ApiModelProperty(value = "奖池金额")
    private BigDecimal prizePoolAmount;

    @ApiModelProperty(value = "奖池投币开始时间")
    @JacksonZoneId
    private Long putInStartTime;

    @ApiModelProperty(value = "奖池投币结束时间")
    @JacksonZoneId
    private Long putInEndTime;

    @ApiModelProperty(value = "中奖记录明细")
    private List<PrizePoolAwardRecordItem> list;
}
