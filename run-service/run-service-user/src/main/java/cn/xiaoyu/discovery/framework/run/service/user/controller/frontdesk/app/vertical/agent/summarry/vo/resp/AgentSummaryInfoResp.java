package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 10:01
 */
@Data
public class AgentSummaryInfoResp implements IResp {
    private static final long serialVersionUID = -2261173882445584493L;

    @ApiModelProperty("佣金")
    private BigDecimal totalCommission;
    @ApiModelProperty("邀请有礼-佣金")
    private BigDecimal InviteTotalCommission;
    @ApiModelProperty(value = "团队总人数=直属+非直属")
    private Long teamTotalCount;
    @ApiModelProperty(value = "直属总人数")
    private Long directlyUnderTotalCount;
    @ApiModelProperty(value = "非直属总人数")
    private Long otherTotalCount;
    @ApiModelProperty(value = "团队总业绩=直属+非直属")
    private BigDecimal teamTotalAchievement;
    @ApiModelProperty(value = "直属总业绩")
    private BigDecimal directlyUnderTotalAchievement;
    @ApiModelProperty(value = "非直属总业绩")
    private BigDecimal otherTotalAchievement;
}
