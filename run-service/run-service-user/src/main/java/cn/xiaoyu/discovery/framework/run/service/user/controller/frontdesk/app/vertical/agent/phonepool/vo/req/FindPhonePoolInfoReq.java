package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.phonepool.vo.req;

import org.apache.commons.lang3.math.NumberUtils;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.agent.PhoneDisplayRulesEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 查询号码池Req
 * @date 2024/3/6
 */
@ApiModel("查询号码池Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FindPhonePoolInfoReq implements IReq {

    private static final long serialVersionUID = -1400348505524811038L;

    /**
     * 返回类型，不传默认0(0返回所有，1分享开关，2号码池)
     */
    @ApiModelProperty(value = "返回类型，不传默认0(0返回所有，1分享开关，2号码池)")
    private Integer type = NumberUtils.INTEGER_ZERO;

    /**
     * 号码展示规则类型(type=0或2时必传)
     */
    @ApiModelProperty(value = "号码展示规则类型(type=0或2时必传)")
    private PhoneDisplayRulesEum displayRulesEum;
}
