package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.test;

import java.math.BigDecimal;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.BookChangeSubTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.BookChangeTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.InOrOutStatusEum;
import cn.xiaoyu.discovery.framework.run.service.pay.api.finance.FinanceBookApi;
import cn.xiaoyu.discovery.framework.run.service.pay.api.finance.vo.req.FindFinanceBookRecordReq;
import cn.xiaoyu.discovery.framework.run.service.user.api.activity.vo.resp.RechargeDiscountControlResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req.PrizePoolEntryPrizeReq;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityService;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.PrizePoolBizService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @since 2024/1/9
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@RequestMapping(TestPrizePoolController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.奖池测试",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + TestPrizePoolController.MODULE_PREFIX + "模块-奖池测试")
public class TestPrizePoolController implements IModulePrefix {

    private final PrizePoolBizService prizePoolBizService;

    @SaIgnore
    @PostMapping("/testEntryPrizePool/{platformId}/{userId}/{coin}/{periodId}")
    @ApiOperation("1.测试user服务参与奖池")
    @ApiOperationSupport(order = 1)
    public R<Boolean> testEntryPrizePool(@PathVariable Long platformId, @PathVariable Long userId,
        @PathVariable BigDecimal coin, @PathVariable Long periodId) {
        PlatformIdContextHolder.setPlatformId(String.valueOf(platformId));
        PrizePoolEntryPrizeReq req = new PrizePoolEntryPrizeReq(coin);
        req.setId(periodId);
        try {
            log.info("参与开始执行");
            prizePoolBizService.entryPrizePool(userId, platformId, req);
        } catch (Exception e) {
            log.error("参与执行错误{}", e.getMessage());
        }
        return R.success();
    }

    private final ActivityService activityService;

    @SaIgnore
    @PostMapping("/getRechargeDiscountControl/{platformId}")
    @ApiOperation("获取平台入款优惠控制信息")
    @ApiOperationSupport(order = 2)
    public R<RechargeDiscountControlResp> getRechargeDiscountControl(@PathVariable Long platformId) {
        PlatformIdContextHolder.setPlatformId(String.valueOf(platformId));
        return R.success(activityService.getRechargeDiscountControl());
    }

    // private final GlobalCoinService globalCoinService;
    // private final GlobalCoinExchangeControlService globalCoinExchangeControlService;

    // @SaIgnore
    // @PostMapping("/testExchangeBalance/{platformId}/{userId}/{exchangeNumber}")
    // @ApiOperation("2.测试user服务全球币兑换")
    // @ApiOperationSupport(order = 2)
    // public R<Boolean> testExchangeBalance(@PathVariable Long platformId, @PathVariable Long userId,
    // @PathVariable Long exchangeNumber) {
    // PlatformIdContextHolder.setPlatformId(String.valueOf(platformId));
    // GlobalCoinsExchangeBalanceReq req = new GlobalCoinsExchangeBalanceReq();
    // try {
    // log.info("兑换开始执行");
    // req.setExchangeNumber(exchangeNumber);
    // if (globalCoinExchangeControlService.validatedExchangeLimit(req.getExchangeNumber())) {
    // GlobalCoinExchangeBalanceResp resp =
    // globalCoinService.submitExchangeBalance(userId, req.getExchangeNumber(), req.getGameSubTypeEum());
    // // 发送财务账变消息
    // globalCoinService.sendUserToPayFinanceGlobalCoinsExchangeMessage(resp);
    // }
    // } catch (Exception e) {
    // log.error("兑换执行错误{}", e.getMessage());
    // }
    // return R.success();
    // }

    public final FinanceBookApi financeBookApi;

    @SaIgnore
    @PostMapping("/getTotalDiscountWalletAmount")
    @ApiOperation("优惠总金额")
    @ApiOperationSupport(order = 1)
    public R<BigDecimal> getTotalDiscountWalletAmount(@RequestParam("pid") Long pid,
        @RequestParam("beginTime") Long beginTime, @RequestParam("endTime") Long endTime) {
        PlatformIdContextHolder.setPlatformId(String.valueOf(pid));
        FindFinanceBookRecordReq recordReq = new FindFinanceBookRecordReq();
        recordReq.setBookChangeTypeEum(BookChangeTypeEum.PRIZE_POOL);
        recordReq.setBookChangeSubTypeEum(BookChangeSubTypeEum.GLOBAL_COINS_EXCHANGE);
        recordReq.setInOrOutStatusEum(InOrOutStatusEum.IN);
        recordReq.setStartCreateTime(beginTime);
        recordReq.setEndCreateTime(endTime);
        R<BigDecimal> totalDiscountWalletAmountR = financeBookApi.getTotalDiscountWalletAmount(recordReq, pid);
        System.out.println("优惠总金额" + totalDiscountWalletAmountR.getData());

        return R.success(totalDiscountWalletAmountR.getData());
    }

}
