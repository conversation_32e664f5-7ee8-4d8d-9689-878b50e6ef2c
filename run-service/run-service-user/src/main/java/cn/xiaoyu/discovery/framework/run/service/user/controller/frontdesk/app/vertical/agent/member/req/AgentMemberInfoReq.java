package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 14:16
 */
@ApiModel("获取推广-直属成员信息Req")
@Data
public class AgentMemberInfoReq extends CreateTimePageReq {

    private static final long serialVersionUID = 2578054000464839540L;

    @ApiModelProperty("成员-会员编码")
    private Long userCode;

    @ApiModelProperty("是否直属")
    private Boolean directer;
}
