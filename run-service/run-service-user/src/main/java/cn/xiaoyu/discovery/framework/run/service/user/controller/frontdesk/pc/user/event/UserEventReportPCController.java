package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.event;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.event.vo.req.UserEventExceptionReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.event.vo.req.UserEventReq;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.UserEventConvert;
import cn.xiaoyu.discovery.framework.run.service.user.service.event.UserEventExceptionLogService;
import cn.xiaoyu.discovery.framework.run.service.user.service.event.UserEventService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/12/21 17:59
 * @Description
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(UserEventReportPCController.MODULE_PREFIX)
@Api(value = "1.用户", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + UserEventReportPCController.MODULE_PREFIX + "模块-PC事件上报")
public class UserEventReportPCController implements IModulePrefix {
    private final UserEventService userEventService;
    private final UserEventExceptionLogService eventExceptionLogService;

    @PostMapping("/saveUserEvent")
    @ApiOperation("1.保存用户事件")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @Decrypt
    @SaIgnore
    public R<Boolean> saveUserEvent(@RequestBody UserEventReq req) {
        return R.success(userEventService.createEntity(UserEventConvert.INSTANCE.convertReqToEntity(req)));
    }

    @PostMapping("/saveEventException")
    @ApiOperation("2.保存用户事件异常")
    @ApiOperationSupport(order = 2)
    @Encrypt
    @Decrypt
    @SaIgnore
    public R<Boolean> saveEventException(@RequestBody(required = false) UserEventExceptionReq req) {
        return R.success(eventExceptionLogService.saveEventException(req));
    }
}
