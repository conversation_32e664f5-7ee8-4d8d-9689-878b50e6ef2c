package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 14:16
 */
@ApiModel("直属成员信息Req")
@Data
public class PrizePoolPeriodApplyReq extends CreateTimeReq {
    private static final long serialVersionUID = 6706857962162339334L;
    // @ApiModelProperty("用户ID")
    private Long userId;
}
