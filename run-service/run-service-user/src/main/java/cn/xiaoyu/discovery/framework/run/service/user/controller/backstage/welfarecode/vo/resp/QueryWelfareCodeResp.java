package cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.BookRecordCategoryEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.welfare.WelfareRechargeLimitTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询福利码Resp
 * <AUTHOR>
 * @since 2023/10/12
 */
@Data
@AllArgsConstructor
public class QueryWelfareCodeResp extends IdResp {

	private static final long serialVersionUID = 4083887787717701032L;

	/**
	 * 福利码
	 */
	@ApiModelProperty(value = "福利码")
	private String welfareCode;
	/**
	 * 描述文本(富文本)
	 */
	@ApiModelProperty(value = "描述文本(富文本)")
	private String description;
	/**
	 * 金额备注
	 */
	@ApiModelProperty(value = "金额备注")
	private String amountRemark;
	/**
	 * 生效时间
	 */
	@ApiModelProperty(value = "生效时间")
	@JacksonZoneId
	private Long startTime;
	/**
	 * 过期时间
	 */
	@ApiModelProperty(value = "过期时间")
	@JacksonZoneId
	private Long expireTime;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Long quantity;
	/**
	 * 充值金额限制类型
	 */
	@ApiModelProperty(value = "充值金额限制类型")
	private WelfareRechargeLimitTypeEum rechargeLimitTypeEum;
	/**
	 * 充值金额限制
	 */
	@ApiModelProperty(value = "充值金额限制")
	private BigDecimal rechargeLimit;
	/**
	 * 打码金额限制类型
	 */
	@ApiModelProperty(value = "打码金额限制类型")
	private WelfareRechargeLimitTypeEum betLimitTypeEum;
	/**
	 * 打码金额限制
	 */
	@ApiModelProperty(value = "打码金额限制")
	private BigDecimal betLimit;
	/**
	 * 打码倍数
	 */
	@ApiModelProperty(value = "打码倍数")
	private BigDecimal betMultiple;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@JacksonZoneId
	private Long createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	@JacksonZoneId
	private Long updateTime;
	/**
	 * 条件明细
	 */
	@ApiModelProperty("条件明细")
	private List<QueryWelfareCodeDetailResp> details;

	/**
	 * 到账类型
	 */
	@ApiModelProperty(value = "到账类型")
	private BookRecordCategoryEum bookRecordCategoryEum;

	/**
	 * 优惠钱包有效天数
	 */
	@ApiModelProperty(value = "优惠钱包有效天数")
	private Integer validDate;
}
