package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;

import org.apache.logging.log4j.util.Strings;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("洗码信息Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WashControlResp implements IResp {
    private static final long serialVersionUID = -6635812474745716146L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;

    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;

    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;

    @ApiModelProperty(value = "有效投注起")
    private BigDecimal betAmountStart;

    @ApiModelProperty(value = "有效投注止")
    private BigDecimal betAmountEnd;

    @ApiModelProperty(value = "洗码比例0.0001格式")
    @JacksonAmountFmtIgnore
    private BigDecimal washRate;

    @ApiModelProperty("创建时间")
    @JacksonZoneId
    private Long createTime;

    @ApiModelProperty(value = "等级名称", hidden = true)
    private String levelName;
    @ApiModelProperty(value = "等级")
    private LevelEum levelEum;

    @ApiModelProperty(hidden = true)
    private Integer subTypeValue;

    public String getLevelName() {
        return levelEum != null ? levelEum.getDefaultDesc() : Strings.EMPTY;
    }
}
