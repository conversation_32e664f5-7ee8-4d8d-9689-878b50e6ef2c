package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivityFirstRechargeResp implements IResp {
    private static final long serialVersionUID = -1500457994448341431L;

    @ApiModelProperty(value = "list")
    private List<ActivityFirstRechargeItemResp> list;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "规则")
    private String rules;

    @ApiModelProperty(value = "今日是否可领取")
    private Boolean todayCanDraw = false;
}
