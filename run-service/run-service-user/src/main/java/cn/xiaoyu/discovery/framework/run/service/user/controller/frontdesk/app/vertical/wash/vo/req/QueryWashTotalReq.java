package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
@ApiModel("洗码汇总查询Req")
@Data
public class QueryWashTotalReq extends CreateTimeReq {
    private static final long serialVersionUID = 643771520906926495L;

    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;

    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;

    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;

    @ApiModelProperty(hidden = true)
    private Long userId;
}
