package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.turntable.TurnTableDiscountTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.turntable.TurnTableStatusEum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024/2/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TurnTableInfoResp implements IResp {

    private static final long serialVersionUID = 5843630072713728541L;

    /**
     * 是否开启
     */
    @ApiModelProperty(value = "是否开启")
    private Boolean opener = Boolean.FALSE;

    /**
     * 是否首次
     */
    @ApiModelProperty(value = "是否首次")
    private Boolean firster = Boolean.TRUE;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private TurnTableStatusEum status = TurnTableStatusEum.NOT_SATISFIED;

    /**
     * 邀请可获得摇奖次数
     */
    @ApiModelProperty(value = "邀请可获得摇奖次数")
    private Integer inviteDrawCount = NumberUtils.INTEGER_ZERO;

    /**
     * 剩余摇奖次数
     */
    @ApiModelProperty(value = "剩余摇奖次数")
    private Integer drawCount = NumberUtils.INTEGER_ZERO;

    /**
     * 目标中奖金额
     */
    @ApiModelProperty(value = "目标中奖金额")
    private BigDecimal lotteryAmount = BigDecimal.ZERO;

    /**
     * 当前金额
     */
    @ApiModelProperty(value = "当前金额")
    private BigDecimal nowAmount = BigDecimal.ZERO;

    /**
     * 剩余金额
     */
    @ApiModelProperty(value = "剩余金额")
    private BigDecimal residualAmount = BigDecimal.ZERO;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private Long expireTime = NumberUtils.LONG_ZERO;

    /**
     * 奖项内容列表
     */
    @ApiModelProperty(value = "奖项内容列表")
    private List<TurnTableInfoProjectResp> projects = Lists.newArrayList();

    /**
     * 转盘赠送金额类型
     */
    @ApiModelProperty(value = "转盘赠送金额类型")
    private TurnTableDiscountTypeEum turnTableDiscountTypeEum = TurnTableDiscountTypeEum.TO_BALANCE;

    /**
     * 下级累计充值限制
     */
    @ApiModelProperty(value = "下级累计充值限制")
    private BigDecimal underRechargeLimit;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule = StringUtils.EMPTY;

    /**
     * 游客ID
     */
    @ApiModelProperty(value = "游客ID")
    private Long visitorId;

    /**
     * 游客编码
     */
    @ApiModelProperty(value = "游客编码")
    private Long visitorCode;
}