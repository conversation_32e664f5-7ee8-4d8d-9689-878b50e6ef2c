package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskShareEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("分享任务Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskShareCallBackReq implements IReq {
    private static final long serialVersionUID = 4976805476148020075L;

    @ApiModelProperty(name = "任务分享类型", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private TaskShareEum taskShareEum;

}
