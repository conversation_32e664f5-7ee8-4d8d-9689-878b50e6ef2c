package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.common.vo;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.pay.finance.BookRecordCategoryEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/27 15:06
 * @Description
 */
@Data
@ApiModel("到账类型Resp")
public class BookRecordCategoryResp implements IResp {
    private static final long serialVersionUID = -8326000867554740579L;
    @ApiModelProperty("佣金到账类型")
    private BookRecordCategoryEum commissionCategoryEum;
}
