package cn.xiaoyu.discovery.framework.run.service.user.properties;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import cn.xiaoyu.discovery.framework.run.common.eum.StatusEum;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.service.system.utils.AssertUtil;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.SmsMsgTypeEum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 短信通道
 *
 * <AUTHOR>
 * @date 2023/8/8
 */
@Getter
@Setter
@ToString
@RefreshScope
@ConfigurationProperties(prefix = "sms")
public class SmsProperties {
    private List<SmsChannel> channels;

    @Getter
    @Setter
    @ToString
    @RefreshScope
    public static class SmsChannel implements Serializable {
        private static final long serialVersionUID = 9064852728700391670L;

        /**
         * 通道名称
         */
        private String channelName;
        /**
         * 所属平台
         */
        private String platform;
        /**
         * 每人每天发送的总次数，当到达该次数时会提示【当日获取验证码次数已用完】-(0不限制)
         */
        private Long daysTotalCount = 0L;
        /**
         * 连续发送的时间内，-(单位秒 0不限制)。 举个栗子；用户在600秒之内发送了5次验证码，那么就要间隔720秒之后才能继续发送，那么
         * 就相当于continuousTime=600，continuousCount=5，continuousInterval=720
         */
        private Long continuousTime = 0L;
        /**
         * 连续发送的次数，当到达该次数时会提示【获取验证码次数频繁，限制获取时间{continuousInterval}秒】-(只有当continuousTime>0时才有效)
         * "获取验证码次数频繁，限制获取时间"+redis.ttl(key)+"秒"
         */
        private Long continuousCount;
        /**
         * 连续发送的次数之后，需要间隔多少秒才能继续发送--(单位秒 只有当continuousTime>0时才有效)
         */
        private Long continuousInterval;
        /**
         * 状态
         */
        private String channelStatus;
        /**
         * 语言
         */
        private String LanguageEum;
        /**
         * 请求地址
         */
        private String reqUrl;
        /**
         * 请求是否为get
         */
        private Boolean isGet;
        /**
         * app key
         */
        private String appKey;
        /**
         * app secret
         */
        private String appSecret;
        /**
         * app 应用代码
         */
        private String appCode;
        /**
         * 验证码模板
         */
        private String captchaTemplate;
        /**
         * 通知模板
         */
        private String noticeTemplate;
        /**
         * 短信类型:普通短信--默认（Common / 空 ）、营销短信(Marketing)
         */
        private String smsType;
    }

    /**
     * 根据短信发送类型获取平台短信渠道
     *
     * @param smsMsgTypeEum
     * @return
     */
    public SmsChannel getPlatformSmsChannel(SmsMsgTypeEum smsMsgTypeEum) {
        String platformId = PlatformIdContextHolder.getPlatformId();
        AssertUtil.assertStringNotBlank(platformId, "platformId is empty");
        List<SmsChannel> channelList = getChannels();
        AssertUtil.assertListNotEmpty(channelList, "channelList is null");
        if (SmsMsgTypeEum.USER_RECALL == smsMsgTypeEum) {
            // 用户召回 -- 营销类短信
            return channelList.stream().filter(o -> o.getPlatform().equals(platformId)
                && o.getChannelStatus().equals(StatusEum.ENABLE.name()) && "Marketing".equalsIgnoreCase(o.getSmsType()))
                .findFirst().orElse(null);
        }
        // 普通短信
        return channelList.stream()
            .filter(o -> o.getPlatform().equals(platformId) && o.getChannelStatus().equals(StatusEum.ENABLE.name())
                && (StringUtils.isBlank(o.getSmsType()) || "Common".equalsIgnoreCase(o.getSmsType())))
            .findFirst().orElse(null);
    }
}
