package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/15
 * @description
 **/
@Data
@ApiModel("兑换福利规则")
public class ExchangeBounsRule implements IResp {

    private static final long serialVersionUID = -8914576630172411069L;
    @ApiModelProperty("兑换金币区间--起")
    private Long start;

    @ApiModelProperty("兑换金币区间--止")
    private Long end;

    @ApiModelProperty("而外赠送百分比")
    private Integer percent;
}
