package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.AwardDrawRoleTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动记录请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRecordTypeReq extends PageReq {
    private static final long serialVersionUID = -8713310352943370951L;

    @ApiModelProperty(name = "活动记录查询类型", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private AwardDrawRoleTypeEum awardDrawRoleTypeEum;
}
