package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 洗码统计
 *
 * <AUTHOR>
 * @date 2023/4/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WashRecordResp implements IResp {
    private static final long serialVersionUID = 3262212056616027067L;
    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;
    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;
    @ApiModelProperty(value = "厂商icon地址")
    @ReplaceDomainName
    private String iconUrl;
    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;
    @ApiModelProperty(value = "洗码总投注")
    private BigDecimal washBetAmount;
    @ApiModelProperty(value = "比例")
    @JacksonAmountFmtIgnore
    private BigDecimal washRate;
    @ApiModelProperty(value = "洗码金额")
    private BigDecimal washAmount;

    @ApiModelProperty(hidden = true)
    private Integer subTypeValue;

}
