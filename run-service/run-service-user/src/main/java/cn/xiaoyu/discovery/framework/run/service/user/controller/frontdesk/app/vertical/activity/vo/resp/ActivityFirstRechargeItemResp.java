package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.AchieveStatusEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivityFirstRechargeItemResp implements IResp {
    private static final long serialVersionUID = -6341812931084523022L;

    @ApiModelProperty(value = "达标记录的id")
    private Long id;

    @ApiModelProperty(value = "第几天")
    private Integer sequence;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "状态")
    private AchieveStatusEum achieveStatusEum;

}
