package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("回归奖励配置Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityReturnAwardResp implements IResp {
    private static final long serialVersionUID = -4519032566959003147L;

    @ApiModelProperty("第一天签到金币最大值")
    private BigDecimal day1Amount;

    @ApiModelProperty("第二天签到金币最大值")
    private BigDecimal day2Amount;

    @ApiModelProperty("第三天签到金币最大值")
    private BigDecimal day3Amount;

    @ApiModelProperty("第四天签到金币最大值")
    private BigDecimal day4Amount;

    @ApiModelProperty("第五天签到金币最大值")
    private BigDecimal day5Amount;

    @ApiModelProperty("第六天签到金币最大值")
    private BigDecimal day6Amount;

    @ApiModelProperty("第七天签到金币最大值")
    private BigDecimal day7Amount;

    @ApiModelProperty("回归连续签到次数")
    private Integer signDays;

    @ApiModelProperty("今日是否签到（0：否，1：是）")
    private Integer todaySign;

}
