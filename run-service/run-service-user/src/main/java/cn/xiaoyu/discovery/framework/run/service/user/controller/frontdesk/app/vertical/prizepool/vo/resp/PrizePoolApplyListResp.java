package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 最近参与奖池用户信息Resp
 * <AUTHOR>
 * @since 2023/12/6
 */
@Data
public class PrizePoolApplyListResp implements IResp {

    private static final long serialVersionUID = 2014416826364529220L;

    @ApiModelProperty("用户名称")
    private String username;
    @ApiModelProperty("期数")
    private BigDecimal putInCoin;
}