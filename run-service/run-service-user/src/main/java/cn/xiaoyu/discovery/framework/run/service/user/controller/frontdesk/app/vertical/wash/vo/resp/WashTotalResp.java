package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@ApiModel("玩家洗码汇总Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WashTotalResp implements IResp {

    private static final long serialVersionUID = 1584895560876756376L;

    @ApiModelProperty(value = "洗码总金额")
    private BigDecimal washTotalAmount;

    @ApiModelProperty(value = "总投注额")
    private BigDecimal betTotalAmount;

    @ApiModelProperty(value = "最后一次洗码金额")
    private BigDecimal lastWashAmount;

    @ApiModelProperty(value = "最后一次洗码投注金额")
    private BigDecimal lastBetAmount;

    @ApiModelProperty(value = "最后一次洗码时间")
    @JacksonZoneId
    private Long lastWashTime;
}
