package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CountryLanguageCurrencyPhonePrefixZoneidEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.LoginTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/2/6 11:53
 */
@ApiModel("用户登录相关信息")
@Data
public class UserLoginReq implements IReq {

    private static final long serialVersionUID = -8984261570834932898L;
    @ApiModelProperty(value = "用户名 登录方式为用户名时必传")
    private String username;

    @ApiModelProperty(value = "邮箱 登录方式为邮箱时必传")
    private String email;

    @ApiModelProperty(value = "手机号前缀 配置手机号登录时必传")
    private String phonePrefix;

    @ApiModelProperty(value = "手机号码 登录方式为手机号时必传")
    private String phone;

    @ApiModelProperty(value = "设备id 登录方式为设备号时必传")
    private String deviceNo;

    @ApiModelProperty(value = "密码 除了设备号登录外必传")
    private String password;

    @ApiModelProperty(value = "登录方式", required = true)
    @NotNull(message = UserCodeException.LOGIN_TYPE_IS_NULL_I18N_CODE)
    private LoginTypeEum loginTypeEum;

    @ApiModelProperty(value = "图形验证码 图形验证码开启时，需要传递")
    private String captchaCode;

    @ApiModelProperty("Authorization 普通图形验证码开启需要回传")
    private String authorization;

    @ApiModelProperty(value = "邮箱验证码 配置邮箱登录时必传")
    private String emailCode;

    @ApiModelProperty(value = "手机验证码 配置手机号登录时必传")
    private String phoneCode;

    @ApiModelProperty(value = "登录的国家枚举")
    private CountryLanguageCurrencyPhonePrefixZoneidEum loginClcppzEum;

}
