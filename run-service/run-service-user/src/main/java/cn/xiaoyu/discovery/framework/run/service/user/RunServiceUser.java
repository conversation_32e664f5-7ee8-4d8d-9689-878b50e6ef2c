package cn.xiaoyu.discovery.framework.run.service.user;

import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.stream.annotation.EnableBinding;

import cn.xiaoyu.discovery.framework.run.common.annotation.EnableCommonService;
import cn.xiaoyu.discovery.framework.run.service.gateway.stream.output.MoreServiceSendUserWsMsgToGatewayOutput;
import cn.xiaoyu.discovery.framework.run.service.pay.stream.output.MoreServiceUpdateDiscountWalletToPayOutput;
import cn.xiaoyu.discovery.framework.run.service.pay.stream.output.MoreServiceUpdateUserBookToPayOutput;
import cn.xiaoyu.discovery.framework.run.service.pay.stream.output.UserRegToPayFinanceBookOutput;
import cn.xiaoyu.discovery.framework.run.service.system.stream.input.SystemClosePlatformToUserInput;
import cn.xiaoyu.discovery.framework.run.service.system.stream.input.SystemOpenPlatformToUserInput;
import cn.xiaoyu.discovery.framework.run.service.user.properties.SmsProperties;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.agentteam.MoreServiceToUserAgentTeamInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.bind.email.MoreServiceBindEmailToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.bind.phone.MoreServiceBindPhoneToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.bind.realname.MoreServiceRealNameToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.bind.withdrawaccount.PayBindWithdrawAccountToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.label.UserToUserLabelInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.login.UserLoginToUserActivityDownloadGiftInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.notice.MoreServiceSendUserNoticeMsgToUserInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pay.activity.PayToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pay.activity.PayToUserDownloadGiftActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pay.invitepolitely.PayToUserInvitePolitelyInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pay.task.PayToUserTaskInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pay.turntable.PayToUserTurnTableInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.phonepool.MoreServiceToUserPhonePoolInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.activeuser.GamePullOrderToUserActiveUserInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.activity.GamePullOrderToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.activity.GamePullOrderToUserDownloadGiftActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.commission.GamePullOrderToUserTreatCommissionInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.globalcoin.GamePullOrderToUserGlobalCoinInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.invitepolitely.GamePullOrderToUserInvitePolitelyInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.prizepool.GamePullOrderToUserPrizePoolInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.task.GamePullOrderToUserTaskInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.turntable.GamePullOrderToUserTurnTableInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.pullorder.wash.GamePullOrderToUserTreatWashInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.reg.UserRegToUserActivityInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.reg.UserRegToUserInvitePolitelyInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.reg.UserRegToUserTurnTableInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.input.updata.MoreServiceBetRechargeWithdrawCommissionToUserChangeDataInput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.agentteam.MoreServiceToUserAgentTeamOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.bind.email.MoreServiceBindEmailToUserActivityOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.bind.phone.MoreServiceBindPhoneToUserActivityOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.bind.realname.MoreServiceRealNameToUserActivityOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.label.UserToUserLabelOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.login.UserLoginToUserActivityDownloadGiftOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.notice.MoreServiceSendUserNoticeMsgToUserOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.phonepool.MoreServiceToUserPhonePoolOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.reg.UserRegToUserActivityOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.reg.UserRegToUserInvitePolitelyOutput;
import cn.xiaoyu.discovery.framework.run.service.user.stream.output.reg.UserRegToUserTurnTableOutput;

/**
 * 依赖nacos sentinel
 *
 * @formatter:off
 * jvm参数配置 --illegal-access=deny --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED -Djdk.attach.allowAttachSelf=true -Dcsp.sentinel.log.dir=/Users/<USER>/logs/ -Dspring.profiles.active=dev -Dconf.nacos.serverAddr=192.168.0.4:9948 -Dconf.sentinel.dashboard=192.168.0.4:7070
 * @formatter:on
 *
 * <AUTHOR>
 */
@EnableBinding({MoreServiceSendUserNoticeMsgToUserOutput.class, UserRegToUserActivityOutput.class,
    UserRegToUserInvitePolitelyOutput.class, MoreServiceUpdateUserBookToPayOutput.class,
    MoreServiceBindEmailToUserActivityOutput.class, MoreServiceBindPhoneToUserActivityOutput.class,
    UserRegToPayFinanceBookOutput.class, MoreServiceToUserAgentTeamInput.class,
    MoreServiceRealNameToUserActivityOutput.class, GamePullOrderToUserPrizePoolInput.class,
    GamePullOrderToUserGlobalCoinInput.class, GamePullOrderToUserActivityInput.class,
    GamePullOrderToUserInvitePolitelyInput.class, GamePullOrderToUserTaskInput.class,
    MoreServiceSendUserNoticeMsgToUserInput.class, MoreServiceRealNameToUserActivityInput.class,
    PayBindWithdrawAccountToUserActivityInput.class, MoreServiceBindPhoneToUserActivityInput.class,
    PayToUserTaskInput.class, PayToUserActivityInput.class, PayToUserInvitePolitelyInput.class,
    SystemClosePlatformToUserInput.class, SystemOpenPlatformToUserInput.class, UserRegToUserActivityInput.class,
    UserRegToUserInvitePolitelyInput.class, MoreServiceBindEmailToUserActivityInput.class,
    MoreServiceBetRechargeWithdrawCommissionToUserChangeDataInput.class, GamePullOrderToUserTreatWashInput.class,
    GamePullOrderToUserTreatCommissionInput.class, MoreServiceSendUserWsMsgToGatewayOutput.class,
    MoreServiceToUserAgentTeamOutput.class, GamePullOrderToUserActiveUserInput.class,
    UserLoginToUserActivityDownloadGiftOutput.class, UserLoginToUserActivityDownloadGiftInput.class,
    PayToUserDownloadGiftActivityInput.class, GamePullOrderToUserDownloadGiftActivityInput.class,
    UserRegToUserTurnTableOutput.class, UserRegToUserTurnTableInput.class, PayToUserTurnTableInput.class,
    MoreServiceUpdateDiscountWalletToPayOutput.class, GamePullOrderToUserTurnTableInput.class,
    MoreServiceToUserPhonePoolOutput.class, MoreServiceToUserPhonePoolInput.class, UserToUserLabelOutput.class,
    UserToUserLabelInput.class})

@EnableCommonService
@EnableConfigurationProperties({SmsProperties.class})
// @EnabledMethodTask
public class RunServiceUser {

    public static void main(String[] args) {
        new SpringApplicationBuilder(RunServiceUser.class).run(args);
    }

}
