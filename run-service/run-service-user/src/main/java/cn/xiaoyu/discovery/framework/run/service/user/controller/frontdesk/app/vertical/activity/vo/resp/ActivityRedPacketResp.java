package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.activity.RedPacketTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/5/12
 */

@ApiModel("红包Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRedPacketResp extends IdResp {
    private static final long serialVersionUID = 4638061703943711080L;

    @ApiModelProperty(value = "红包标题")
    private String title;

    @ApiModelProperty(value = "红包类型")
    private RedPacketTypeEum redType;

    @ApiModelProperty(value = "红包金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "红包数量")
    private Integer redNum;

    @ApiModelProperty(hidden = true)
    private String appointUser;

    @ApiModelProperty(value = "参与会员VIP等级（最低等级）", hidden = true)
    private LevelEum lowestLevel;

    @ApiModelProperty(value = "参与会员VIP等级（最高等级）", hidden = true)
    private LevelEum highestLevel;

    @ApiModelProperty(value = "参与会员层级(id)", hidden = true)
    private Long hierarchicalId;

    @ApiModelProperty(value = "红包持续显示时间(单位：分钟)", hidden = true)
    private Integer displayTime;

    @ApiModelProperty(value = "更新时间", hidden = true)
    @JacksonZoneId
    private Long updateTime;

}
