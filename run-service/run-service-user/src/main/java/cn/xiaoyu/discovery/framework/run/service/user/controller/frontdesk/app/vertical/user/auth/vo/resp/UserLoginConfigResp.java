package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/2/6 11:04
 */
@ApiModel("用户界面相关配置信息")
@Data
public class UserLoginConfigResp implements IResp {

    private static final long serialVersionUID = -6438552391126394332L;

    /**
     * 背景地址 可以是图片 可以是视频
     */
    @ApiModelProperty(value = "背景地址 可以是图片 可以是视频")
    @ReplaceDomainName
    private String backgroundAddress;

    /** ========================设备登录开关start======================== */
    /**
     * 是否开启设备号登录-默认关闭
     */
    @ApiModelProperty(value = "是否开启设备号登录-默认关闭")
    private Boolean enabledEquipmentLogin = false;
    /** ========================设备登录开关end======================== */

    /** ========================用户名登录开关start======================== */
    /**
     * 是否开启用户名登录-默认开启
     */
    @ApiModelProperty(value = "是否开启用户登录-默认开启")
    private Boolean enabledUsernameLogin = true;
    /**
     * 是否开启用户名登录普通验证码-默认开启
     */
    @ApiModelProperty(value = "是否开启用户名登录普通验证码-默认开启")
    private Boolean enabledUsernameBaseCaptchaLogin = true;
    /**
     * 是否开启用户名登录aj验证码-默认关闭
     */
    @ApiModelProperty(value = "是否开启用户名登录aj验证码-默认关闭")
    private Boolean enabledUsernameAjCaptchaLogin = false;
    /** ========================用户名登录开关end======================== */

    /** ========================手机号登录开关start======================== */
    /**
     * 是否开启手机登录-默认关闭
     */
    @ApiModelProperty(value = "是否开启手机登录-默认关闭")
    private Boolean enabledPhoneLogin = false;
    /**
     * 是否开启手机登录必填手机号验证码-前提必须开启短信验证码-开启手机登录-默认开启
     */
    @ApiModelProperty(value = "是否开启手机登录必填手机号验证码-前提必须开启短信验证码-开启手机登录-默认开启")
    private Boolean enabledPhoneMustPhoneCaptchaLogin = true;
    /**
     * 是否开启手机登录普通验证码-默认关闭
     */
    @ApiModelProperty(value = "是否开启手机登录普通验证码-默认关闭")
    private Boolean enabledPhoneBaseCaptchaLogin = false;
    /**
     * 是否开启手机登录aj验证码-默认关闭
     */
    @ApiModelProperty(value = "是否开启手机登录aj验证码-默认关闭")
    private Boolean enabledPhoneAjCaptchaLogin = false;
    /** ========================手机号登录开关end======================== */

    /** ========================邮箱登录开关start======================== */
    /**
     * 是否开启邮箱登录-默认关闭
     */
    @ApiModelProperty(value = "是否开启邮箱登录-默认关闭")
    private Boolean enabledEmailLogin = false;
    /**
     * 是否开启邮箱登录必填邮箱验证码-前提必须开启邮箱验证码-必须开启邮箱登录-默认开启
     */
    @ApiModelProperty(value = "是否开启邮箱登录必填邮箱验证码-前提必须开启邮箱验证码-必须开启邮箱登录-默认开启")
    private Boolean enabledEmailMustEmailCaptchaLogin = true;
    /**
     * 是否开启邮箱登录普通验证码-默认关闭
     */
    @ApiModelProperty(value = "是否开启邮箱登录普通验证码-默认关闭")
    private Boolean enabledEmailBaseCaptchaLogin = false;
    /**
     * 是否开启邮箱登录aj验证码-默认关闭
     */
    @ApiModelProperty(value = "是否开启邮箱登录aj验证码-默认关闭")
    private Boolean enabledEmailAjCaptchaLogin = false;
    /** ========================邮箱登录开关end======================== */

    @ApiModelProperty(value = "是否开启google快捷登录-默认关闭")
    private Boolean enabledGoogleLogin = false;
    @ApiModelProperty(value = "是否开启facebook快捷登录-默认关闭")
    private Boolean enabledFacebookLogin = false;

    /** ========================找回密码开关start======================== */
    /**
     * 是否开启手机号找回密码-默认关闭
     */
    @ApiModelProperty(value = "是否开启手机号找回密码-默认开启")
    private Boolean enabledPhoneRetrievalPassword = true;

    /**
     * 是否开启邮箱找回密码-默认关闭
     */
    @ApiModelProperty(value = "是否开启邮箱找回密码-默认开启")
    private Boolean enabledEmailRetrievalPassword = true;

    /** ========================找回密码开关end======================== */
}
