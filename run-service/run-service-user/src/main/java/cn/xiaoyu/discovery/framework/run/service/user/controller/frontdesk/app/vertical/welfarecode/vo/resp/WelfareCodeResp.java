package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.welfarecode.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @since 2023/10/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WelfareCodeResp implements IResp {

    private static final long serialVersionUID = 8681960592803924757L;

    @ApiModelProperty(value = "是否可用")
    private Boolean isUse = false;

    @ApiModelProperty(value = "金额备注")
    private String remark = StringUtils.EMPTY;

    @ApiModelProperty(value = "规则")
    private String rule = StringUtils.EMPTY;
}