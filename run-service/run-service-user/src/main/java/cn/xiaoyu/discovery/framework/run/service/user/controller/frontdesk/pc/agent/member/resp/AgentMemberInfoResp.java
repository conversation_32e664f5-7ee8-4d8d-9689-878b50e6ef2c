package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.member.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 14:17
 */
@Data
@ApiModel("直属成员信息Resp")
@Deprecated
public class AgentMemberInfoResp implements IResp {
    private static final long serialVersionUID = -8527159410050509257L;

    @ApiModelProperty("用户Id")
    private Long userId;
    @ApiModelProperty("用户名【账号】")
    private String username;
    @ApiModelProperty("充值金额")
    private BigDecimal totalRecharge;
    @ApiModelProperty("投注金额")
    private BigDecimal totalBetAmount;
    @ApiModelProperty("下级投注-直属下属投注金额")
    private BigDecimal directlyUnderTotalBetAmount;
    @ApiModelProperty("注册时间")
    @JacksonZoneId
    private Long registerTime;
}
