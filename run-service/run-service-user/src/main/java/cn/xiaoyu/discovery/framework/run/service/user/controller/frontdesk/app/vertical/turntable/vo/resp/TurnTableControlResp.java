package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024/2/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TurnTableControlResp implements IResp {

    private static final long serialVersionUID = 8954929430117449175L;

    /**
     * 是否开启
     */
    @ApiModelProperty(value = "是否开启")
    private Boolean opener = Boolean.FALSE;

    /**
     * 是否首次
     */
    @ApiModelProperty(value = "是否首次")
    private Boolean firster = Boolean.TRUE;

    /**
     * 目标中奖金额
     */
    @ApiModelProperty(value = "目标中奖金额")
    private BigDecimal lotteryAmount = BigDecimal.ZERO;

    /**
     * 奖项内容列表
     */
    @ApiModelProperty(value = "奖项内容列表")
    private List<TurnTableInfoProjectResp> projects = Lists.newArrayList();

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    @ReplaceDomainName
    private String icon = StringUtils.EMPTY;

    /**
     * 转盘弹窗顶部图标
     */
    @ApiModelProperty(value = "转盘弹窗顶部图标")
    @ReplaceDomainName
    private String topIcon = StringUtils.EMPTY;

    /**
     * 游客可玩
     */
    @ApiModelProperty(value = "游客可玩")
    private Boolean visitorCanPlay;
}