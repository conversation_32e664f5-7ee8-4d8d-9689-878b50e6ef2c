package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.event.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserEventExceptionReq implements IReq {

    private static final long serialVersionUID = -104857386112701946L;
    @ApiModelProperty(value = "事件类型")
    private String eventName;
    @ApiModelProperty(value = "事件类型")
    private String exceptionMessage;
}
