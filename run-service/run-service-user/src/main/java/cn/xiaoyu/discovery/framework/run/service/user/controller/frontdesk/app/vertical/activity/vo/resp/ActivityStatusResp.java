package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;
import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/5/31
 */
@ApiModel("活动状态Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityStatusResp implements IResp {
    private static final long serialVersionUID = -874552524470281302L;

    @ApiModelProperty("金蛋开关状态")
    private Boolean goldenEggEnabled = false;

    @ApiModelProperty("转盘开关状态")
    private Boolean turnPlateEnabled = false;

    @ApiModelProperty("签到开关状态")
    private Boolean signEnabled = false;

    /**
     * 默认开启，红包已经从活动里面抽离到 红包中心
     */
    @ApiModelProperty("红包开关状态")
    private Boolean redPacketEnabled = true;

    @ApiModelProperty("回归开关状态")
    private Boolean returnEnabled = false;

    @ApiModelProperty("洗码开关状态")
    private Boolean washEnabled = false;

    @ApiModelProperty(value = "下载有礼开关状态")
    private Boolean downloadGiftEnabled;

    @ApiModelProperty(value = "h5首页下载logo图")
    @ReplaceDomainName
    private String homeH5ImgUrl;

    @ApiModelProperty(value = "绑定活动开启状态数组")
    private List<ActivityStatusItemResp> list;

    @ApiModelProperty(value = "绑定活动开关")
    private Boolean bindActivityOpener = false;

    @ApiModelProperty(value = "绑定活动描述")
    private String bindActivityRule;

    @ApiModelProperty(value = "绑定活动总金额")
    private BigDecimal totalAwardAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "任务开关")
    private Boolean taskOpener = false;
}
