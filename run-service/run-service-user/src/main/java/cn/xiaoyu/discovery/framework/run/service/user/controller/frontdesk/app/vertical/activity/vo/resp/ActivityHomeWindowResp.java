package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivityHomeWindowResp extends IdResp {
    private static final long serialVersionUID = 3828884243326694864L;

    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "活动首页弹窗图地址")
    private String homeWindowPicture;

    @ApiModelProperty(value = "活动文本 html")
    private String text;

    @ApiModelProperty(value = "开始时间")
    @JacksonZoneId
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    @JacksonZoneId
    private Long endTime;
}
