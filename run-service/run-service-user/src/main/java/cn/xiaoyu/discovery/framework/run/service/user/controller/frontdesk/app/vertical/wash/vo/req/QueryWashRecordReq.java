package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/10
 */
@ApiModel("洗码记录分页查询Req")
@Data
public class QueryWashRecordReq extends CreateTimePageReq {

    @ApiModelProperty(value = "洗码批次ID")
    private Long batchId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    Integer field;
}
