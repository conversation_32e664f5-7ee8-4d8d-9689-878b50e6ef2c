package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;
import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.service.user.dto.activity.ActivityApplyParamDto;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动详情Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDetailResp extends IdResp {
    private static final long serialVersionUID = -7856999658868335146L;

    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "h5/app活动封面")
    @ReplaceDomainName
    private String h5Picture;

    @ApiModelProperty(value = "pc活动封面")
    @ReplaceDomainName
    private String pcPicture;

    @ApiModelProperty(value = "活动文本 html")
    private String text;

    @ApiModelProperty(value = "是否需要报名")
    private Boolean needer;

    @ApiModelProperty(value = "该活动是否已申请")
    private Boolean isApply;

    @ApiModelProperty(value = "活动奖励金额")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "活动申请参数")
    private List<ActivityApplyParamDto> applyParamList;
}
