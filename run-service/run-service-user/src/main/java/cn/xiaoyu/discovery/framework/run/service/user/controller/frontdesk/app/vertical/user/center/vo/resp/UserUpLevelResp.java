package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/12 17:55
 */
@ApiModel("用户升级信息Resp")
@Data
public class UserUpLevelResp {
    @ApiModelProperty("累计打码金额")
    private BigDecimal totalBetAmount = BigDecimal.ZERO;

    @ApiModelProperty("累计充值金额")
    private BigDecimal totalRechargeAmount = BigDecimal.ZERO;
}
