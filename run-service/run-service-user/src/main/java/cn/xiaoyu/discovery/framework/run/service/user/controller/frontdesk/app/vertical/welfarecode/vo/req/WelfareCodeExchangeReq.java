package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.welfarecode.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("福利码兑换Req")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WelfareCodeExchangeReq implements IReq {

    private static final long serialVersionUID = -2718623078525418217L;

    @ApiModelProperty(value = "福利码")
    private String welfareCode;
}
