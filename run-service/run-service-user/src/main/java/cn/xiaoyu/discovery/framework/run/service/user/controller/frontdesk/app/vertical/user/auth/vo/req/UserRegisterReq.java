package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CountryLanguageCurrencyPhonePrefixZoneidEum;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CurrencyEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.RegTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("用户注册Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRegisterReq implements IReq {
    private static final long serialVersionUID = -8914066031469714629L;
    @ApiModelProperty(value = "用户名 注册方式为用户名时必传")
    private String username;

    @ApiModelProperty(value = "邮箱 注册方式为邮箱时必传")
    private String email;

    @ApiModelProperty(value = "手机号码 注册方式为手机号时必传")
    private String phone;

    @ApiModelProperty(value = "设备id 注册方式为设备号时必传")
    private String deviceNo;

    @ApiModelProperty(value = "密码 除了设备号注册外必传")
    private String password;

    @ApiModelProperty(value = "注册方式", required = true)
    @NotNull(message = UserCodeException.REGISTER_ENUM_IS_NULL_I18N_CODE)
    private RegTypeEum regTypeEum;

    @ApiModelProperty(value = "图形验证码 图形验证码开启时，需要传递")
    private String captchaCode;

    @ApiModelProperty("Authorization 普通图形验证码开启需要回传")
    private String authorization;

    @ApiModelProperty(value = "邮箱验证码 配置邮箱展示时必传")
    private String emailCode;

    @ApiModelProperty(value = "手机验证码 配置手机号展示时必传")
    private String phoneCode;

    @ApiModelProperty(value = "手机号前缀 配置手机号展示时必传")
    private String phonePrefix;

    @ApiModelProperty(value = "邀请码 如果配置展示则必传")
    private String invitationCode;

    @ApiModelProperty(value = "安全密码 如果配置展示则必传")
    private String securityPassword;

    @ApiModelProperty(value = "真实姓名 如果配置展示则必传")
    private String realName;

    @ApiModelProperty(value = "身份证id卡 如果配置展示则必传")
    private String idCard;

    @ApiModelProperty(value = "注册的国家枚举", required = true)
    @NotNull(message = UserCodeException.COUNTRY_IS_NULL_I18N_CODE)
    private CountryLanguageCurrencyPhonePrefixZoneidEum regClcppzEum;

    @ApiModelProperty(value = "注册的货币 可以为空 为空使用注册国家的默认货币")
    private CurrencyEum regCurrencyEum;

    /**
     * 20231202 新增--渠道信息 -- json格式，base64加密
     */
    @ApiModelProperty(value = "渠道信息")
    private String channelInfo;

}
