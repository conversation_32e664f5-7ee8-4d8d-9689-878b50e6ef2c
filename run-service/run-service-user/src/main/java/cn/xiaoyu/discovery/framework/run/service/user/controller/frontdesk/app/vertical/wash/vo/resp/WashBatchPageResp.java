package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/17
 */
@Data
public class WashBatchPageResp implements IResp {
    private static final long serialVersionUID = -6460908973826372524L;
    @ApiModelProperty("创建时间")
    @JacksonZoneId
    private Long createTime;
    @ApiModelProperty("修改时间")
    @JacksonZoneId
    private Long updateTime;
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("平台id/租户id")
    private Long platformId;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户code")
    private Long userCode;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty(value = "洗码总金额")
    private BigDecimal washTotalAmount;
    @ApiModelProperty(value = "总投注额")
    private BigDecimal betTotalAmount;
    @ApiModelProperty(value = "洗码金额的需求打码倍数")
    @JacksonAmountFmtIgnore
    private BigDecimal washBetMultiple;
    @ApiModelProperty("创建时间 时间戳")
    private Long createTimeStamp;
}
