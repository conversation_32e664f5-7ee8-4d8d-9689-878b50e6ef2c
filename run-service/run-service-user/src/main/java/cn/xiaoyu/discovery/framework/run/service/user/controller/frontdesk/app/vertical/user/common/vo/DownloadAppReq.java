package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.common.vo;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CountryLanguageCurrencyPhonePrefixZoneidEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.DownloadOriginEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadAppReq implements IReq {
    private static final long serialVersionUID = 5010695951846276919L;
    @ApiModelProperty(value = "下载源")
    private DownloadOriginEum downloadOriginEum;
    @ApiModelProperty(value = "国家枚举")
    private CountryLanguageCurrencyPhonePrefixZoneidEum clcppzEum;

    public DownloadAppReq(DownloadOriginEum downloadOriginEum) {
        this.downloadOriginEum = downloadOriginEum;
    }
}
