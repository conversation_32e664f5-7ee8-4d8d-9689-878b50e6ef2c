package cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询福利码兑换记录Req
 *
 * <AUTHOR>
 * @since 2023/10/12
 */
@ApiModel("查询福利码兑换记录Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryWelfareCodeExchangeRecordReq extends CreateTimePageReq {

    private static final long serialVersionUID = 6926663987204497072L;

    /**
     * 福利码ID
     */
    @ApiModelProperty(value = "福利码ID")
    private Long welfareCodeId;
}
