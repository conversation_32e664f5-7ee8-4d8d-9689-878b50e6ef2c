package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.DownloadGiftStatusEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.DownloadGiftTaskTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ActivityDownloadGiftAmountItemResp implements IResp {
    private static final long serialVersionUID = 6612318041703708590L;

    @ApiModelProperty(value = "第几天")
    private Integer sequence;

    @ApiModelProperty(value = "目标金额")
    private BigDecimal targetAmount;

    @ApiModelProperty(value = "已完成金额")
    private BigDecimal completeAmount;

    @ApiModelProperty(value = "完成进度")
    private BigDecimal completeRate;

    @ApiModelProperty(value = "时间区间")
    private String taskDate;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "下载有礼活动完成状态")
    private DownloadGiftStatusEum downloadGiftStatusEum;

    @ApiModelProperty(value = "下载有礼任务类型")
    private DownloadGiftTaskTypeEum downloadGiftTaskTypeEum;

}
