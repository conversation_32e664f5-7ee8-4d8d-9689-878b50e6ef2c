package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/5 11:10
 * @Description
 */
@Data
@ApiModel("新版推广-汇总信息")
public class AgentNewSummaryInfoResp implements IResp {
    private static final long serialVersionUID = 5084815531560171077L;
    @ApiModelProperty("佣金")
    private BigDecimal commission;
    @ApiModelProperty("邀请有礼-佣金")
    private BigDecimal inviteCommission;
    @ApiModelProperty(value = "团队业绩")
    private BigDecimal teamAchievement;
    @ApiModelProperty(value = "注册人数")
    private Long registerUserCount;
    @ApiModelProperty(value = "登录人数")
    private Long loginUserCount;
    @ApiModelProperty(value = "充值人数")
    private Long rechargeUserCount;
    @ApiModelProperty(value = "首充人数")
    private Long firstRechargeUserCount;
    @ApiModelProperty(value = "提现人数")
    private Long withdrawUserCount;
    @ApiModelProperty(value = "投注人数")
    private Long betUserCount;
}
