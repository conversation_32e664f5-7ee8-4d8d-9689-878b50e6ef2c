package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date : 2023/5/12
 */

@ApiModel("红包记录Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRedPacketRecordResp implements IResp {
    private static final long serialVersionUID = -7754028878844401236L;

    @ApiModelProperty(value = "红包标题")
    private String title;

    @ApiModelProperty(value = "获取的红包金额")
    private BigDecimal amount;

    @ApiModelProperty("领取时间")
    @JacksonZoneId
    private Long createTime;
}
