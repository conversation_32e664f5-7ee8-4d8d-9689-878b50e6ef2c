package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PrizePoolAwardRecordItem implements IResp {
    private static final long serialVersionUID = 8953601766982469721L;
    @ApiModelProperty("用户名称")
    private String username;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户头像")
    private String avatar;

    @ApiModelProperty(value = "中奖金额")
    private BigDecimal winAmount;

    @ApiModelProperty(value = "投入金币")
    private BigDecimal putInCoin;

    @ApiModelProperty(value = "当前用户名次")
    private Integer rank;
}
