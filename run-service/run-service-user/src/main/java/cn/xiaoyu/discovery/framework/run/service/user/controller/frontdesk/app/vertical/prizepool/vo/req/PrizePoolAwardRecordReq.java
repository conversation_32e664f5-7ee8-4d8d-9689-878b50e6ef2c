package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.prizepool.PrizePoolTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizePoolAwardRecordReq implements IReq {
	private static final long serialVersionUID = 325033694339648777L;

	@ApiModelProperty(value = "奖池类型")
	private PrizePoolTypeEum prizePoolTypeEum;
}
