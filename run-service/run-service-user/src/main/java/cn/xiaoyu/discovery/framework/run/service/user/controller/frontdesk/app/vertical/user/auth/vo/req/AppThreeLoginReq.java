package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.threelogin.ThreeLoginType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @since 2023/8/21 19:54
 */
@Data
@ApiModel("app三方登录请求参数")
@AllArgsConstructor
@NoArgsConstructor
public class AppThreeLoginReq implements IReq {
    private static final long serialVersionUID = -4183374337830033190L;
    @ApiModelProperty("获取用户信息的token")
    private String threeAccessToken;
    @ApiModelProperty("三方登录的类型-谷歌、脸书等")
    private ThreeLoginType threeLoginType;
}
