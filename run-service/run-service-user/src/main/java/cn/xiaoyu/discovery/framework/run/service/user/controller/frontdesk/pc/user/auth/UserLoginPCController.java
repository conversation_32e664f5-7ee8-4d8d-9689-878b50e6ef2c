package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth;

import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.Validator;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.context.HeaderDTO;
import cn.xiaoyu.discovery.framework.run.common.context.utils.ContextDTOUtils;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.eum.AccountStatusEum;
import cn.xiaoyu.discovery.framework.run.common.eum.EquipmenTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.SystemTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.TerminalTypeEum;
import cn.xiaoyu.discovery.framework.run.common.exception.BizException;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserEmailCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserLoginReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserPhoneCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserGraphicCaptchaResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserLoginConfigResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserLoginResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth.vo.req.ThreeLoginReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth.vo.req.ThreeLoginStateReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.auth.vo.resp.AuthorizeInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.ctrl.SwitchConvert;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.ctrl.UserLoginControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.ctrl.UserLoginSwitchControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.eum.threelogin.ThreeLoginOpenType;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.auth.UserLoginService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.auth.three.ThreeLoginAuthService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.captcha.UserCaptchaService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.ctrl.UserLoginControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.ctrl.UserLoginSwitchControlService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @since 2023/2/6 10:53
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 2)
@RequestMapping(UserLoginPCController.MODULE_PREFIX)
@Api(value = "2.用户登录",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + UserLoginPCController.MODULE_PREFIX + "模块-PC用户登录操作")
public class UserLoginPCController implements IModulePrefix {

    private final UserLoginSwitchControlService userLoginSwitchControlService;
    private final UserLoginControlService userLoginControlService;
    private final UserLoginService userLoginService;
    private final UserCaptchaService userCaptchaService;
    private final UserService userService;
    private final ThreeLoginAuthService threeLoginAuthService;

    @PostMapping("/getLoginConfig")
    @ApiOperation("1.获取登录相关配置信息")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    public R<UserLoginConfigResp> getLoginConfig() {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();

        UserLoginSwitchControlEntity userLoginSwitchControlEntity =
            userLoginSwitchControlService.findByTerminalTypeAndSystemType(terminalTypeEum, systemTypeEum);
        if (Objects.isNull(userLoginSwitchControlEntity)) {
            userLoginSwitchControlEntity = new UserLoginSwitchControlEntity();
        }
        UserLoginConfigResp resp = SwitchConvert.INSTANCE.convertLoginSwitchControlToResp(userLoginSwitchControlEntity);

        UserLoginControlEntity userLoginControlEntity =
            this.userLoginControlService.findByTerminalTypeAndSystemType(terminalTypeEum, systemTypeEum);
        if (Objects.isNull(userLoginControlEntity)) {
            userLoginControlEntity = new UserLoginControlEntity();
        }
        resp.setBackgroundAddress(userLoginControlEntity.getBackgroundAddress());

        return R.success(resp);
    }

    @PostMapping("/getLoginGraphicCaptcha")
    @ApiOperation("2.开启简单图形验证码则获取图形验证码")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<UserGraphicCaptchaResp> getLoginGraphicCaptcha(HttpServletRequest request) {
        String sessionId = request.getSession().getId();
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        return R.success(this.userCaptchaService.getLoginGraphicCaptcha(sessionId, terminalTypeEum, systemTypeEum));
    }

    @PostMapping("/sendLoginEmailCaptcha")
    @ApiOperation("3.发送邮箱验证码")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Void> sendLoginEmailCaptcha(@RequestBody @Valid UserEmailCaptchaReq req) {
        if (!Validator.isEmail(req.getEmail())) {
            throw new BizException(UserCodeException.EMAIL_FORMAT_IS_NOT_CORRECT);
        }
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        this.userCaptchaService.sendLoginEmailCaptcha(req.getEmail(), terminalTypeEum, systemTypeEum);
        return R.success();
    }

    @PostMapping("/sendLoginPhoneCaptcha")
    @ApiOperation("4.发送手机验证码")
    @ApiOperationSupport(order = 4)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Void> sendLoginPhoneCaptcha(@RequestBody @Valid UserPhoneCaptchaReq req) {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        this.userCaptchaService.sendLoginPhoneCaptcha(req.getPhonePrefix(), req.getPhone(), terminalTypeEum,
            systemTypeEum);
        return R.success();
    }

    @PostMapping("/login")
    @ApiOperation("5.用户登录认证")
    @ApiOperationSupport(order = 5)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<UserLoginResp> login(@RequestBody @Valid UserLoginReq req) {
        HeaderDTO headerDTO = ContextDTOUtils.getHeaderDTO();
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        EquipmenTypeEum equipmenTypeEum = ContextDTOUtils.getEquipmenTypeEum();
        return R
            .success(userLoginService.login(req, terminalTypeEum, systemTypeEum, equipmenTypeEum, headerDTO));
    }

    @PostMapping("/logout")
    @ApiOperation("6.用户退出登录")
    @ApiOperationSupport(order = 6)
    @SaCheckLogin(type = StpFrontdeskUtils.TYPE)
    @Encrypt
    public R<Boolean> logout() {
        if (StpFrontdeskUtils.isLogin()) {
            Long userId = FrontdeskUserInfoUtils.getUserId();
            userService.updateAccountStatusById(userId, AccountStatusEum.SIGN_UP);
            StpFrontdeskUtils.logout();
        }
        return R.success();
    }

    @PostMapping("/threeLogin/renderAuth")
    @ApiOperation("7.快捷登录-获取授权请求地址-需要保state参数，用于查询登录信息")
    @ApiOperationSupport(order = 7)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<AuthorizeInfoResp> renderAuth(@RequestBody @Valid ThreeLoginReq req) {

        if (Objects.isNull(req.getThreeLoginOpenType())) {
            req.setThreeLoginOpenType(ThreeLoginOpenType.POP_UP);
        }
        AuthorizeInfoResp authorizeInfoResp = threeLoginAuthService.getAuthorizeInfoResp(req);
        log.info("授权请求地址 authorizeUrl = " + authorizeInfoResp.getAuthorizeUrl());

        return R.success(authorizeInfoResp);
    }

    @PostMapping("/threeLogin/getUserLoginResp")
    @ApiOperation("8.快捷登录-根据state获取登录信息")
    @ApiOperationSupport(order = 8)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<UserLoginResp> getUserLoginResp(@RequestBody ThreeLoginStateReq req) {
        UserLoginResp userLoginResp = threeLoginAuthService.getUserLoginRespByState(req.getState());
        // if (Objects.isNull(userLoginResp)) {
        // return R.failed(UserCodeException.USER_NOT_LOGIN, I18nUtils.getI18nMessage(null,
        // UserCodeException.USER_NOT_LOGIN.getI18nCode(), "未获取到登录信息或登录信息已过期，请先登录"));
        // }
        return R.success(userLoginResp);
    }
}
