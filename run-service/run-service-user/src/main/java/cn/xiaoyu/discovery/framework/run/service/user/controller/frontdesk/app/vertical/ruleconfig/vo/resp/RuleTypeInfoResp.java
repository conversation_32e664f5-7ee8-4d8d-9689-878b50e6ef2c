package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.ruleconfig.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.ruleconfig.RuleTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 前端-规则配置Resp
 * @date 2024/2/26
 */
@ApiModel("规则配置Resp")
@Data
public class RuleTypeInfoResp implements IResp {

    private static final long serialVersionUID = 8998915959038516970L;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private RuleTypeEum ruleTypeEum;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String ruleTitle;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String ruleContent;

    /**
     * 是否显示计算结果（2024-03-25暂时只有[RuleTypeEum.COMMISSION_DETAILS]类型使用）
     *
     */
    @ApiModelProperty(value = "是否显示计算结果（2024-03-25暂时只有[RuleTypeEum.COMMISSION_DETAILS]类型使用）")
    private Boolean isCalculationResult = Boolean.TRUE;

    @ApiModelProperty(value = "人数")
    private Long number;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
}
