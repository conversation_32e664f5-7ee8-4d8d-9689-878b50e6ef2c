package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.prizepool.PrizePoolTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.PrizePoolQueryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PrizePoolRankReq implements IReq {
    private static final long serialVersionUID = -671461694979112248L;

    @ApiModelProperty(value = "奖池类型", required = true)
    @NotNull(message = UserCodeException.PARAM_MISS_I18N_CODE)
    private PrizePoolTypeEum prizePoolTypeEum;

    @ApiModelProperty(value = "榜单查询类型", required = true)
    @NotNull(message = UserCodeException.PARAM_MISS_I18N_CODE)
    private PrizePoolQueryTypeEum prizePoolQueryTypeEum;
}
