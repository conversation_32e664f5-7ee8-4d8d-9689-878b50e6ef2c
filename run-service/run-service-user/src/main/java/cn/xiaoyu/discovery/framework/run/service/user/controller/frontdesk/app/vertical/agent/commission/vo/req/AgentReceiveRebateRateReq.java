package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("返佣比例Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentReceiveRebateRateReq extends PageReq {
    private static final long serialVersionUID = -2940101477386157974L;

    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;

    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;

    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;

    @ApiModelProperty(value = "小类-子游戏", hidden = true)
    private Integer subTypeEumValue;
}
