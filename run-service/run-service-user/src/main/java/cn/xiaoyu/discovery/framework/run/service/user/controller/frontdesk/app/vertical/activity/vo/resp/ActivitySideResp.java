package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.activity.ActivityTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("侧边栏活动Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySideResp extends IdResp {
    private static final long serialVersionUID = -194811057520931024L;

    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "活动小图标")
    @ReplaceDomainName
    private String minPicture;

    @ApiModelProperty(value = "是否跳转详情,false跳转url")
    private Boolean clickShowTexter;

    @ApiModelProperty(value = "活动跳转地址 app有约定转为约定的内部地址")
    private String clickJumpUrl;

    @ApiModelProperty(value = "活动类型")
    private ActivityTypeEum activityTypeEum;

    @ApiModelProperty(value = "排序", hidden = true)
    private Integer sort;

    @ApiModelProperty(value = "开始时间", hidden = true)
    private Long startTime;
    @ApiModelProperty(value = "结束时间", hidden = true)
    private Long endTime;
}
