package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.desensitize.annotation.JacksonSlideDesensitize;
import cn.xiaoyu.discovery.framework.run.common.desensitize.eum.SlideDesensitizationTypeEum;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.EggAwardTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("幸运金蛋奖品记录Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityLuckyEggAwardRecordResp implements IResp {
    private static final long serialVersionUID = 2369553124172541772L;

    @JacksonSlideDesensitize(type = SlideDesensitizationTypeEum.CUSTOM, leftPlainTextLen = 1, rightPlainTextLen = 1)
    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("奖品类型")
    private EggAwardTypeEum awardType;

    @ApiModelProperty(value = "实物奖品")
    private String realAward;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "奖品数量")
    private Integer awardNum;

    @ApiModelProperty(value = "是否实物")
    private Boolean isReal;

}
