package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.turntable.TurnTableDrawTypeeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.turntable.TurnTableProjectTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2024/2/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TurnTableInfoProjectResp implements IResp {

    private static final long serialVersionUID = 6504385377482398597L;

    /**
     * 转盘中奖类型
     */
    @ApiModelProperty(value = "转盘中奖类型")
    private TurnTableDrawTypeeEum turnTableDrawTypeeEum;

    /**
     * 转盘选项显示类型
     */
    @ApiModelProperty(value = "转盘选项显示类型")
    private TurnTableProjectTypeEum turnTableProjectTypeEum;

    /**
     * 显示内容
     */
    @ApiModelProperty(value = "显示内容")
    private String content;

    /**
     * 显示图标
     */
    @ApiModelProperty(value = "显示图标")
    @ReplaceDomainName
    private String iconAddress;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
}