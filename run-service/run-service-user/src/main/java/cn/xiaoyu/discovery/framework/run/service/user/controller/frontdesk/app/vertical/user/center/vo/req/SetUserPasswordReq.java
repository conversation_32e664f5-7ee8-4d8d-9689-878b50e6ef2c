package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("设置用户登录密码Req")
@Data
public class SetUserPasswordReq implements IReq {

    private static final long serialVersionUID = -7718507479695054974L;

    @ApiModelProperty("密码")
    private String password;

}
