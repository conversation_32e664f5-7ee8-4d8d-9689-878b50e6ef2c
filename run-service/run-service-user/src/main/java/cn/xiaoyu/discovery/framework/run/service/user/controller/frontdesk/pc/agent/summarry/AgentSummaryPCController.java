package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.summarry;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.eum.SystemTypeEum;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.system.utils.CommonUtil;
import cn.xiaoyu.discovery.framework.run.service.system.utils.DateTimeUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.req.AgentSummaryInfoReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.resp.AgentNewSummaryInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.resp.AgentSummaryInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.summarry.vo.resp.AgentSummaryInfoV3Resp;
import cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentRebateControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserDataTotalEntity;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.AgentRebateTreatRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.invite.AgentInvitePayRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.summary.AgentSummaryService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateSettlementSummaryRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.total.UserDataTotalService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 9:58
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@RequestMapping(AgentSummaryPCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.推广-汇总信息", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + AgentSummaryPCController.MODULE_PREFIX + "模块-PC-推广-汇总信息")
public class AgentSummaryPCController implements IModulePrefix {

    private final AgentSummaryService agentSummaryService;

    private final AgentRebateTreatRecordService treatRecordService;
    private final AgentInvitePayRecordService invitePayRecordService;
    private final UserService userService;
    private final UserDataTotalService userDataTotalService;
    private final AgentRebateControlService agentRebateControlService;
    private final AgentRebateSettlementSummaryRecordService agentRebateSettlementSummaryRecordService;

    @PostMapping("/getAgentSummaryInfo")
    @ApiOperation("1.获取推广-汇总信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @Decrypt
    public R<AgentSummaryInfoResp> getAgentSummaryInfo(@RequestBody AgentSummaryInfoReq req) {

        AgentSummaryInfoResp resp = new AgentSummaryInfoResp();
        req.setUserId(FrontdeskUserInfoUtils.getUserId());

        // 1. 获取佣金:
        // 1.1 开始时间大于等于今天开始时间 - 查询今天的数据 - 待返佣记录表
        // 1.2 否则查询 返佣结算记录表
        BigDecimal commissionAmount = null;
        BigDecimal todayCommissionAmount = BigDecimal.ZERO;
        // 1.3. 查询团队业绩
        BigDecimal totalAchievementAmount = null;
        BigDecimal directAchievementAmount = null;
        UserDataTotalEntity total = userDataTotalService.findByUserId(FrontdeskUserInfoUtils.getUserId());
        if (req.getStartCreateTime() >= DateTimeUtils.getTodayStartTime()) {
            // 1）今日佣金 - 查待结算表
            commissionAmount =
                Objects.nonNull(total) ? CommonUtil.decimalDefValue(total.getTodayCommission()) : BigDecimal.ZERO;
        } else {
            // 由于结算再第二天进行，因此查询时间需要往后一天
            DateTime startDateTime = new DateTime(req.getStartCreateTime(), DateTimeUtils.getPlatformTimeZone())
                .offset(DateField.DAY_OF_YEAR, 1);
            DateTime endDateTime = new DateTime(req.getEndCreateTime(), DateTimeUtils.getPlatformTimeZone())
                .offset(DateField.DAY_OF_YEAR, 1);
            // 2）历史佣金- 查询已结算-
            commissionAmount = agentRebateSettlementSummaryRecordService.sumCommissionAmount(
                FrontdeskUserInfoUtils.getUserId(), startDateTime.getTime(), endDateTime.getTime());

            // 3）不等于昨天开始时间- 近7、30天 加上今天数据
            if (req.getStartCreateTime() < DateTimeUtils.getYesterdayStartTime()) {
                todayCommissionAmount =
                    Objects.nonNull(total) ? CommonUtil.decimalDefValue(total.getTodayCommission()) : BigDecimal.ZERO;
            }
        }
        commissionAmount = Optional.ofNullable(commissionAmount).orElse(BigDecimal.ZERO);
        // 加上今日佣金-【可为0】
        commissionAmount = commissionAmount.add(Optional.ofNullable(todayCommissionAmount).orElse(BigDecimal.ZERO));
        resp.setTotalCommission(commissionAmount);

        // - 所有业绩-今日-历史 - 查待结算表
        totalAchievementAmount = treatRecordService.sumAchievementAmount(FrontdeskUserInfoUtils.getUserId(), null,
            req.getStartCreateTime(), req.getEndCreateTime());
        directAchievementAmount = treatRecordService.sumAchievementAmount(FrontdeskUserInfoUtils.getUserId(), true,
            req.getStartCreateTime(), req.getEndCreateTime());

        // 业绩
        totalAchievementAmount = Optional.ofNullable(totalAchievementAmount).orElse(BigDecimal.ZERO);
        directAchievementAmount = Optional.ofNullable(directAchievementAmount).orElse(BigDecimal.ZERO);
        resp.setTeamTotalAchievement(totalAchievementAmount);
        resp.setDirectlyUnderTotalAchievement(directAchievementAmount);
        resp.setOtherTotalAchievement(totalAchievementAmount.subtract(directAchievementAmount));

        // 2. 邀请有礼佣金
        BigDecimal inviteTotalCommission = invitePayRecordService.sumPayAmount(FrontdeskUserInfoUtils.getUserId(),
            req.getStartCreateTime(), req.getEndCreateTime());
        inviteTotalCommission = Optional.ofNullable(inviteTotalCommission).orElse(BigDecimal.ZERO);
        resp.setInviteTotalCommission(inviteTotalCommission);

        // 3. 查询团队人数【需要根据模式】

        AgentRebateControlEntity agentRebateControlEntity =
            agentRebateControlService.findBySystemTypeEum(SystemTypeEum.BW);
        Long totalUserNum = null;
        Long directTotalUserNum = null;

        // 查询人数 -- TODO 需要根据佣金计算模式来统计 ? 三级 - 无限级？
        switch (agentRebateControlEntity.getCalculateModeEum()) {
            case UNLIMITED_NO_DIFFERENCE:
            case UNLIMITED_DIFFERENCE:
            case THREE_FLOORS:
            default:
                totalUserNum = userService.sumTotalUserNum(FrontdeskUserInfoUtils.getUserCode(),
                    req.getStartCreateTime(), req.getEndCreateTime());
                directTotalUserNum = userService.sumDirectTotalUserNum(FrontdeskUserInfoUtils.getUserCode(),
                    req.getStartCreateTime(), req.getEndCreateTime());
                break;
        }

        totalUserNum = Optional.ofNullable(totalUserNum).orElse(NumberUtils.LONG_ZERO);
        directTotalUserNum = Optional.ofNullable(directTotalUserNum).orElse(NumberUtils.LONG_ZERO);

        // 人数
        resp.setTeamTotalCount(totalUserNum);
        resp.setDirectlyUnderTotalCount(directTotalUserNum);
        resp.setOtherTotalCount(totalUserNum - directTotalUserNum);

        return R.success(resp);
    }

    @PostMapping("/getNewAgentSummaryInfo")
    @ApiOperation("2.获取推广-汇总信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @Decrypt
    public R<AgentNewSummaryInfoResp> getNewAgentSummaryInfo(@RequestBody AgentSummaryInfoReq req) {
        return R.success(agentSummaryService.getAgentSummaryInfo(req));
    }

    @PostMapping("/getAgentSummaryInfoV3")
    @ApiOperation("3.获取推广-汇总信息V3")
    @ApiOperationSupport(order = 1)
    @Encrypt
    @Decrypt
    public R<AgentSummaryInfoV3Resp> getAgentSummaryInfoV3() {
        return R.success(agentSummaryService.getAgentSummaryInfoV3());
    }

}
