package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("注册界面配置信息Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserRegisterConfigResp implements IResp {

    private static final long serialVersionUID = -761747154632807956L;

    @ApiModelProperty(value = "背景地址 可以是图片 可以是视频")
    @ReplaceDomainName
    private String backgroundAddress;
    @ApiModelProperty(value = "隐私协议")
    private String privacyAgreement;

    @ApiModelProperty(value = "手机号最小位数限制")
    private Integer phoneDigitsMinLimit;

    @ApiModelProperty(value = "手机号最大位数限制")
    private Integer phoneDigitsMaxLimit;

    @ApiModelProperty(value = "是否开启设备号注册-默认关闭")
    private Boolean enabledEquipmentReg = false;

    @ApiModelProperty(value = "是否开启用户注册-默认开启")
    private Boolean enabledUsernameReg = true;

    @ApiModelProperty(value = "是否开启用户名注册普通验证码-默认开启")
    private Boolean enabledUsernameBaseCaptchaReg = true;

    @ApiModelProperty(value = "是否开启用户名注册aj验证码-默认关闭")
    private Boolean enabledUsernameAjCaptchaReg = false;

    @ApiModelProperty(value = "是否开启用户名注册必填手机号-默认关闭")
    private Boolean enabledUsernameMustPhoneReg = false;

    @ApiModelProperty(value = "是否开启用户名注册必填手机号验证码-前提必须开启短信验证码-必须开启必填手机号-默认关闭")
    private Boolean enabledUsernameMustPhoneCaptchaReg = false;

    @ApiModelProperty(value = "是否开启用户名注册必填邮箱-默认关闭")
    private Boolean enabledUsernameMustEmailReg = false;

    @ApiModelProperty(value = "是否开启用户名注册必填邮箱验证码-前提必须开启邮箱验证码-必须开启必填邮箱-默认关闭")
    private Boolean enabledUsernameMustEmailCaptchaReg = false;

    @ApiModelProperty(value = "是否开启手机注册-默认关闭")
    private Boolean enabledPhoneReg = false;

    @ApiModelProperty(value = "是否开启手机注册必填手机号验证码-前提必须开启短信验证码-开启手机注册-默认开启")
    private Boolean enabledPhoneMustPhoneCaptchaReg = true;

    @ApiModelProperty(value = "是否开启手机注册普通验证码-默认关闭")
    private Boolean enabledPhoneBaseCaptchaReg = false;

    @ApiModelProperty(value = "是否开启手机注册aj验证码-默认关闭")
    private Boolean enabledPhoneAjCaptchaReg = false;

    @ApiModelProperty(value = "是否开启手机注册必填用户名-默认关闭-开启则用户名使用输入的-关闭用户名为手机号")
    private Boolean enabledPhoneMustUsernameReg = false;

    @ApiModelProperty(value = "是否开启手机注册必填邮箱-默认关闭-开启则用户名使用输入的邮箱-关闭用户名为手机号")
    private Boolean enabledPhoneMustEmailReg = false;

    @ApiModelProperty(value = "是否开启手机注册必填邮箱验证码-前提必须开启邮箱验证码-必须开启必填邮箱-默认关闭")
    private Boolean enabledPhoneMustEmailCaptchaReg = false;

    @ApiModelProperty(value = "是否开启邮箱注册-默认关闭")
    private Boolean enabledEmailReg = false;

    @ApiModelProperty(value = "是否开启邮箱注册必填邮箱验证码-前提必须开启邮箱验证码-必须开启邮箱注册-默认开启")
    private Boolean enabledEmailMustEmailCaptchaReg = true;

    @ApiModelProperty(value = "是否开启邮箱注册普通验证码-默认关闭")
    private Boolean enabledEmailBaseCaptchaReg = false;

    @ApiModelProperty(value = "是否开启邮箱注册aj验证码-默认关闭")
    private Boolean enabledEmailAjCaptchaReg = false;

    @ApiModelProperty(value = "是否开启邮箱注册必填用户名-默认关闭-开启则用户名使用输入的-关闭用户名为邮箱号")
    private Boolean enabledEmailMustUsernameReg = false;

    @ApiModelProperty(value = "是否开启邮箱注册必填手机号-默认关闭-开启则用户名使用输入的手机号-关闭用户名为邮箱")
    private Boolean enabledEmailMustPhoneReg = false;

    @ApiModelProperty(value = "是否开启邮箱注册必填手机号验证码-前提必须开启短信验证码-开启必填手机号-默认关闭")
    private Boolean enabledEmailMustPhoneCaptchaReg = false;

    @ApiModelProperty(value = "是否开启注册必填邀请码-默认关闭")
    private Boolean enabledMustInvitationCodeReg = false;

    @ApiModelProperty(value = "是否开启注册必填安全密码-默认关闭")
    private Boolean enabledMustSecurityPasswdReg = false;

    @ApiModelProperty(value = "是否开启注册必填真实姓名-默认关闭")
    private Boolean enabledMustRealNameReg = false;

    @ApiModelProperty(value = "是否开启注册必填身份证id卡-默认关闭")
    private Boolean enabledMustIdCardReg = false;

    @ApiModelProperty(value = "是否显示邀请码输入框")
    private Boolean showInvitationCodeInput = true;

}
