package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.agent.invite;

import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollectionUtil;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.context.utils.ContextDTOUtils;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.req.DomainNameReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.req.ShareInfoReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.resp.AgentShareInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.resp.AgentTerminalControlResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.resp.InviteAwardItemResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.agent.AgentInviteControlConvert;
import cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentInviteControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentRebateControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentRebateTerminalControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.AgentRebateTerminalControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.domain.AgentDomainNameVisitorService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.domain.AgentPopularizeService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.invite.AgentInviteControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.invite.AgentInviteItemService;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateControlService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/16 11:08
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@PcController
@RestController
@ApiSort(value = 1)
@RequestMapping(AgentInvitePCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.推广-邀请有礼-信息", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + AgentInvitePCController.MODULE_PREFIX + "模块-PC-推广-邀请有礼信息")
public class AgentInvitePCController implements IModulePrefix {
    private final AgentPopularizeService agentPopularizeService;
    private final AgentRebateTerminalControlService agentRebateTerminalControlService;
    private final AgentInviteControlService agentInviteControlService;
    private final AgentDomainNameVisitorService agentDomainNameVisitorService;
    private final AgentRebateControlService agentRebateControlService;
    private final AgentInviteItemService agentInviteItemService;

    @PostMapping("/shareInfo")
    @ApiOperation("1.获取推广分享信息")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PageResp<AgentShareInfoResp>> shareInfo(@RequestBody ShareInfoReq req) {
        return R.success(agentPopularizeService.findAgentshareList(req));
    }

    @PostMapping("/getTerminalControlInfo")
    @ApiOperation("2.获取教程信息-包含文本说明")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Encrypt
    public R<AgentTerminalControlResp> getTerminalControlInfo() {
        AgentTerminalControlResp agentTerminalControlResp = new AgentTerminalControlResp();
        AgentRebateTerminalControlEntity terminalControlEntity = agentRebateTerminalControlService
            .findByTerminalTypeAndSystemType(ContextDTOUtils.getTerminalTypeEum(), ContextDTOUtils.getSystemTypeEum());
        List<AgentInviteControlEntity> inviteControlList = agentInviteControlService.findAll();
        if (CollectionUtil.isNotEmpty(inviteControlList)) {
            agentTerminalControlResp.setInviteText(inviteControlList.get(0).getInviteText());
        }
        if (Objects.isNull(terminalControlEntity)) {
            return R.success(agentTerminalControlResp);
        }
        // 20230901 根据佣金记录类型和佣金计算模式，确认返回的推广图片
        AgentRebateControlEntity controlEntity =
            agentRebateControlService.findBySystemTypeEum(ContextDTOUtils.getSystemTypeEum());
        switch (controlEntity.getCalculateModeEum()) {
            case UNLIMITED_NO_DIFFERENCE:
                switch (controlEntity.getCalculateTypeEum()) {
                    case BET:
                        agentTerminalControlResp.setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedBetImgUrl());
                        break;
                    case WIN:
                        agentTerminalControlResp.setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedWinImgUrl());
                        break;
                    case LOSE:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedLoseImgUrl());
                        break;
                    default:
                        break;
                }
                break;
            case UNLIMITED_DIFFERENCE:
                switch (controlEntity.getCalculateTypeEum()) {
                    case BET:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedDifferenceBetImgUrl());
                        break;
                    case WIN:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedDifferenceWinImgUrl());
                        break;
                    case LOSE:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedDifferenceLoseImgUrl());
                        break;
                    default:
                        break;
                }
                break;
            case THREE_FLOORS:
                switch (controlEntity.getCalculateTypeEum()) {
                    case BET:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteThreeFloorsBetImgUrl());
                        break;
                    case WIN:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteThreeFloorsWinImgUrl());
                        break;
                    case LOSE:
                        agentTerminalControlResp
                            .setPromoteImgUrl(terminalControlEntity.getPromoteUnlimitedLoseImgUrl());
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        return R.success(agentTerminalControlResp);
    }

    @PostMapping("/visitDomainName")
    @ApiOperation("3.域名访问统计")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Boolean> visitDomainName(@RequestBody @Valid DomainNameReq req) {
        Boolean success = agentDomainNameVisitorService.addVisitNum(
            Long.valueOf(PlatformIdContextHolder.getPlatformId()), System.currentTimeMillis(), req.getDomainName());
        return R.success(success);
    }

    @PostMapping("/getInviteAwardItems")
    @ApiOperation("4.获取邀请明细")
    @ApiOperationSupport(order = 4)
    @SaIgnore
    @Encrypt
    public R<List<InviteAwardItemResp>> getInviteAwardItems() {
        return R.success(
            AgentInviteControlConvert.INSTANCE.convertItemNumResp(agentInviteItemService.findAllByInviteNumAsc()));
    }
}
