package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TurnPlateAwardTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("中奖结果Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTurnPlateResultResp extends IdResp {
    private static final long serialVersionUID = -7576094887193072815L;

    @ApiModelProperty(value = "奖品图标")
    @ReplaceDomainName
    private String awardIcon;

    @ApiModelProperty(value = "奖品类型")
    private TurnPlateAwardTypeEum awardType;

    @ApiModelProperty(value = "实物奖品")
    private String realAward;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "奖品数量")
    private Integer awardNum;

    @ApiModelProperty(value = "是否实物")
    private Boolean isReal;

    @ApiModelProperty(value = "领奖时间")
    @JacksonZoneId
    private Long createTime;
}
