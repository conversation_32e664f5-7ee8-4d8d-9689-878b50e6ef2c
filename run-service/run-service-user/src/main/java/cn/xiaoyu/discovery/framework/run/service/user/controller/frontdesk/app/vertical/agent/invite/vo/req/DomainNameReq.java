package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/7/7
 */

@Data
public class DomainNameReq implements IReq {
    private static final long serialVersionUID = 5439558353465830276L;
    @ApiModelProperty(value = "访问的域名", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private String domainName;
}
