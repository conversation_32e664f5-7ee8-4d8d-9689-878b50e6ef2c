package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动详情请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDetailReq implements IReq {
    private static final long serialVersionUID = -4172462741892920959L;

    @ApiModelProperty(name = "活动id")
    private Long activityId;

}
