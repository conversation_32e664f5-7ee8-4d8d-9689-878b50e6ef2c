package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import cn.xiaoyu.discovery.framework.run.common.eum.SexEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/19 15:04
 */
@ApiModel("更新用户信息Req")
@Data
public class UpdateUserInfoReq extends IdReq {
    private static final long serialVersionUID = 1732686159821824233L;

    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "国家码-手机前缀")
    private String phonePrefix;
    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "真实姓名-支付提现绑卡需使用-也可直接存储在绑卡处-在联动存储此处")
    private String realName;

    @ApiModelProperty(value = "昵称")
    private String nickname;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "性别")
    private SexEum sexEum;
    @ApiModelProperty(value = "头像-域名后的地址串")
    private String avatar;
    @ApiModelProperty("验证码-手机验证码或邮箱验证码")
    private String captcha;
}
