package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("转盘奖品Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTurnPlateAwardResp extends IdResp {
    private static final long serialVersionUID = -857656725083182400L;

    @ApiModelProperty(value = "奖品图标")
    @ReplaceDomainName
    private String awardIcon;

    @ApiModelProperty(value = "实物奖品")
    private String realAward;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "奖品数量")
    private Integer awardNum;

    @ApiModelProperty(value = "是否实物")
    private Boolean isReal;

}
