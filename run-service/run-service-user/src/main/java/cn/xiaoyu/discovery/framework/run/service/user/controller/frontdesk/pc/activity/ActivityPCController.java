package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.activity;

import java.util.List;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.context.HeaderDTO;
import cn.xiaoyu.discovery.framework.run.common.context.utils.ContextDTOUtils;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.eum.SystemTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.TerminalTypeEum;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.common.utils.ip.IpUtils;
import cn.xiaoyu.discovery.framework.run.common.utils.request.RequestUtils;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.activity.ActivityTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.prizepool.vo.req.FindUserRedPacketPickUpRecordPageReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.ActivityApplyReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.ActivityDetailReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.ActivityRecordTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.ActivityTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.LuckyEggTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.RedPacketTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.req.TurnPlateTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityDetailResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityDownloadGiftDetailResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityFirstRechargeResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityGrabRedPacketResultResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityHomeWindowResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityItemResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityLabelResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityLuckyEggAwardRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityLuckyEggInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityLuckyEggResultResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityRedPacketRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityRedPacketResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityReturnAwardResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityReturnSignResultResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivitySideResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivitySignConfigResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivitySignResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityStatusResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityTurnPlateAwardRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityTurnPlateInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityTurnPlateResultResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.LuckyEggEnableResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.RedEnvelopeRainResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.TurnPlatePlayEnableResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.activity.vo.resp.ActivityDownloadGiftWidgetPcResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.AwardDrawRoleTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.HierarchicalValidationTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityBusinessService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityDistributeRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityEntryRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityFirstRechargeLoginBizService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityLabelService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityLuckyEggBusinessService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityRedPacketBusinessService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityReturnBusinessService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivitySignService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityTurnPlateBusinessService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.downloadGift.ActivityDownloadGiftBizService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.hierarchical.UserHierarchicalBizService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date : 2023/5/24
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@RequestMapping(ActivityPCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.活动管理",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + ActivityPCController.MODULE_PREFIX + "模块-PC活动管理操作")
public class ActivityPCController implements IModulePrefix {
    private final ActivityBusinessService activityBusinessService;
    private final ActivityEntryRecordService activityEntryRecordService;
    private final ActivityService activityService;
    private final ActivityLabelService activityLabelService;
    private final ActivityDistributeRecordService activityDistributeRecordService;
    private final ActivitySignService signService;
    private final ActivityTurnPlateBusinessService activityTurnPlateBusinessService;
    private final ActivityLuckyEggBusinessService activityLuckyEggBusinessService;
    private final ActivityReturnBusinessService activityReturnBusinessService;
    private final ActivityRedPacketBusinessService activityRedPacketBusinessService;
    private final UserHierarchicalBizService userHierarchicalBizService;
    private final ActivityDownloadGiftBizService activityDownloadGiftBizService;
    private final ActivityFirstRechargeLoginBizService activityFirstRechargeLoginBizService;

    @PostMapping("/getActivityLabelList")
    @ApiOperation("1.获取活动标签列表")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    public R<PageResp<ActivityLabelResp>> getActivityLabelList() {
        List<ActivityLabelResp> list = activityLabelService.getActivityLabelList();
        return R.success(new PageResp<>(list, (long)list.size()));
    }

    @PostMapping("/getActivityList")
    @ApiOperation("2.获取活动列表")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PageResp<ActivityItemResp>> getActivityList(@RequestBody ActivityTypeReq req) {
        return R.success(activityBusinessService.getActivityList(req));
    }

    @PostMapping("/getActivityDetail")
    @ApiOperation("3.获取活动详情")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<ActivityDetailResp> getActivityDetail(@RequestBody @Valid ActivityDetailReq req) {
        return R.success(activityBusinessService.getActivityDetail(req.getActivityId()));
    }

    @PostMapping("/applyActivity")
    @ApiOperation("4.活动报名")
    @ApiOperationSupport(order = 4)
    @Encrypt
    @Decrypt
    public R<Boolean> applyActivity(@RequestBody @Valid ActivityApplyReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.ACTIVITY);
        return R.success(
            activityBusinessService.applyActivity(userId, req, Long.valueOf(PlatformIdContextHolder.getPlatformId())));
    }

    @PostMapping("/applyActivityRecord")
    @ApiOperation("5.活动报名进度查询")
    @ApiOperationSupport(order = 5)
    @Encrypt
    @Decrypt
    public R<PageResp<ActivityRecordResp>> applyActivityRecord(@RequestBody PageReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        req.setPageNo(req.getPageNo() > 0 ? (req.getPageNo() - 1) * req.getPageSize() : 0);
        List<ActivityRecordResp> list = activityEntryRecordService.getActivityRecord(req, userId);
        Long total = activityEntryRecordService.getActivityRecordTotal(userId);
        return R.success(new PageResp<>(list, total));
    }

    @PostMapping("/getSideActivityList")
    @ApiOperation("6.获取客户端侧边栏活动列表")
    @ApiOperationSupport(order = 6)
    @SaIgnore
    @Encrypt
    public R<PageResp<ActivitySideResp>> getSideActivityList() {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        return R.success(activityService.getSideActivityList(terminalTypeEum));
    }

    @PostMapping("/getLuckyTurnPlateInfo")
    @ApiOperation("8.获取幸运转盘界面信息")
    @ApiOperationSupport(order = 8)
    @SaIgnore
    @Encrypt
    public R<ActivityTurnPlateInfoResp> getLuckyTurnPlateInfo() {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        return R.success(activityTurnPlateBusinessService.getLuckyTurnPlateInfo(userId));
    }

    @PostMapping("/playLuckyTurnPlate")
    @ApiOperation("9.幸运转盘开转")
    @ApiOperationSupport(order = 9)
    @Encrypt
    @Decrypt
    public R<ActivityTurnPlateResultResp> playLuckyTurnPlate(@RequestBody @Valid TurnPlateTypeReq req) {
        HeaderDTO headerDTO = ContextDTOUtils.getHeaderDTO();
        Long userId = FrontdeskUserInfoUtils.getUserId();
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.ACTIVITY);
        String regIp = StringUtils.isNotEmpty(headerDTO.getOuterInnerIp()) ? headerDTO.getOuterInnerIp()
            : IpUtils.getClientIPAddress(RequestUtils.getRequest());
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        return R.success(activityTurnPlateBusinessService.playLuckyTurnPlate(req.getTurnPlateTypeEum(), userId, regIp,
            terminalTypeEum, systemTypeEum));
    }

    @PostMapping("/getLuckyTurnPlateAwardRecord")
    @ApiOperation("10.获取幸运转盘获奖记录")
    @ApiOperationSupport(order = 10)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PageResp<ActivityTurnPlateAwardRecordResp>>
        getLuckyTurnPlateAwardRecord(@RequestBody ActivityRecordTypeReq req) {
        Long userId = null;
        if (req.getAwardDrawRoleTypeEum() == AwardDrawRoleTypeEum.MYSELF) {
            userId = FrontdeskUserInfoUtils.getUserId();
        }
        req.setPageNo(req.getPageNo() > 0 ? (req.getPageNo() - 1) * req.getPageSize() : 0);
        return R.success(activityDistributeRecordService.findPageTurnRecordsByUserIdAndTypePage(req, userId,
            ActivityTypeEum.TURN_PLATE));
    }

    @PostMapping("/getLuckyEggInfo")
    @ApiOperation("11.获取幸运金蛋界面信息")
    @ApiOperationSupport(order = 11)
    @Encrypt
    public R<ActivityLuckyEggInfoResp> getLuckyEggInfo() {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        return R.success(activityLuckyEggBusinessService.getLuckyEggInfo(userId));
    }

    @PostMapping("/playLuckyEgg")
    @ApiOperation("12.砸幸运金蛋")
    @ApiOperationSupport(order = 12)
    @Encrypt
    @Decrypt
    public R<ActivityLuckyEggResultResp> playLuckyEgg(@RequestBody @Valid LuckyEggTypeReq req) {
        HeaderDTO headerDTO = ContextDTOUtils.getHeaderDTO();
        Long userId = FrontdeskUserInfoUtils.getUserId();
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.ACTIVITY);
        String regIp = StringUtils.isNotEmpty(headerDTO.getOuterInnerIp()) ? headerDTO.getOuterInnerIp()
            : IpUtils.getClientIPAddress(RequestUtils.getRequest());
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        return R.success(activityLuckyEggBusinessService.playLuckyEgg(req.getEggAwardTypeEum(), userId, regIp,
            terminalTypeEum, systemTypeEum));
    }

    @PostMapping("/getLuckyEggAwardRecord")
    @ApiOperation("13.幸运金蛋中奖记录")
    @ApiOperationSupport(order = 13)
    @Encrypt
    @Decrypt
    public R<PageResp<ActivityLuckyEggAwardRecordResp>> getLuckyEggAwardRecord(@RequestBody @Valid PageReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        req.setPageNo(req.getPageNo() > 0 ? (req.getPageNo() - 1) * req.getPageSize() : 0);
        List<ActivityLuckyEggAwardRecordResp> list = activityDistributeRecordService.findEggAwardRecord(req, userId);
        Long total = activityDistributeRecordService.findEggAwardRecordTotalNum(userId);
        return R.success(new PageResp<>(list, total));
    }

    @PostMapping("/getReturnAward")
    @ApiOperation("14.回归奖励")
    @ApiOperationSupport(order = 14)
    @Encrypt
    public R<ActivityReturnAwardResp> getReturnAward() {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        return R.success(activityReturnBusinessService.getReturnAward(userId));
    }

    @PostMapping("/signReturnAward")
    @ApiOperation("15.领取回归奖励")
    @ApiOperationSupport(order = 15)
    @Encrypt
    public R<ActivityReturnSignResultResp> getReturnAwardList() {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.ACTIVITY);
        return R.success(activityReturnBusinessService.signReturnAward(userId));
    }

    @PostMapping("/grabRedPacket")
    @ApiOperation("16.抢红包")
    @ApiOperationSupport(order = 16)
    @Encrypt
    @Decrypt
    public R<ActivityGrabRedPacketResultResp> grabRedPacket(@RequestBody RedPacketTypeReq req) {
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(FrontdeskUserInfoUtils.getUserId(),
            HierarchicalValidationTypeEum.ACTIVITY);
        ActivityGrabRedPacketResultResp resp =
            activityRedPacketBusinessService.grabRedPacket(req, FrontdeskUserInfoUtils.getUserId());
        if (StringUtils.isNotBlank(resp.getFailureMsg())) {
            return R.failed(resp.getCode(), resp.getFailureMsg());
        }
        // 发送资金流水消息 & 个人消息
        activityRedPacketBusinessService.sendPickupMsg(resp);
        resp.setPickUpRecord(null);
        return R.success(resp);
    }

    @PostMapping("/getRedPacket")
    @ApiOperation("17.获取红包")
    @ApiOperationSupport(order = 17)
    @Encrypt
    public R<PageResp<ActivityRedPacketResp>> getRedPacket() {
        PageResp<ActivityRedPacketResp> resp = new PageResp<>();
        List<ActivityRedPacketResp> list = activityRedPacketBusinessService.getRedPacket();
        resp.setList(list);
        resp.setTotal((long)list.size());
        return R.success(resp);
    }

    @PostMapping("/redEnvelopeRain")
    @ApiOperation("18.红包雨")
    @ApiOperationSupport(order = 18)
    @Encrypt
    public R<List<RedEnvelopeRainResp>> redEnvelopeRain() {
        return R.success(activityRedPacketBusinessService.redEnvelopeRain());
    }

    @PostMapping("/getRedPacketRecord")
    @ApiOperation("19.获取红包记录")
    @ApiOperationSupport(order = 19)
    @Encrypt
    @Decrypt
    public R<PageResp<ActivityRedPacketRecordResp>>
        getRedPacketRecord(@RequestBody FindUserRedPacketPickUpRecordPageReq req) {
        return R.success(activityRedPacketBusinessService.getRedPacketRecord(req));
    }

    @PostMapping("/getSignConfig")
    @ApiOperation("20.获取签到配置")
    @ApiOperationSupport(order = 20)
    @Encrypt
    public R<ActivitySignConfigResp> getSignConfig() {
        return R.success(signService.getSignConfig());
    }

    @PostMapping("/userSign")
    @ApiOperation("21.用户签到")
    @ApiOperationSupport(order = 21)
    @Encrypt
    public R<ActivitySignResp> userSign() {
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(FrontdeskUserInfoUtils.getUserId(),
            HierarchicalValidationTypeEum.ACTIVITY);
        return R.success(signService.userSign());
    }

    @PostMapping("/getActivityStatus")
    @ApiOperation("22.获取活动开关状态")
    @ApiOperationSupport(order = 22)
    @SaIgnore
    @Encrypt
    public R<ActivityStatusResp> getActivityStatus() {
        return R.success(activityBusinessService.getActivityStatus());
    }

    @PostMapping("/getTurnPlatePlayEnabled")
    @ApiOperation("23.转盘是否可转状态")
    @ApiOperationSupport(order = 23)
    @Encrypt
    public R<TurnPlatePlayEnableResp> getTurnPlatePlayEnabled() {
        return R.success(activityTurnPlateBusinessService.getTurnPlatePlayEnabled());
    }

    @PostMapping("/getLuckyEggPlayEnabled")
    @ApiOperation("24.幸运金蛋是否可砸状态")
    @ApiOperationSupport(order = 24)
    @Encrypt
    public R<LuckyEggEnableResp> getLuckyEggPlayEnabled() {
        return R.success(activityLuckyEggBusinessService.getLuckyEggPlayEnabled());
    }

    @PostMapping("/getHomeWindowActivity")
    @ApiOperation("24.获取首页弹窗活动")
    @ApiOperationSupport(order = 24)
    @SaIgnore
    @Encrypt
    public R<List<ActivityHomeWindowResp>> getHomeWindowActivity() {
        return R.success(activityBusinessService.getHomeWindowActivity());
    }

    @PostMapping("/getDownloadGiftWidget")
    @ApiOperation("25.获取下载有礼活动弹窗")
    @ApiOperationSupport(order = 25)
    @SaIgnore
    @Encrypt
    public R<ActivityDownloadGiftWidgetPcResp> getDownloadGiftWidget() {
        return R.success(activityDownloadGiftBizService.getDownloadGiftPCWidget());
    }

    @PostMapping("/getDownloadGiftDetail")
    @ApiOperation("26.获取下载有礼活动详情")
    @ApiOperationSupport(order = 26)
    @SaIgnore
    @Encrypt
    public R<ActivityDownloadGiftDetailResp> getDownloadGiftDetail() {
        return R.success(activityDownloadGiftBizService.getDownloadGiftDetail());
    }

    @PostMapping("/receiveDownloadGiftAward")
    @ApiOperation("27.领取下载有礼活动奖励")
    @ApiOperationSupport(order = 27)
    @Decrypt
    @Encrypt
    public R<Boolean> receiveDownloadGiftAward(@RequestBody IdReq req) {
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(FrontdeskUserInfoUtils.getUserId(),
            HierarchicalValidationTypeEum.ACTIVITY);
        return R.success(activityDownloadGiftBizService.receiveDownloadGiftAward(req.getId(),
            FrontdeskUserInfoUtils.getUserId(), Long.valueOf(PlatformIdContextHolder.getPlatformId())));
    }

    @PostMapping("/getFirstRechargeLoginActivity")
    @ApiOperation("28.获取首充登录活动详情")
    @ApiOperationSupport(order = 28)
    @Encrypt
    public R<ActivityFirstRechargeResp> getFirstRechargeLoginActivity() {
        return R.success(activityFirstRechargeLoginBizService.getHomeActivityData(FrontdeskUserInfoUtils.getUserId()));
    }

    @PostMapping("/drawFirstRechargeLoginAward")
    @ApiOperation("29.领取首充登录活动奖励")
    @ApiOperationSupport(order = 29)
    @Encrypt
    @Decrypt
    public R<Boolean> drawFirstRechargeLoginAward(@RequestBody IdReq req) {
        return R.success(
            activityFirstRechargeLoginBizService.drawActivityAward(req.getId(), FrontdeskUserInfoUtils.getUserId()));
    }
}
