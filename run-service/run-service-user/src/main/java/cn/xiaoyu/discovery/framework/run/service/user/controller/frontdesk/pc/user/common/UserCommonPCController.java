package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.user.common;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.common.vo.BookRecordCategoryResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.common.vo.DownloadAppReq;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.common.UserCommonService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.logs.UserDownloadAppLogService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/2/27 14:59
 * @Description
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(UserCommonPCController.MODULE_PREFIX)
@Api(value = "1.用户",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + UserCommonPCController.MODULE_PREFIX + "模块-公共参数")
public class UserCommonPCController implements IModulePrefix {
    private final UserCommonService userCommonService;
    private final UserDownloadAppLogService downloadAppLogService;

    @PostMapping("/getBookRecordCategory")
    @ApiOperation("1.获取到账类型")
    @ApiOperationSupport(order = 1)
    @Decrypt
    @Encrypt
    @SaIgnore
    public R<BookRecordCategoryResp> getBookRecordCategory() {
        return R.success(userCommonService.getBookRecordCategory());
    }

    @PostMapping("/createDownloadAppLog")
    @ApiOperation("2.创建下载APP日志")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Boolean> createDownloadAppLog(@RequestBody(required = false) DownloadAppReq req) {
        return R.success(downloadAppLogService.createDownloadAppLog(req));
    }
}
