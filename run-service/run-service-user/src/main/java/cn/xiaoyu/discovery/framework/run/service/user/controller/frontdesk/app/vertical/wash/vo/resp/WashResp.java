package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel("手动洗码Req")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WashResp implements IResp {

	private static final long serialVersionUID = -8319688708488119665L;
	@ApiModelProperty(value = "本次洗码金额")
	private BigDecimal washAmount;
}
