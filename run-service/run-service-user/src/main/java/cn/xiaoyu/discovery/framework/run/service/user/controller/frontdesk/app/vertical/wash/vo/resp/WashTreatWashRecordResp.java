package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.amount.fmt.annotation.JacksonAmountFmtIgnore;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameManufacturersTypeEum;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@ApiModel("待洗码列表Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WashTreatWashRecordResp implements IResp {

    private static final long serialVersionUID = -2103084543821884172L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;

    @ApiModelProperty(value = "厂商")
    private GameManufacturersTypeEum manufacturersTypeEum;

    @ApiModelProperty(value = "厂商icon地址")
    @ReplaceDomainName
    private String iconUrl;

    @ApiModelProperty(value = "小类-子游戏")
    private GameSubTypeEum subTypeEum;

    @ApiModelProperty(value = "待洗码的打码金额（有效投注）")
    private BigDecimal treatWashBetAmount;

    @ApiModelProperty(value = "洗码比例0.0001格式（只有按照子游戏计算和显示时才会有值）")
    @JacksonAmountFmtIgnore
    private BigDecimal washRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "洗码金额")
    private BigDecimal washAmount;

    @ApiModelProperty(value = "洗码笔数")
    private Long count;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "用户code", hidden = true)
    private Long userCode;

    @ApiModelProperty(value = "用户名", hidden = true)
    private String username;

    @ApiModelProperty(value = "平台id/租户id", hidden = true)
    private Long platformId;

    @ApiModelProperty(hidden = true)
    private Integer categoryTypeValue;

    @ApiModelProperty(hidden = true)
    private Integer manufacturersTypeValue;

    @ApiModelProperty(hidden = true)
    private Integer subTypeValue;

}
