package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("幸运金蛋奖品Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityLuckyEggAwardResp implements IResp {
    private static final long serialVersionUID = 7530013136175164638L;

    @ApiModelProperty(value = "奖品图标")
    private String awardIcon;

    @ApiModelProperty(value = "实物奖品")
    private String realAward;

    @ApiModelProperty(value = "奖金")
    private BigDecimal awardAmount;

    @ApiModelProperty(value = "奖品数量")
    private Integer awardNum;

    @ApiModelProperty(value = "是否实物")
    private Boolean isReal;
}
