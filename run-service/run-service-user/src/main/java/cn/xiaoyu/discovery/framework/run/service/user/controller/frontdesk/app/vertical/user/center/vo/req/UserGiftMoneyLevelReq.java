package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description
 * <AUTHOR>
 * @date 2023/9/26
 */
@Data
public class UserGiftMoneyLevelReq implements IReq {
    private static final long serialVersionUID = -5930663446422024138L;

    @ApiModelProperty(value = "等级枚举")
    private LevelEum levelEum;
}
