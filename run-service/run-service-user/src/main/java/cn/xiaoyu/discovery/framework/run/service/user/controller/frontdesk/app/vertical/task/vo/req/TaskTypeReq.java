package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskAgingTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("任务类型请求Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskTypeReq implements IReq {
    private static final long serialVersionUID = 3892785911010103615L;

    @ApiModelProperty(name = "任务时效类型", required = true)
    @NotNull(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private TaskAgingTypeEum taskAgingTypeEum;
}
