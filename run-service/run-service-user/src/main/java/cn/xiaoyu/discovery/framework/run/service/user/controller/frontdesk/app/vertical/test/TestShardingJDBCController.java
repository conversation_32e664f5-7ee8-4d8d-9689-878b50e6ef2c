package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.test;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.activity.RedPacketSendTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.activity.vo.req.FindAvailableRedPacketReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityRedPacketResp;
import cn.xiaoyu.discovery.framework.run.service.user.mapper.activity.ActivityRedPacketPickUpRecordMapper;
import cn.xiaoyu.discovery.framework.run.service.user.mapper.user.UserMapper;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityRedPacketPickUpRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.activity.ActivityRedPacketService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.logs.UserRegLogService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@RequestMapping(TestShardingJDBCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.用户模块分表测试", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + TestShardingJDBCController.MODULE_PREFIX + "模块-用户模块分表测试")
public class TestShardingJDBCController implements IModulePrefix {

    private final UserRegLogService userRegLogService;
    private final ActivityRedPacketPickUpRecordService activityRedPacketPickUpRecordService;
    private final ActivityRedPacketPickUpRecordMapper activityRedPacketPickUpRecordMapper;
    private final UserService userService;
    private final UserMapper userMapper;
    private final ActivityRedPacketService activityRedPacketService;

    @SaIgnore
    @PostMapping("/testUserTBs/{platformId}")
    @ApiOperation("分表测试")
    @ApiOperationSupport(order = 2)
    public R<Boolean> testUserTBs(@PathVariable Long platformId, Long startTime, Long endTime, Long deleteId,
        Long deleteId_2, String idS, String idS_2, Long delete_id, Long delete_createTime, Long userId) {
        PlatformIdContextHolder.setPlatformId(String.valueOf(platformId));

        FindAvailableRedPacketReq queryReq = FindAvailableRedPacketReq.builder()
            .sendTypeEum(RedPacketSendTypeEum.COMMON).userId(userId).time(System.currentTimeMillis()).build();
        List<ActivityRedPacketResp> normalRedPacketList = activityRedPacketService.findNormalRedPacket(queryReq);
        // System.out.println("查询用来注册来源");
        // List<UserRegLogDto> regList = userRegLogService.findUserByLog();

        // System.out.println("根据时间区间以及区间筛选用户注册信息");
        // List<RegChannelResp> list = userRegLogService.getChannelRegData(startTime, endTime, null);
        // System.out.println(JSON.toJSONString(list));
        // System.out.println("获取用户注册数");
        // PlatformInfoReq platformInfoReq = new PlatformInfoReq();
        // platformInfoReq.setStartCreateTime(startTime);
        // platformInfoReq.setEndCreateTime(endTime);

        // System.out.println("根据时间插入到指定分表测试");
        // ActivityRedPacketPickUpRecordEntity entity = new ActivityRedPacketPickUpRecordEntity();
        // entity.setId(IdService.getNextId());
        // entity.setReceiveStatusEum(ReceiveStatusEum.FAILED);
        // entity.setTitle("插入到指定表测试");
        // entity.setCreateTime(startTime);
        // activityRedPacketPickUpRecordService.createEntity(entity);

        // try {
        // System.out.println("按平台分表--单个ID删除 （Service.removeById）");
        // userService.removeById(deleteId);
        // } catch (Exception e) {
        // e.printStackTrace();
        // System.out.println("删除失败 userService.removeById");
        // }
        //
        // try {
        // System.out.println("按平台分表--单个ID删除 （Mapper.deleteById）");
        // userMapper.deleteById(deleteId_2);
        // } catch (Exception e) {
        // e.printStackTrace();
        // System.out.println("删除失败 userService.deleteById");
        // }
        //
        // try {
        // if (StringUtils.isNotBlank(idS)) {
        // System.out.println("按平台分表--ID批量删除 （removeByIds）");
        // userService.removeByIds(List.of(idS.split(",")));
        // }
        // } catch (Exception e) {
        // e.printStackTrace();
        // System.out.println("删除失败 userService.removeByIds");
        // }
        //
        // try {
        // if (StringUtils.isNotBlank(idS_2)) {
        // System.out.println("按平台分表--ID批量删除 （Mapper.deleteBatchIds）");
        // userMapper.deleteBatchIds(List.of(idS_2.split(",")));
        // }
        // } catch (Exception e) {
        // e.printStackTrace();
        // System.out.println("删除失败 userMapper.deleteBatchIds");
        // }
        //
        // System.out.println("删除 平台 + create_time 分表数据");
        // try {
        // activityRedPacketPickUpRecordService.deleteData(new DateDto(startTime, endTime, ""));
        // } catch (Exception e) {
        // e.printStackTrace();
        // System.out.println("删除失败 deleteData");
        // }
        //
        // try {
        // System.out.println("单条数据--精准删除 （id + 时间）");
        // activityRedPacketPickUpRecordService
        // .deleteByIdAndCreateTime(new DeleteByIdAndCreateTimeReq(delete_id, delete_createTime));
        // } catch (Exception e) {
        // e.printStackTrace();
        // System.out.println("删除失败 deleteByIdAndCreateTime");
        // }
        return R.success();
    }

}
