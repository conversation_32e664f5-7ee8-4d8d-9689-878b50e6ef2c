package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth;

import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.Validator;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.context.HeaderDTO;
import cn.xiaoyu.discovery.framework.run.common.context.utils.ContextDTOUtils;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.eum.EquipmenTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.SystemTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.TerminalTypeEum;
import cn.xiaoyu.discovery.framework.run.common.exception.BizException;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserEmailCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserLoginReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserPhoneCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserRegisterReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserGraphicCaptchaResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserLoginResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserRegisterConfigResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.UserConvert;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.ctrl.SwitchConvert;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.ctrl.UserRegControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.ctrl.UserRegSwitchControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.LoginTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.auth.UserLoginService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.auth.UserRegisterService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.captcha.UserCaptchaService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.ctrl.UserRegControlService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.ctrl.UserRegSwitchControlService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@RequestMapping(UserRegisVerticalController.MODULE_PREFIX)
@Api(value = "1.用户注册", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + UserRegisVerticalController.MODULE_PREFIX + "模块-竖版app用户注册操作")
public class UserRegisVerticalController implements IModulePrefix {

    private final UserRegisterService userRegisterService;
    private final UserRegControlService userRegControlService;
    private final UserRegSwitchControlService userRegSwitchControlService;
    private final UserCaptchaService userCaptchaService;
    private final UserLoginService userLoginService;

    @PostMapping("/getRegisterConfig")
    @ApiOperation("1.获取注册界面配置信息")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    public R<UserRegisterConfigResp> getRegisterConfig() {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();

        UserRegSwitchControlEntity entity =
            this.userRegSwitchControlService.findByTerminalTypeAndSystemType(terminalTypeEum, systemTypeEum);
        UserRegisterConfigResp resp = SwitchConvert.INSTANCE.convertRegisterConfigResp(entity);

        UserRegControlEntity userRegControlEntity =
            userRegControlService.findByTerminalTypeAndSystemType(terminalTypeEum, systemTypeEum);
        if (Objects.nonNull(userRegControlEntity)) {
            resp.setBackgroundAddress(userRegControlEntity.getBackgroundAddress());
            resp.setPrivacyAgreement(userRegControlEntity.getPrivacyAgreement());
            // resp.setPhoneDigitsMinLimit(userRegControlEntity.getPhoneDigitsMinLimit());
            // resp.setPhoneDigitsMaxLimit(userRegControlEntity.getPhoneDigitsMaxLimit());
        }
        return R.success(resp);
    }

    @PostMapping("/getGraphicCaptcha")
    @ApiOperation("2.开启简单图形验证码则获取图形验证码")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    public R<UserGraphicCaptchaResp> getGraphicCaptcha(HttpServletRequest request) {
        String sessionId = request.getSession().getId();
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        return R.success(this.userCaptchaService.getRegGraphicCaptcha(sessionId, terminalTypeEum, systemTypeEum));
    }

    @PostMapping("/sendEmailCaptcha")
    @ApiOperation("3.发送邮箱验证码")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<Void> sendEmailCaptcha(@RequestBody @Valid UserEmailCaptchaReq req) {
        if (!Validator.isEmail(req.getEmail())) {
            throw new BizException(UserCodeException.EMAIL_FORMAT_IS_NOT_CORRECT);
        }
        // 判断邮箱重复
        userRegisterService.checkEmail(req.getEmail());
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        this.userCaptchaService.sendRegEmailCaptcha(req.getEmail(), terminalTypeEum, systemTypeEum);
        return R.success();
    }

    @PostMapping("/sendPhoneCaptcha")
    @ApiOperation("4.发送手机验证码")
    @ApiOperationSupport(order = 4)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<Void> sendPhoneCaptcha(@RequestBody @Valid UserPhoneCaptchaReq req) {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        this.userCaptchaService.sendRegPhoneCaptcha(req.getPhonePrefix(), req.getPhone(), terminalTypeEum,
            systemTypeEum);
        return R.success();
    }

    @PostMapping("/register")
    @ApiOperation("5.注册用户")
    @ApiOperationSupport(order = 5)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<UserLoginResp> register(@RequestBody @Valid UserRegisterReq req) {
        HeaderDTO headerDTO = ContextDTOUtils.getHeaderDTO();
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        EquipmenTypeEum equipmenTypeEum = ContextDTOUtils.getEquipmenTypeEum();
        Long userCode =
            this.userRegisterService.register(req, terminalTypeEum, systemTypeEum, equipmenTypeEum, headerDTO);
        UserLoginResp loginResp = null;
        if (Objects.nonNull(userCode)) {
            UserLoginReq loginReq = UserConvert.INSTANCE.convertUserRegReqToLoginReq(req);
            switch (req.getRegTypeEum()) {
                case REG_USERNAME:
                    loginReq.setLoginTypeEum(LoginTypeEum.LOGIN_USERNAME);
                    break;
                case REG_EMAIL:
                    loginReq.setLoginTypeEum(LoginTypeEum.LOGIN_EMAIL);
                    break;
                case REG_PHONE:
                    loginReq.setLoginTypeEum(LoginTypeEum.LOGIN_PHONE_NUM);
                    break;
                case REG_DEVICE_NO:
                    loginReq.setLoginTypeEum(LoginTypeEum.LOGIN_DEVICE_ID);
                    break;
                default:
                    break;
            }
            // 登录的国家
            loginReq.setLoginClcppzEum(req.getRegClcppzEum());
            loginResp =
                userLoginService.loginAfterReg(loginReq, terminalTypeEum, systemTypeEum, equipmenTypeEum, headerDTO);
        }
        return R.success(loginResp);
    }
}
