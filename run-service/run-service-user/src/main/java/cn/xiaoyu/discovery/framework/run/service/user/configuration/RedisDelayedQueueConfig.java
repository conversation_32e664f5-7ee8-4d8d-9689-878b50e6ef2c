package cn.xiaoyu.discovery.framework.run.service.user.configuration;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import cn.xiaoyu.discovery.framework.run.service.system.reids.RedisDelayedQueueListener;
import lombok.extern.slf4j.Slf4j;

/**
 * redisson队列初始化
 *
 * <AUTHOR>
 * @date 2023/4/9
 */
@Component
@Slf4j
public class RedisDelayedQueueConfig implements ApplicationContextAware {
    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取应用上下文并获取相应的接口实现类
     *
     * @param applicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        Map<String, RedisDelayedQueueListener> map = applicationContext.getBeansOfType(RedisDelayedQueueListener.class);
        for (Map.Entry<String, RedisDelayedQueueListener> taskEventListenerEntry : map.entrySet()) {
            startThread(taskEventListenerEntry.getValue());
        }
    }

    /**
     * 启动线程获取队列*
     *
     * @param redisDelayedQueueListener 任务回调监听
     * @param <T> 泛型
     * @return
     */
    private <T> void startThread(RedisDelayedQueueListener redisDelayedQueueListener) {
        if (StringUtils.isBlank(redisDelayedQueueListener.getQueueName())) {
            log.error("未配置队列名称[{}]", redisDelayedQueueListener.getClass().getName());
            return;
        }
        RBlockingQueue<T> blockingFairQueue = redissonClient.getBlockingQueue(redisDelayedQueueListener.getQueueName());
        CompletableFuture.runAsync(() -> {
            while (true) {
                try {
                    if (redissonClient.isShutdown()) {
                        break;
                    }
                    try {
                        if (blockingFairQueue.isEmpty()) {
                            Thread.sleep(1000);
                            log.info("延时队列-{}-为空,{}", blockingFairQueue.getName(), blockingFairQueue.getCodec());
                        }
                    } catch (InterruptedException ex) {
                        log.error(ex.getMessage());
                    }
                    T t = blockingFairQueue.take();
                    if (null == t) {
                        log.info("延时队列-{}-获取的数据为null,{}", blockingFairQueue.getName(), blockingFairQueue.getCodec());
                        return;
                    }
                    redisDelayedQueueListener.invoke(t);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        });
        log.info("延迟队列监听启动成功[{}-{}.class]", redisDelayedQueueListener.getQueueName(),
            redisDelayedQueueListener.getClass().getSimpleName());
    }
}
