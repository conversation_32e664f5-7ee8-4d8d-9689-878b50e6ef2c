package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.member.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;

/**
 * 查询团队打码信息Resp
 * <AUTHOR>
 * @since 2024/2/27
 */
@ApiModel("查询团队打码信息Resp")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryTeamBetInfoResp implements IResp {

    private static final long serialVersionUID = 1833780387606720073L;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long id;

    /**
     * 用户编码
     */
    @ApiModelProperty("用户编码")
    private Long userCode;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 团队人数
     */
    @ApiModelProperty("团队人数")
    private Integer memberCount = NumberUtils.INTEGER_ZERO;

    /**
     * 团队打码人数
     */
    @ApiModelProperty("团队打码人数")
    private Integer betCount = NumberUtils.INTEGER_ZERO;

    /**
     * 团队打码金额
     */
    @ApiModelProperty("团队打码金额")
    private BigDecimal betAmount = BigDecimal.ZERO;

    /**
     * 团队充值人数
     */
    @ApiModelProperty("团队充值人数")
    private Integer rechargeCount = NumberUtils.INTEGER_ZERO;

    /**
     * 团队充值金额
     */
    @ApiModelProperty("团队充值金额")
    private BigDecimal rechargeAmount = BigDecimal.ZERO;
}
