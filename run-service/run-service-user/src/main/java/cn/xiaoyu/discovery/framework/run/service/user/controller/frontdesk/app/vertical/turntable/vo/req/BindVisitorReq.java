package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CountryLanguageCurrencyPhonePrefixZoneidEum;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@ApiModel("绑定游客账号Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BindVisitorReq implements IReq {

    private static final long serialVersionUID = 5936485048183918816L;

    @ApiModelProperty(value = "手机号前缀 配置手机号展示时必传")
    private String phonePrefix;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    @NotNull(message = UserCodeException.PHONE_IS_NULL_I18N_CODE)
    private String phone;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码 除了设备号注册外必传")
    @NotNull(message = UserCodeException.PASSWORD_IS_NULL_I18N_CODE)
    private String password;

    @ApiModelProperty(value = "注册的国家枚举", required = true)
    @NotNull(message = UserCodeException.COUNTRY_IS_NULL_I18N_CODE)
    private CountryLanguageCurrencyPhonePrefixZoneidEum regClcppzEum;

    /**
     * 游客ID
     */
    @ApiModelProperty(value = "游客ID")
    private Long visitorId;
}
