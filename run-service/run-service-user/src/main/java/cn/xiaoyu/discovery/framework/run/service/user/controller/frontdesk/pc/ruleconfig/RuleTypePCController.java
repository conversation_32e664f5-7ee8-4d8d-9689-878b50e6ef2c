package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.pc.ruleconfig;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp.RebateGameRateResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.ruleconfig.vo.req.FindRuleTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.ruleconfig.vo.resp.RuleTypeInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.agent.team.AgentRebateGameSettingService;
import cn.xiaoyu.discovery.framework.run.service.user.service.ruleconfig.RuleConfigService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.PcController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 前端-PC规则配置信息
 * @date 2024/1/22
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@PcController
@ApiSort(value = 1)
@RequestMapping(RuleTypePCController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.规则配置信息",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + RuleTypePCController.MODULE_PREFIX + "模块-PC规则配置信息")
public class RuleTypePCController implements IModulePrefix {
    private final RuleConfigService ruleConfigService;
    private final AgentRebateGameSettingService agentRebateGameSettingService;

    @PostMapping("/getRuleConfig")
    @ApiOperation("1.获取规则配置信息")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<List<RuleTypeInfoResp>> getRuleConfig(@RequestBody FindRuleTypeReq req) {
        return R.success(this.ruleConfigService.findByRuleTypeEumList(req.getRuleTypeEumList()));
    }

    @PostMapping("/getRebateGameSettingList")
    @ApiOperation("2.获取游戏返佣比例")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    public R<List<RebateGameRateResp>> getRebateGameSettingList() {
        return R.success(this.agentRebateGameSettingService.getRebateGameSettingList());
    }
}
