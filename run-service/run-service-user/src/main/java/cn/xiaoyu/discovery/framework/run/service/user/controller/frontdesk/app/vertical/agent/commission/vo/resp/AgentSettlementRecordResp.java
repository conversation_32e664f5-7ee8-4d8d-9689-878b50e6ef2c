package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/27 20:09
 * @Description
 */
@Data
@ApiModel("佣金记录Resp")
public class AgentSettlementRecordResp implements IResp {
    private static final long serialVersionUID = 4742430802013016448L;

    @ApiModelProperty(value = "用户code")
    private Long userCode;
    @ApiModelProperty("子游戏")
    private GameSubTypeEum gameSubTypeEum;
    @ApiModelProperty("总投注额")
    private BigDecimal totalBetAmount;
    @ApiModelProperty("总返佣额")
    private BigDecimal totalCommissionAmount;
    @ApiModelProperty("日结日期")
    private String endDay;

}
