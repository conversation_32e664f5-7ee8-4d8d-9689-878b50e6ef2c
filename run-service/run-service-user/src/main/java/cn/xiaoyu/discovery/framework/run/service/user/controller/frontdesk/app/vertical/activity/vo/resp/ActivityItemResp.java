package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.activity.ActivityTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("活动模型Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityItemResp extends IdResp {
    private static final long serialVersionUID = 2877498166728215027L;

    @ApiModelProperty(value = "活动标题")
    private String title;

    @ApiModelProperty(value = "h5/app活动封面")
    @ReplaceDomainName
    private String h5Picture;

    @ApiModelProperty(value = "pc活动封面")
    @ReplaceDomainName
    private String pcPicture;

    @ApiModelProperty(value = "点击是否显示富文本")
    private Boolean clickShowTexter;

    @ApiModelProperty(value = "点击跳转地址-clickShowTexter为false使用")
    private String clickJumpUrl;

    @ApiModelProperty(value = "活动类型")
    private ActivityTypeEum activityTypeEum;
}
