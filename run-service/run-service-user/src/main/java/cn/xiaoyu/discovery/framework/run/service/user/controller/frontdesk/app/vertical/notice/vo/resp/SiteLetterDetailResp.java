package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 站内信通知详细Resp
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("站内信通知详细Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SiteLetterDetailResp extends IdResp {

    private static final long serialVersionUID = 4259172191493166497L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    private NoticeTypeEum noticeTypeEum;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 定时发送时间
     */
    @ApiModelProperty(value = "定时发送时间")
    @JacksonZoneId
    private Long sendTime;

    /**
     * 到期时间
     */
    @ApiModelProperty(value = "到期时间")
    @JacksonZoneId
    private Long expireTime;

    /**
     * 是否显示小红点
     */
    @ApiModelProperty(value = "是否显示小红点")
    private Boolean redPoint;
}
