package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotEmpty;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("获取邮箱验证码Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserEmailCaptchaReq implements IReq {
    private static final long serialVersionUID = -7043988345614539104L;

    @ApiModelProperty(value = "邮箱号", required = true)
    @NotEmpty(message = UserCodeException.EMAIL_IS_NULL_I18N_CODE)
    // todo 加上验证合法邮箱验证
    private String email;

}
