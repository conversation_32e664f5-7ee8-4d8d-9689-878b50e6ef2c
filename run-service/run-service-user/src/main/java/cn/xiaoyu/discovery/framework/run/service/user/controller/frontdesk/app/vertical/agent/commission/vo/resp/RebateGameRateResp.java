package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp;

import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.GameCategoryTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 前端-游戏返佣比例Resp
 * @date 2024/3/1
 */
@ApiModel("游戏返佣比例Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RebateGameRateResp implements IResp {
    private static final long serialVersionUID = 2249916014307765820L;

    @ApiModelProperty(value = "大类")
    private GameCategoryTypeEum categoryTypeEum;

    @ApiModelProperty(value = "详情")
    private List<RebateGameRateItemResp> list;

}
