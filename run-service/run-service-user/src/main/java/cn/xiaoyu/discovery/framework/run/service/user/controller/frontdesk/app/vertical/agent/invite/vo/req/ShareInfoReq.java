package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.PromotionSourceTypeEum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShareInfoReq implements IReq {
    private static final long serialVersionUID = 6859794182130271015L;

    @ApiModelProperty(value = "推广活动类型来源")
    private PromotionSourceTypeEum promotionSourceTypeEum;

    @ApiModelProperty(value = "未登陆下的用户编码")
    private Long userCode;
}
