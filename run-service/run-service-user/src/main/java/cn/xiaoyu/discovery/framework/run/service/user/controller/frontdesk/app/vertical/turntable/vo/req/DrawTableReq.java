package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CountryLanguageCurrencyPhonePrefixZoneidEum;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@ApiModel("转动转盘Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DrawTableReq implements IReq {

    private static final long serialVersionUID = 4155817541396227161L;

    @ApiModelProperty(value = "注册的国家枚举", required = true)
    @NotNull(message = UserCodeException.COUNTRY_IS_NULL_I18N_CODE)
    private CountryLanguageCurrencyPhonePrefixZoneidEum regClcppzEum;
}
