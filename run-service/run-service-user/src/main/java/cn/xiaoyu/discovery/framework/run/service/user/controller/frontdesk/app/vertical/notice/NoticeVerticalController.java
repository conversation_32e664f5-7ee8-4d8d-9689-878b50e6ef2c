package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.PageReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.req.NoticeTypeAllReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.req.NoticeTypeIdReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.req.NoticeTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp.AnnounceBaseResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp.AnnounceDetailResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp.NoticeUnReadInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp.ServiceMessageDetailResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp.SiteLetterDetailResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.notice.NoticeAnnounceService;
import cn.xiaoyu.discovery.framework.run.service.user.service.notice.NoticeServiceMessageService;
import cn.xiaoyu.discovery.framework.run.service.user.service.notice.NoticeSiteLetterService;
import cn.xiaoyu.discovery.framework.run.service.user.service.notice.NoticeVerticalService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(NoticeVerticalController.MODULE_PREFIX)
@Api(value = "1.通知", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + NoticeVerticalController.MODULE_PREFIX
    + "模块-竖版app通知操作")
public class NoticeVerticalController implements IModulePrefix {

    private final NoticeVerticalService noticeVerticalService;
    private final NoticeAnnounceService noticeAnnounceService;
    private final NoticeServiceMessageService noticeServiceMessageService;
    private final NoticeSiteLetterService noticeSiteLetterService;

    @PostMapping("/getAnnounceForHome")
    @ApiOperation("1.首页跑马灯公告显示")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    public R<List<AnnounceBaseResp>> getAnnounceForHome() {
        return R.success(noticeAnnounceService.getAnnounceBaseList());
    }

    @PostMapping("/getNoticeUnReadInfo")
    @ApiOperation("2.通知界面按通知类型显示未读消息数和小红点")
    @ApiOperationSupport(order = 2)
    @Encrypt
    public R<List<NoticeUnReadInfoResp>> getNoticeUnReadInfo() {
        return R.success(noticeVerticalService.getNoticeUnReadInfo());
    }

    @PostMapping("/getAnnouncePage")
    @ApiOperation("3.通知界面查询公告通知列表")
    @ApiOperationSupport(order = 3)
    @Encrypt
    @Decrypt
    public R<PageResp<AnnounceDetailResp>> getAnnouncePage(@RequestBody @Valid PageReq req) {
        return R.success(noticeAnnounceService.getAnnouncePage(req));
    }

    @PostMapping("/getServiceMessagePage")
    @ApiOperation("4.通知界面查询服务通知列表")
    @ApiOperationSupport(order = 4)
    @Encrypt
    @Decrypt
    public R<PageResp<ServiceMessageDetailResp>> getServiceMessagePage(@RequestBody @Valid PageReq req) {
        return R.success(noticeServiceMessageService.getServiceMessagePage(req));
    }

    @PostMapping("/getSiteLetterPage")
    @ApiOperation("5.通知界面查询站内信通知列表")
    @ApiOperationSupport(order = 5)
    @Encrypt
    @Decrypt
    public R<PageResp<SiteLetterDetailResp>> getSiteLetterPage(@RequestBody @Valid PageReq req) {
        return R.success(noticeSiteLetterService.getSiteLetterPage(req));
    }

    @PostMapping("/readNotice")
    @ApiOperation("6.读取通知")
    @ApiOperationSupport(order = 6)
    @Encrypt
    @Decrypt
    public R<Boolean> readNotice(@RequestBody @Valid NoticeTypeIdReq req) {
        req.setPlatformId(Long.valueOf(PlatformIdContextHolder.getPlatformId()));
        req.setUserId(FrontdeskUserInfoUtils.getUserId());
        return R.success(noticeVerticalService.readNotice(req));
    }

    @PostMapping("/readAllNotice")
    @ApiOperation("7.全部读取")
    @ApiOperationSupport(order = 7)
    @Encrypt
    @Decrypt
    public R<Boolean> readAllNotice(@RequestBody @Valid NoticeTypeAllReq req) {
        return R.success(noticeVerticalService.readAllNotice(req));
    }

    @PostMapping("/deleteNotice")
    @ApiOperation("8.删除通知")
    @ApiOperationSupport(order = 8)
    @Encrypt
    @Decrypt
    public R<Boolean> deleteNotice(@RequestBody @Valid NoticeTypeIdReq req) {
        return R.success(noticeVerticalService.deleteNotice(req));
    }

    @PostMapping("/clearAllNotice")
    @ApiOperation("10.清空")
    @ApiOperationSupport(order = 10)
    @Encrypt
    @Decrypt
    public R<Boolean> clearAllNotice(@RequestBody @Valid NoticeTypeReq req) {
        return R.success(noticeVerticalService.clearAllNotice(req));
    }
}
