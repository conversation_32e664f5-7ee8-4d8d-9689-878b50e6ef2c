package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.user.eum.prizepool.PrizePoolTypeEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 奖池列表Resp
 *
 * <AUTHOR>
 * @since 2023/12/6
 */
@Data
public class PrizePoolPeriodListResp implements IResp {

    private static final long serialVersionUID = -9104522047757700321L;

    @ApiModelProperty("奖池ID")
    private Long id;
    @ApiModelProperty("期数")
    private String period;
    @ApiModelProperty(value = "奖池类型")
    private PrizePoolTypeEum prizePoolTypeEum;
    @ApiModelProperty(value = "奖池金额")
    private BigDecimal prizePoolAmount;
    @ApiModelProperty(value = "投币下限")
    private BigDecimal globalCoinStart;
    @ApiModelProperty(value = "投币上限")
    private BigDecimal globalCoinLimit;
    @ApiModelProperty(value = "投币开始时间")
    @JacksonZoneId
    private Long putInStartTime;
    @ApiModelProperty(value = "投币结束时间")
    @JacksonZoneId
    private Long putInEndTime;
    @ApiModelProperty(value = "投币结束时间戳")
    private Long putInEndTimeStamp;
    @ApiModelProperty(value = "自身投币")
    private BigDecimal minePutInCoin = BigDecimal.ZERO;
    @ApiModelProperty(value = "参与人数")
    private Integer putInPerson = 0;
    @ApiModelProperty(value = "总投币")
    private BigDecimal totalPutInCoin = BigDecimal.ZERO;

    @ApiModelProperty(value = "奖池图标-客户端展示")
    @ReplaceDomainName
    private String iconUrl;
}
