package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import java.math.BigDecimal;
import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("转盘信息Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTurnPlateInfoResp implements IResp {
    private static final long serialVersionUID = 5613506107460443188L;

    @ApiModelProperty(value = "当日已充值")
    private BigDecimal todayRechargeAmount;

    @ApiModelProperty(value = "当日已投注")
    private BigDecimal todayBetAmount;

    @ApiModelProperty(value = "黄金奖品list")
    private List<ActivityTurnPlateAwardResp> goldAwardList;

    @ApiModelProperty(value = "黄金规则描述")
    private String goldRulesDesc;

    @ApiModelProperty(value = "白金奖品list")
    private List<ActivityTurnPlateAwardResp> platinumAwardList;

    @ApiModelProperty(value = "白金规则描述")
    private String platinumRulesDesc;

    @ApiModelProperty(value = "钻石奖品list")
    private List<ActivityTurnPlateAwardResp> diamondAwardList;

    @ApiModelProperty(value = "钻石规则描述")
    private String diamondRulesDesc;
}
