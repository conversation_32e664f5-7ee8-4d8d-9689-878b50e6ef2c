package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req;

import javax.validation.constraints.NotEmpty;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.common.exception.CommonCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("获取手机验证码Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPhoneCaptchaReq implements IReq {
    private static final long serialVersionUID = -846519536783808984L;

    @ApiModelProperty(value = "手机前缀", required = true)
    @NotEmpty(message = CommonCodeException.VALUE_IS_NULL_I18N_CODE)
    private String phonePrefix;

    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = UserCodeException.PHONE_IS_NULL_I18N_CODE)
    private String phone;
}
