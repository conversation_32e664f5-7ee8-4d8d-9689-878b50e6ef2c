package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task;

import java.util.List;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.task.vo.resp.TaskCategoryResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.req.TaskListReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.req.TaskReceiveReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.req.TaskTypeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.resp.TaskAwardRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.task.vo.resp.TaskListResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.task.TaskConvert;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskAgingTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.HierarchicalValidationTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.service.task.TaskBusinessService;
import cn.xiaoyu.discovery.framework.run.service.user.service.task.TaskDistributeRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.task.TaskService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.hierarchical.UserHierarchicalBizService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 2)
@RequestMapping(TaskVerticalController.MODULE_PREFIX)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@Api(value = "1.任务管理", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + TaskVerticalController.MODULE_PREFIX
    + "模块-竖版app任务管理操作")
public class TaskVerticalController implements IModulePrefix {

    private final TaskService taskService;
    private final TaskBusinessService taskBusinessService;
    private final TaskDistributeRecordService taskDistributeRecordService;
    private final UserHierarchicalBizService userHierarchicalBizService;

    @PostMapping("/getTaskAgingTypeList")
    @ApiOperation("1.获取任务时效类型列表")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Encrypt
    public R<PageResp<TaskAgingTypeEum>> getTaskAgingTypeList() {
        List<TaskAgingTypeEum> list = taskService.getTaskAgingTypeList();
        return R.success(new PageResp<>(list, (long)list.size()));
    }

    @PostMapping("/getTaskTypeList")
    @ApiOperation("2.获取任务类型列表")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<PageResp<TaskTypeEum>> getTaskTypeList(@RequestBody @Valid TaskTypeReq req) {
        List<TaskTypeEum> list = taskService.getTaskTypeList(req);
        return R.success(new PageResp<>(list, (long)list.size()));
    }

    @PostMapping("/getTaskList")
    @ApiOperation("3.获取任务列表")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<TaskListResp> getTaskList(@RequestBody @Valid TaskListReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        return R.success(
            taskBusinessService.getTaskList(req, userId, Long.valueOf(PlatformIdContextHolder.getPlatformId())));
    }

    @PostMapping("/receiveTaskAward")
    @ApiOperation("4.领取任务奖励")
    @ApiOperationSupport(order = 4)
    @Encrypt
    @Decrypt
    public R<Boolean> receiveTask(@RequestBody TaskReceiveReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.TASK);
        return R.success(taskBusinessService.receiveTaskAward(req, userId));
    }

    @PostMapping("/getTaskAwardRecord")
    @ApiOperation("5.任务奖励领取记录")
    @ApiOperationSupport(order = 5)
    @Encrypt
    public R<PageResp<TaskAwardRecordResp>> getTaskAwardRecord() {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        List<TaskAwardRecordResp> list =
            TaskConvert.INSTANCE.convertRecordResp(taskDistributeRecordService.findByUserId(userId));
        return R.success(new PageResp<>(list, (long)list.size()));
    }

    @PostMapping("/autoDrawTaskAward")
    @ApiOperation("6.一键领取任务奖励")
    @ApiOperationSupport(order = 6)
    @Encrypt
    @Decrypt
    public R<Boolean> autoDrawTaskAward(@RequestBody @Valid TaskListReq req) {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.TASK);
        return R.success(taskBusinessService.autoDrawTaskAward(req, userId));
    }

    @PostMapping("/getLimitGames")
    @ApiOperation("7.获取指定游戏")
    @ApiOperationSupport(order = 7)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<List<TaskCategoryResp>> getLimitGames(@RequestBody @Valid TaskReceiveReq req) {
        return R.success(taskBusinessService.getLimitGames(req));
    }

}
