package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ActivityDownloadGiftDetailResp implements IResp {
    private static final long serialVersionUID = -5688694096306874838L;

    @ApiModelProperty(value = "奖励列表")
    private List<ActivityDownloadGiftItemResp> list;

    @ApiModelProperty(value = "详情页顶部展示图")
    @ReplaceDomainName
    private String detailImgUrl;

    @ApiModelProperty(value = "app规则描述")
    private String appText;

    @ApiModelProperty(value = "h5规则描述")
    private String h5Text;

    @ApiModelProperty(value = "未登录规则描述")
    private String noLoginText;

    @ApiModelProperty(value = "领奖需要满足历史充值额")
    private BigDecimal needHistoryRechargeAmount;

    @ApiModelProperty(value = "领奖需要满足注册天数")
    private Integer needRegDay;
}
