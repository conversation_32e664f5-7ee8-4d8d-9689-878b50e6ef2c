package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/7/17
 */

@Data
@Builder
public class TurnPlatePlayEnableResp implements IResp {
    private static final long serialVersionUID = -3825127065518689245L;

    @ApiModelProperty(value = "金转盘可转次数")
    private Integer goldPlayNum;

    @ApiModelProperty(value = "白金转盘可转次数")
    private Integer platinumPlayNum;

    @ApiModelProperty(value = "钻石转盘可转次数")
    private Integer diamondPlayNum;

}
