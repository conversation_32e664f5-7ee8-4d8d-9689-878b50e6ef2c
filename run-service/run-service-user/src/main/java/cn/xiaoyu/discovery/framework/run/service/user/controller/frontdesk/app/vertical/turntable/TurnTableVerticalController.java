package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.req.BindVisitorReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.req.DrawTableReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp.TurnTableControlResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp.TurnTableInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp.TurnTableInviteRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp.TurnTableRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserLoginResp;
import cn.xiaoyu.discovery.framework.run.service.user.eum.user.HierarchicalValidationTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.service.turntable.TurnTableBizService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.hierarchical.UserHierarchicalBizService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(TurnTableVerticalController.MODULE_PREFIX)
@Api(value = "1.裂变转盘", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + TurnTableVerticalController.MODULE_PREFIX + "模块-竖版app裂变转盘操作")
public class TurnTableVerticalController implements IModulePrefix {

    private final UserHierarchicalBizService userHierarchicalBizService;
    private final TurnTableBizService turnTableBizService;

    @PostMapping("/control")
    @ApiOperation("1.开关")
    @ApiOperationSupport(order = 1)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<TurnTableControlResp> control() {
        return R.success(turnTableBizService.control());
    }

    @PostMapping("/info")
    @ApiOperation("2.信息")
    @ApiOperationSupport(order = 2)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<TurnTableInfoResp> info() {
        return R.success(turnTableBizService.info());
    }

    @PostMapping("/draw")
    @ApiOperation("3.摇奖")
    @ApiOperationSupport(order = 3)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<BigDecimal> draw(@RequestBody DrawTableReq req) {
        return R.success(turnTableBizService.draw(req));
    }

    @PostMapping("/receive")
    @ApiOperation("4.领取")
    @ApiOperationSupport(order = 4)
    @Decrypt
    @Encrypt
    public R<Boolean> receive() {
        Long userId = FrontdeskUserInfoUtils.getUserId();
        // 层级校验-是否可领取活动优惠
        userHierarchicalBizService.validationByUserId(userId, HierarchicalValidationTypeEum.ACTIVITY);
        turnTableBizService.receive();
        return R.success(Boolean.TRUE);
    }

    @PostMapping("/record")
    @ApiOperation("5.获奖记录")
    @ApiOperationSupport(order = 5)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<List<TurnTableRecordResp>> record() {
        return R.success(turnTableBizService.record());
    }

    @PostMapping("/inviteRecord")
    @ApiOperation("6.邀请记录")
    @ApiOperationSupport(order = 6)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<List<TurnTableInviteRecordResp>> inviteRecord() {
        return R.success(turnTableBizService.inviteRecord());
    }

    @PostMapping("/bind")
    @ApiOperation("7.游客账号绑定")
    @ApiOperationSupport(order = 6)
    @SaIgnore
    @Decrypt
    @Encrypt
    public R<UserLoginResp> bind(@RequestBody BindVisitorReq req) {
        return R.success(turnTableBizService.bind(req));
    }
}
