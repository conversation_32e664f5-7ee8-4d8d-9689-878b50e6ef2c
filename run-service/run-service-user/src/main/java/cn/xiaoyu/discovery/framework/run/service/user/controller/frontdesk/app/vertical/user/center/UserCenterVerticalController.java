package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.Validator;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.context.utils.ContextDTOUtils;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.eum.SystemTypeEum;
import cn.xiaoyu.discovery.framework.run.common.eum.TerminalTypeEum;
import cn.xiaoyu.discovery.framework.run.common.exception.BizException;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.api.user.vo.req.SaveSecurePasswordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserEmailCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.req.UserPhoneCaptchaReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp.UserInfoResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req.SetUserPasswordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req.UpdateUserInfoReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req.UpdateUserPasswordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.resp.UserInfoUpdateConfigResp;
import cn.xiaoyu.discovery.framework.run.service.user.convert.user.ctrl.SwitchConvert;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.ctrl.UserInfoSwitchControlEntity;
import cn.xiaoyu.discovery.framework.run.service.user.exception.UserCodeException;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.captcha.UserCaptchaService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.center.UserCenterService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.ctrl.UserInfoSwitchControlService;
import cn.xiaoyu.discovery.framework.run.service.user.utils.FrontdeskUserInfoUtils;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/1 21:15
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(UserCenterVerticalController.MODULE_PREFIX)
@Api(value = "1.用户信息", tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-"
    + UserCenterVerticalController.MODULE_PREFIX + "模块-竖版app用户中心操作")
public class UserCenterVerticalController implements IModulePrefix {

    private final UserCaptchaService userCaptchaService;
    private final UserCenterService userCenterService;
    private final UserInfoSwitchControlService userInfoSwitchControlService;

    @PostMapping("/getUserInfo")
    @ApiOperation("1.获取用户信息")
    @ApiOperationSupport(order = 1)
    @Encrypt
    public R<UserInfoResp> getUserInfo() {
        return R.success(userCenterService.getUserInfo(FrontdeskUserInfoUtils.getUserId()));
    }

    @PostMapping("/updateUserInfo")
    @ApiOperation("2.更新用户信息")
    @ApiOperationSupport(order = 2)
    @Encrypt
    @Decrypt
    public R<Boolean> updateUserInfo(@RequestBody UpdateUserInfoReq req) {
        return R.success(userCenterService.updateUser(req));
    }

    @PostMapping("/updateUserPassword")
    @ApiOperation("3.更新用户密码")
    @ApiOperationSupport(order = 3)
    @Encrypt
    @Decrypt
    public R<Boolean> updateUserPassword(@RequestBody UpdateUserPasswordReq req) {
        return R.success(userCenterService.updateUserPassword(req));
    }

    @PostMapping("/sendBindingEmailCaptcha")
    @ApiOperation("4.发送邮箱验证码")
    @ApiOperationSupport(order = 4)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Void> sendLoginEmailCaptcha(@RequestBody @Valid UserEmailCaptchaReq req) {
        if (!Validator.isEmail(req.getEmail())) {
            throw new BizException(UserCodeException.EMAIL_FORMAT_IS_NOT_CORRECT);
        }
        this.userCaptchaService.sendBindingEmailCaptcha(req.getEmail(), ContextDTOUtils.getTerminalTypeEum(),
            ContextDTOUtils.getSystemTypeEum());
        return R.success();
    }

    @PostMapping("/sendBindingPhoneCaptcha")
    @ApiOperation("5.发送手机验证码")
    @ApiOperationSupport(order = 5)
    @SaIgnore
    @Encrypt
    @Decrypt
    public R<Void> sendLoginPhoneCaptcha(@RequestBody @Valid UserPhoneCaptchaReq req) {
        if (StringUtils.isEmpty(req.getPhone())) {
            throw new BizException(UserCodeException.PHONE_IS_NULL);
        }
        this.userCaptchaService.sendBindingPhoneCaptcha(req.getPhonePrefix(), req.getPhone(),
            ContextDTOUtils.getTerminalTypeEum(), ContextDTOUtils.getSystemTypeEum());
        return R.success();
    }

    @PostMapping("/getBindingConfig")
    @ApiOperation("6.获取绑定开关配置信息")
    @ApiOperationSupport(order = 6)
    @Encrypt
    @SaIgnore
    public R<UserInfoUpdateConfigResp> getRegisterConfig() {
        TerminalTypeEum terminalTypeEum = ContextDTOUtils.getTerminalTypeEum();
        SystemTypeEum systemTypeEum = ContextDTOUtils.getSystemTypeEum();
        UserInfoSwitchControlEntity entity =
            this.userInfoSwitchControlService.findByTerminalTypeAndSystemType(terminalTypeEum, systemTypeEum);
        UserInfoUpdateConfigResp resp = SwitchConvert.INSTANCE.convertUserInfoConfigResp(entity);

        return R.success(resp);
    }

    @PostMapping("/submitSecurePassword")
    @ApiOperation("7.提交安全密码")
    @ApiOperationSupport(order = 6)
    @Encrypt
    @Decrypt
    public R<Boolean> submitSecurePassword(@RequestBody SaveSecurePasswordReq req) {
        this.userCenterService.submitSecurePassword(req);
        return R.success(true);
    }

    @PostMapping("/setUserPassword")
    @ApiOperation("8.设置用户登录密码")
    @ApiOperationSupport(order = 8)
    @Encrypt
    @Decrypt
    public R<Boolean> setUserPassword(@RequestBody SetUserPasswordReq req) {
        return R.success(userCenterService.setUserPassword(req, FrontdeskUserInfoUtils.getUserId()));
    }
}
