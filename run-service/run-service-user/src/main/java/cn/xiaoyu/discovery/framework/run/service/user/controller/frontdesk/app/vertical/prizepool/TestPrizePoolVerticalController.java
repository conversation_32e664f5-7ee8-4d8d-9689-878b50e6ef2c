package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollUtil;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.platform.context.PlatformIdContextHolder;
import cn.xiaoyu.discovery.framework.run.service.system.dto.DateDto;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.prizepool.vo.resp.GlobalCoinsSummaryResp;
import cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserRecallSettingsEntity;
import cn.xiaoyu.discovery.framework.run.service.user.service.prizepool.coin.GlobalCoinService;
import cn.xiaoyu.discovery.framework.run.service.user.service.sms.SmsRecordService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserRecallSettingsService;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.UserService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@AppVerticalController
@ApiSort(value = 1)
@RequestMapping(TestPrizePoolVerticalController.MODULE_PREFIX)
@Api(value = "奖池相关接口测试类")
public class TestPrizePoolVerticalController implements IModulePrefix {

    private final GlobalCoinService globalCoinService;

    // @PostMapping("/testUpdateGlobalCoins")
    // @ApiOperation("修改全球币账户测试")
    // public R<Boolean> testUpdateGlobalCoins(@RequestParam(name = "platformId", defaultValue = "2") String platformId,
    // CoinSourceTypeEum coinSourceTypeEum, InOrOutStatusEum inOrOutStatusEum, BigDecimal changeCoins, Long userId) {
    // PlatformIdContextHolder.setPlatformId(platformId);
    // UpdateGlobalCoinsReq req = new UpdateGlobalCoinsReq();
    // req.setChangeTypeEum(coinSourceTypeEum);
    // req.setInOrOutEum(inOrOutStatusEum);
    // req.setGlobalCoins(changeCoins);
    // req.setUserId(userId);
    // return R.success(globalCoinService.updateGlobalCoins(req));
    // }
    // private final GlobalCoinExchangeControlService globalCoinExchangeControlService;
    // private final RedissonClient redissonClient;
    // private final RedisTemplate redisTemplate;
    // private final RedisService redisService;
    // @PostMapping("/setExchangeLimit")
    // @ApiOperation("设置兑换控制测试")
    // public R<Boolean> setExchangeLimit(@RequestParam(name = "platformId", defaultValue = "2") String platformId,
    // @RequestParam(name = "changeNumber", defaultValue = "100") BigDecimal changeNumber, Integer methodNum) {
    // // if (methodNum == 0) {
    // // System.out.println("redissonClient.getMap");
    // // RMap<String, String> map = redissonClient.getMap("global:coins:getMap:expire");
    // // map.put(PrizePoolConstant.EXCHANGE_RATIO, Double.toString(0.1));
    // // map.put(PrizePoolConstant.TODAY_EXCHANGE, changeNumber.toString());
    // // map.expire(Duration.ofHours(24));
    // // } else if (methodNum == 1) {
    // // System.out.println("redissonClient.getMapCache");
    // // RMap<String, String> map = redissonClient.getMapCache("global:coins:getMapCache:expire");
    // // map.put(PrizePoolConstant.EXCHANGE_RATIO, Double.toString(0.1));
    // // map.put(PrizePoolConstant.TODAY_EXCHANGE, changeNumber.toString());
    // // map.expire(Duration.ofHours(24));
    // // } else if (methodNum == 2) {
    // // System.out.println("redissonClient.getMapCache for redisTemplate");
    // // String key = "global:coins:template:expire";
    // // RMap<String, String> map = redissonClient.getMapCache(key);
    // // map.put(PrizePoolConstant.EXCHANGE_RATIO, Double.toString(0.1));
    // // map.put(PrizePoolConstant.TODAY_EXCHANGE, changeNumber.toString());
    // // redisTemplate.expire(key, Duration.ofHours(24));
    // // }
    // String key = "global:coins:redistemplatemap:expire";
    // Map<String, Object> map = new HashMap<>();
    // map.put(PrizePoolConstant.EXCHANGE_RATIO, 1);
    // map.put(PrizePoolConstant.TODAY_EXCHANGE, 2);
    // redisService.setMap(key, map);
    // redisService.setExpire(key, 60, TimeUnit.SECONDS);
    // return R.success();
    // }
    //
    // @PostMapping("/validatedExchangeLimit")
    // @ApiOperation("校验兑换控制测试")
    // public R<Boolean> validatedExchangeLimit(Long changeNumber) {
    // RLock lock = redissonClient.getLock(globalCoinExchangeControlService.exchangeLimitCacheKey());
    // boolean result = false;
    // boolean locked = false;
    // try {
    // locked = lock.tryLock(5, TimeUnit.SECONDS);
    // if (locked) {
    // GlobalCoinsExchangeControlResp exchangeControlResp =
    // globalCoinExchangeControlService.getExchangeControl();
    // Double exchangeRatio = exchangeControlResp.getExchangeRatio();
    // if (exchangeRatio.compareTo(0D) == 0) {
    // // 兑换失败，功能暂未开放/兑换比例未配置，请联系客服！
    // throw new BizException(UserCodeException.GLOBAL_COIN_EXCHANGE_RATIO_EMPTY);
    // }
    // BigDecimal exchangeLimit = exchangeControlResp.getTodayExchangeLimit();
    // // 获取比例计算转换后的余额
    // double balance = changeNumber * exchangeControlResp.getExchangeRatio();
    //
    // if (exchangeLimit.compareTo(BigDecimal.valueOf(balance)) >= 0) {
    //
    // redisService.setMapItem(globalCoinExchangeControlService.entityMapName(),
    // PrizePoolConstant.TODAY_EXCHANGE,
    // exchangeLimit.subtract(BigDecimal.valueOf(balance)).toString());
    // System.out.println("满足基本兑换条件");
    // result = true;
    // } else {
    // System.out.println("兑换失败,可兑换额度不足");
    // result = false;
    // }
    // }
    // } catch (InterruptedException e) {
    // e.printStackTrace();
    // throw new BizException("系统繁忙，请稍后再试");
    // } finally {
    // if (locked) {
    // lock.unlock();
    // }
    // }
    // return result ? R.success() : R.failed(500, "兑换失败,可兑换额度不足");
    // }

    // private final PlatformFrameworkService platformFrameworkService;
    // private final GlobalCoinRecordService globalCoinRecordService;

    @PostMapping("/findGlobalCoinsSummary")
    @ApiOperation("报表=获取全球币摘要")
    public R<GlobalCoinsSummaryResp> findGlobalCoinsSummary(String platFormId, Long startTime, Long endTime) {
        PlatformIdContextHolder.setPlatformId(platFormId);
        DateDto dto = new DateDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        GlobalCoinsSummaryResp globalCoinsSummary = globalCoinService.findGlobalCoinsSummary(dto);
        return R.success(globalCoinsSummary);
    }

    private final UserRecallSettingsService recallSettingsService;
    private final SmsRecordService smsRecordService;
    private final UserService userService;

    @PostMapping("/getExpectRecallUsers")
    @ApiOperation("获取待召回人数 和已召回人数")
    public R<String> getExpectRecallUsers(String platFormId, Long recallId) {
        PlatformIdContextHolder.setPlatformId(platFormId);
        Long count = recallSettingsService.getExpectRecallUsers(recallId);

        UserRecallSettingsEntity settings = recallSettingsService.findById(recallId);
        List<Long> userIdList = smsRecordService.findUserIdByTime(settings.getRecallFirstTime(),
            settings.getRecallEndTime(), settings.getId());
        Long successRecallUsers = 0L;
        if (CollUtil.isNotEmpty(userIdList)) {
            // 统计召回用户数量
            successRecallUsers = userService.selectCountByLastLoginTime(userIdList, settings.getRecallFirstTime(),
                settings.getRecallEndTime());
        }
        return R.success("待召回人数：" + count + " 已召回人数：" + successRecallUsers);
    }

}
