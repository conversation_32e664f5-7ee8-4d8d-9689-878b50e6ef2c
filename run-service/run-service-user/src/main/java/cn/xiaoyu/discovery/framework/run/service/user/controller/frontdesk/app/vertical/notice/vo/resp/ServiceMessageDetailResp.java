package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.notice.ServiceMessageSubTypeEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.notice.ServiceMessageTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 服务通知详细Resp
 *
 * <AUTHOR>
 * @since 2023/3/29
 */
@ApiModel("服务通知详细Resp")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceMessageDetailResp extends IdResp {

    private static final long serialVersionUID = -2775288015476120348L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    private NoticeTypeEum noticeTypeEum;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 附加信息
     */
    @ApiModelProperty(value = "附加信息")
    private String attachInfo;

    /**
     * 服务发送消息大类
     */
    @ApiModelProperty(value = "服务发送消息大类")
    private ServiceMessageTypeEum serviceMessageTypeEum;

    /**
     * 服务发送消息小类
     */
    @ApiModelProperty(value = "服务发送消息小类")
    private ServiceMessageSubTypeEum serviceMessageSubTypeEum;

    /**
     * 重定向链接
     */
    @ApiModelProperty(value = "重定向链接")
    private String linkUrl;

    /**
     * 推送时间
     */
    @ApiModelProperty(value = "推送时间")
    @JacksonZoneId
    private Long sendTime;

    /**
     * 是否显示小红点
     */
    @ApiModelProperty(value = "是否显示小红点")
    private Boolean redPoint;
}
