package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.invite.vo.resp;

import java.math.BigDecimal;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InviteAwardItemResp implements IResp {
    private static final long serialVersionUID = -2287487796726289387L;

    @ApiModelProperty(value = "邀请人数")
    private Integer inviteNum;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal awardAmount;
}
