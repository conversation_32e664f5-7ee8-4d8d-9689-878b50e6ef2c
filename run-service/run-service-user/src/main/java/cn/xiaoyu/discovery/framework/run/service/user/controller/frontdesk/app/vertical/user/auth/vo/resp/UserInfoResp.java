package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.auth.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IdResp;
import cn.xiaoyu.discovery.framework.run.common.eum.SexEum;
import cn.xiaoyu.discovery.framework.run.common.i18n.eum.CurrencyEum;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.user.LevelEum;
import cn.xiaoyu.discovery.framework.run.starter.file.replace.annotation.ReplaceDomainName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/2/6 11:43
 */
@ApiModel("用户基础信息")
@Data
public class UserInfoResp extends IdResp {

    private static final long serialVersionUID = -2663948276171331524L;
    @ApiModelProperty(value = "用户名")
    private String username;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "国家码")
    private String phonePrefix;
    @ApiModelProperty(value = "手机号码")
    private String phone;
    @ApiModelProperty(value = "是否是测试账号")
    private Boolean tester;

    @ApiModelProperty(value = "真实姓名-支付提现绑卡需使用-也可直接存储在绑卡处-在联动存储此处")
    private String realName;

    @ApiModelProperty(value = "昵称")
    private String nickname;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "性别")
    private SexEum sexEum;
    @ApiModelProperty(value = "头像-域名后的地址串")
    @ReplaceDomainName
    private String avatar;

    @ApiModelProperty(value = "前台用户code")
    private Long userCode;
    @ApiModelProperty(value = "邀请来的上级id，默认0/null 代表平台 通过注册的渠道码转用户id获得")
    private Long parentId;
    @ApiModelProperty(value = "等级Id")
    private Long levelId;
    /**
     * 等级枚举
     * <p>
     * 枚举 {@link LevelEum}
     */
    @ApiModelProperty(value = "等级枚举")
    private LevelEum levelEum;
    @ApiModelProperty(value = "等级图标地址")
    @ReplaceDomainName
    private String levelImgUrl;
    @ApiModelProperty(value = "等级附件地址-达标图标")
    @ReplaceDomainName
    private String levelAttachmentUrl;
    @ApiModelProperty(value = "等级附件地址-未达标图标")
    @ReplaceDomainName
    private String levelAttachmentUrl2;
    /**
     * 层级信息 start
     */
    @ApiModelProperty(value = "层级id")
    private Long hierarchicalId;
    // @ApiModelProperty(value = "层级名称", hidden = true)
    // private String hierarchicalName;
    @ApiModelProperty(value = "是否允许充值")
    private Boolean allowRecharge;
    @ApiModelProperty(value = "是否允许提现")
    private Boolean allowWithdraw;
    @ApiModelProperty(value = "是否允许领取返水")
    private Boolean allowReceiveWash;
    @ApiModelProperty(value = "是否允许领取代理佣金")
    private Boolean allowReceiveAgentCommission;
    @ApiModelProperty(value = "是否允许领取活动优惠")
    private Boolean allowReceiveActivityDiscount;
    /**
     * 层级信息 end
     */
    @ApiModelProperty(value = "是否是正式账号（false游客账号、true正式账号）")
    private Boolean porder;
    @ApiModelProperty("货币 登录成功以后此货币需要替换header中的货币字段")
    private CurrencyEum currencyEum;

    @ApiModelProperty(value = "是否是代理 有下级用户 代表就是代理 也就是邀请有人注册后 需要判断修改邀请人信息进行修改")
    private Boolean agenter;

    @ApiModelProperty("用户注册时所选国家对应的时区")
    private String zoneId;

    @ApiModelProperty("用户是否设置了[安全密码]标识，true = 还没设置，false = 已经设置过了")
    private Boolean noSetSecurePassword;

    @ApiModelProperty("用户是否设置了[登录密码]标识，true = 还没设置，false = 已经设置过了")
    private Boolean noSetLoginPassword;

}
