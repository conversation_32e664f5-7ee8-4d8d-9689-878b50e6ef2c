package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel("签到Resp")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySignResp implements Serializable {
	private static final long serialVersionUID = -2222819439300957175L;

	@ApiModelProperty("签到金额")
	private BigDecimal signAmount;

	@ApiModelProperty("周期内签到天数")
	private Integer signDays;
}
