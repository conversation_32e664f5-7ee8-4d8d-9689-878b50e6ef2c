package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.center.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/3/19 15:12
 */
@ApiModel("更新用户密码Req")
@Data
public class UpdateUserPasswordReq extends IdReq {
    private static final long serialVersionUID = -2094812366437112683L;

    @ApiModelProperty("旧密码")
    private String oldPassword;
    @ApiModelProperty("新密码")
    private String newPassword;
    @ApiModelProperty("是否首次设置-绑定提现密码")
    private boolean isFirstSet;
}
