package cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdListReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IdReq;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.PageResp;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpBackstageUtils;
import cn.xiaoyu.discovery.framework.run.service.system.eum.permission.MenuEum;
import cn.xiaoyu.discovery.framework.run.service.system.eum.permission.RoleTypeEum;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.req.AddWelfareCodeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.req.QueryWelfareCodeExchangeRecordReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.req.QueryWelfareCodeReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.req.UpdatRuleReq;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.resp.QueryWelfareCodeExchangeRecordResp;
import cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.welfarecode.vo.resp.QueryWelfareCodeResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.welfarecode.WelfareCodeService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.BackstageController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@BackstageController
@ApiSort(value = 1)
@SaCheckLogin(type = StpBackstageUtils.TYPE)
@RequestMapping(WelfareCodeController.MODULE_PREFIX)
@Api(value = "1.福利码设置",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + WelfareCodeController.MODULE_PREFIX + "模块-管理后台福利码操作")
public class WelfareCodeController implements IModulePrefix {

    private final WelfareCodeService welfareCodeService;

    @PostMapping("/addWelfareCode")
    @ApiOperation("1.新增福利码")
    @ApiOperationSupport(order = 1)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "addWelfareCode",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<Boolean> addWelfareCode(@RequestBody AddWelfareCodeReq req) {
        return R.success(welfareCodeService.addWelfareCode(req));
    }

    @PostMapping("/queryWelfareCode")
    @ApiOperation("2.查询福利码")
    @ApiOperationSupport(order = 2)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "queryWelfareCode",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<PageResp<QueryWelfareCodeResp>> queryWelfareCode(@RequestBody QueryWelfareCodeReq req) {
        return R.success(welfareCodeService.queryWelfareCode(req));
    }

    @PostMapping("/delWelfareCodeById")
    @ApiOperation("3.删除福利码")
    @ApiOperationSupport(order = 3)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "delWelfareCodeById",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<Boolean> delWelfareCodeById(@RequestBody IdReq req) {
        return R.success(welfareCodeService.delWelfareCodeById(req.getId()));
    }

    @PostMapping("/delWelfareCodeByIds")
    @ApiOperation("4.删除福利码集合")
    @ApiOperationSupport(order = 4)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "delWelfareCodeByIds",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<Boolean> delWelfareCodeByIds(@RequestBody IdListReq req) {
        return R.success(welfareCodeService.delWelfareCodeByIds(req.getIds()));
    }

    @PostMapping("/queryWelfareCodeExchangeRecord")
    @ApiOperation("5.根据福利码ID查询福利码兑换记录")
    @ApiOperationSupport(order = 5)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "queryWelfareCodeExchangeRecord",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<PageResp<QueryWelfareCodeExchangeRecordResp>> queryWelfareCodeExchangeRecord(@RequestBody QueryWelfareCodeExchangeRecordReq req) {
        return R.success(welfareCodeService.queryWelfareCodeExchangeRecord(req));
    }

    @PostMapping("/generateCode")
    @ApiOperation("6.生成随机五位福利码")
    @ApiOperationSupport(order = 6)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "generateCode",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<String> generateCode() {
        return R.success(welfareCodeService.generateCode());
    }

    @PostMapping("/updateRule")
    @ApiOperation("7.更改福利码规则")
    @ApiOperationSupport(order = 7)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "updateRule",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<Boolean> updateRule(@RequestBody UpdatRuleReq req) {
        return R.success(welfareCodeService.updateRule(req));
    }

    @PostMapping("/getRule")
    @ApiOperation("8.获取福利码规则")
    @ApiOperationSupport(order = 8)
    @SaCheckPermission(value = MenuEum.WELFARE_CODE_PERMISSION_CODE_PREFIX + "getRule",
            orRole = {RoleTypeEum.SUPPER_ADMIN_CODE, RoleTypeEum.ADMIN_CODE}, type = StpBackstageUtils.TYPE)
    @Encrypt
    @Decrypt
    public R<String> getRule() {
        return R.success(welfareCodeService.getRule());
    }
}
