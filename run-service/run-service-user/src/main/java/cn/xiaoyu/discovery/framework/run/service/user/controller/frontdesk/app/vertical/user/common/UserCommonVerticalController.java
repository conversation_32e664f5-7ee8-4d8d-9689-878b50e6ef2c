package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.common;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.xiaoyu.discovery.framework.run.common.constant.CommonServiceConstant;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Decrypt;
import cn.xiaoyu.discovery.framework.run.common.crypto.mvc.annotation.Encrypt;
import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.R;
import cn.xiaoyu.discovery.framework.run.common.satoken.utils.StpFrontdeskUtils;
import cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.user.common.vo.BookRecordCategoryResp;
import cn.xiaoyu.discovery.framework.run.service.user.service.user.common.UserCommonService;
import cn.xiaoyu.discovery.framework.run.starter.swagger.annotation.AppVerticalController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/2/27 14:59
 * @Description
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@AppVerticalController
@ApiSort(value = 1)
@SaCheckLogin(type = StpFrontdeskUtils.TYPE)
@RequestMapping(UserCommonVerticalController.MODULE_PREFIX)
@Api(value = "1.用户",
    tags = CommonServiceConstant.RUN_SERVICE_USER_NAME + "服务-" + UserCommonVerticalController.MODULE_PREFIX + "模块-公共参数")
public class UserCommonVerticalController implements IModulePrefix {
    private final UserCommonService userCommonService;

    @PostMapping("/getBookRecordCategory")
    @ApiOperation("1.获取到账类型")
    @ApiOperationSupport(order = 1)
    @Decrypt
    @Encrypt
    @SaIgnore
    public R<BookRecordCategoryResp> getBookRecordCategory() {
        return R.success(userCommonService.getBookRecordCategory());
    }
}
