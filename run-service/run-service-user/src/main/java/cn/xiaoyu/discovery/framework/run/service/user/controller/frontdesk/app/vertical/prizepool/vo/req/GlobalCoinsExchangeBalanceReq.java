package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.game.eum.game.sub.GameSubTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("全球币兑换余额请求req")
public class GlobalCoinsExchangeBalanceReq implements IReq {

    private static final long serialVersionUID = -3628096003901014832L;
    @ApiModelProperty(value = "兑换数量", required = true)
    private Long exchangeNumber;

    @ApiModelProperty(value = "子游戏枚举（游戏内奖池币快速兑换余额时必传）")
    private GameSubTypeEum gameSubTypeEum;
}
