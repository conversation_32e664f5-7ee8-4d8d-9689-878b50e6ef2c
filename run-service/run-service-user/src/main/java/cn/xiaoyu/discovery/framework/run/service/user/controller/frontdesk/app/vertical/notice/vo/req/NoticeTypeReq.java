package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.notice.vo.req;

import javax.validation.constraints.NotNull;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.IReq;
import cn.xiaoyu.discovery.framework.run.service.user.eum.notice.NoticeTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通知清空Req
 * <AUTHOR>
 * @since 2023/7/25
 */
@ApiModel("通知清空Req")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NoticeTypeReq implements IReq {

    private static final long serialVersionUID = 4282869231004936740L;
    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    @NotNull(message = "通知类型不能为空")
    private NoticeTypeEum noticeTypeEum;
}
