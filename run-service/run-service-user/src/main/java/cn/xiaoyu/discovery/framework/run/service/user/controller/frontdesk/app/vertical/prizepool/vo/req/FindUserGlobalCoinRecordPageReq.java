package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.prizepool.vo.req;

import java.util.List;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimePageReq;
import cn.xiaoyu.discovery.framework.run.service.common.eum.user.prizepool.CoinSourceTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("用户全球币/奖池币改变记录分页请求req")
public class FindUserGlobalCoinRecordPageReq extends CreateTimePageReq {

    private static final long serialVersionUID = -3543536002118552417L;
    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(value = "改变类型", hidden = true)
    private List<CoinSourceTypeEum> coinSourceTypeEum =
        List.of(CoinSourceTypeEum.EXCHANGE_BALANCE, CoinSourceTypeEum.EXCHANGE_DISCOUNT_WALLET);
}
