//package cn.xiaoyu.discovery.framework.run.service.user.configuration;
//
//import java.util.concurrent.ThreadPoolExecutor;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
///**
// * 线程池配置类 根据自己环境进行配置
// */
//@Configuration
//@EnableAsync
//public class ExecutorConfig {
//
//    /**
//     * 核心线程数 自行设置
//     */
//    private static final int CORE_POOL_SIZE = 15;
//    /**
//     * 最大线程数 自行设置
//     */
//    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2 + 1;
//
//    @Bean(name="serviceUserTaskExecutor")
//    public ThreadPoolTaskExecutor taskExecutor(){
//        ThreadPoolTaskExecutor poolTaskExecutor = new ThreadPoolTaskExecutor();
//        /**
//         * 此方法返回可用处理器的虚拟机的最大数量; 不小于1
//         * int core = Runtime.getRuntime().availableProcessors();
//         */
//        //设置核心线程数
//        poolTaskExecutor.setCorePoolSize(CORE_POOL_SIZE);
//        //设置最大线程数
//        poolTaskExecutor.setMaxPoolSize(MAX_POOL_SIZE);
//        //除核心线程外的线程存活时间
//        poolTaskExecutor.setKeepAliveSeconds(3);
//        //如果传入值大于0，底层队列使用的是LinkedBlockingQueue,否则默认使用SynchronousQueue
//        poolTaskExecutor.setQueueCapacity(40);
//        //线程名称前缀
//        poolTaskExecutor.setThreadNamePrefix("thread-execute");
//        //设置拒绝策略
//        poolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        return poolTaskExecutor;
//
//    }
//}
