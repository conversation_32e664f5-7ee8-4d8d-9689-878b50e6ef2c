package cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.turntable.vo.resp;

import cn.xiaoyu.discovery.framework.run.common.domain.vo.resp.IResp;
import cn.xiaoyu.discovery.framework.run.common.zoneid.annotation.JacksonZoneId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @since 2024/2/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TurnTableInviteRecordResp implements IResp {

    private static final long serialVersionUID = -6146468067702600071L;

    /**
     * 被邀请用户编码
     */
    @ApiModelProperty(value = "被邀请用户编码")
    private Long inviteUserCode;

    /**
     * 被邀请用户名
     */
    @ApiModelProperty(value = "被邀请用户名")
    private String inviteUsername;

    /**
     * 是否成功邀请
     */
    @ApiModelProperty(value = "是否成功邀请")
    private Boolean successer;

    /**
     * 目标充值金额
     */
    @ApiModelProperty(value = "目标充值金额")
    private BigDecimal underRechargeLimit = BigDecimal.ZERO;

    /**
     * 当前充值金额
     */
    @ApiModelProperty(value = "当前充值金额")
    private BigDecimal nowRechargeAmount = BigDecimal.ZERO;

    /**
     * 剩余金额
     */
    @ApiModelProperty(value = "剩余金额")
    private BigDecimal residualAmount = BigDecimal.ZERO;

    /**
     * 邀请时间
     */
    @ApiModelProperty(value = "邀请时间")
    @JacksonZoneId
    private Long createTime;

    /**
     * 目标打码金额
     */
    @ApiModelProperty(value = "目标打码金额")
    private BigDecimal underBetLimit = BigDecimal.ZERO;

    /**
     * 当前打码金额
     */
    @ApiModelProperty(value = "当前打码金额")
    private BigDecimal nowBetAmount = BigDecimal.ZERO;

    /**
     * 剩余打码金额
     */
    @ApiModelProperty(value = "剩余打码金额")
    private BigDecimal residualBetAmount = BigDecimal.ZERO;
}