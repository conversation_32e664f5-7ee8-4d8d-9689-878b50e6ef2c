nacos:
    logging:
        default:
            config:
                enabled: false
    serverAddr: ${conf.nacos.serverAddr:192.168.0.4:9948}
    group: run-group

sentinel:
    dashboard: ${conf.sentinel.dashboard:192.168.0.4:7070}

spring:
    application:
        name: run-service-user

    cloud:
        nacos:
            config:
                fileExtension: yaml
                serverAddr: ${nacos.serverAddr}
                group: ${nacos.group}
                extensionConfigs:
                    # 公有配置
                    -   dataId: run-common.yaml
                        group: ${nacos.group}
                        refresh: true
                    # 自己服务配置
                    -   dataId: run-service-user.yaml
                        group: ${nacos.group}
                        refresh: true

            discovery:
                group: ${nacos.group}
                # Nacos 服务器地址
                serverAddr: ${nacos.serverAddr}
                # 默认值为 ${spring.application.name}
                service: ${spring.application.name}
                # 外网注册打开 并填写启动程序的外网ip
                # ip:

        sentinel:
            # 开启sentinel
            enabled: true
            # sentinel默认是懒加载 我们这里开启饥饿加载 这样启动后 在sentinel 服务端的web终端就可以看到了
            eager: true
            transport:
                # sentinel 控制台地址
                dashboard: ${sentinel.dashboard}
            datasource:
                nacos:
                    serverAddr: ${nacos.serverAddr}
