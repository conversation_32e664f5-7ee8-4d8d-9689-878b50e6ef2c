# web容器端口
server:
    port: 30001

# 二级缓存配置
l2cache:
    config:
        # 缓存实例Id，唯一标识应分布式场景下的一个缓存实例节点
        #instanceId: a1
        # 是否存储空值，默认true，防止缓存穿透
        allowNullValues: true
        # 空值过期时间，单位秒
        nullValueExpireTimeSeconds: 30
        # 缓存类型
        cacheType: composite
        # 组合缓存配置
        composite:
            # 一级缓存类型
            l1CacheType: caffeine
            # 二级缓存类型
            l2CacheType: redis
            # 是否全部启用一级缓存，默认false
            l1AllOpen: false
            # 是否手动启用一级缓存，默认false
            l1Manual: true
            # 手动配置走一级缓存的缓存key集合，针对单个key维度
            l1ManualKeySet:
            # 手动配置走一级缓存的缓存名字集合，针对cacheName维度
            l1ManualCacheNameSet:
        # 一级缓存
        caffeine:
            # 是否自动刷新过期缓存 true 是 false 否
            autoRefreshExpireCache: false
            # 缓存刷新调度线程池的大小
            refreshPoolSize: 2
            # 缓存刷新的频率(秒)
            refreshPeriod: 10
            # 高并发场景下建议使用refreshAfterWrite，在缓存过期后不会被回收，再次访问时会去刷新缓存，在新值没有加载完毕前，其他的线程访问始终返回旧值
            # Caffeine在缓存过期时默认只有一个线程去加载数据，配置了refreshAfterWrite后当大量请求过来时，可以确保其他用户快速获取响应。
            # 创建缓存的默认配置（完全与SpringCache中的Caffeine实现的配置一致）
            # 如果expireAfterWrite和expireAfterAccess同时存在，以expireAfterWrite为准。
            # 推荐用法：refreshAfterWrite 和 @Cacheable(sync=true)
            # 注意：当缓存项在有效期内重复利用率很低时，不适合用本地缓存，如3千万用户参加抢购活动，用户信息的缓存，则不能用本地缓存。
            # 因为超过最大数量限制时，再往里面添加元素，会异步按照LFU淘汰元素，若未及时淘汰，在大量用户请求的情况，会导致堆内存飙升，频繁的FullGC和YoungGC，最终导致OOM。
            defaultSpec: initialCapacity=10,maximumSize=2,refreshAfterWrite=2m,softValues,recordStats
            # 设置指定缓存名的创建缓存配置(如：userCache为缓存名称)
            # specs:
        # 二级缓存
        redis:
            # 加载数据时，是否加锁
            lock: false
            # 加锁时，true调用tryLock()，false调用lock()
            tryLock: true
            # 批量操作的大小，可以理解为是分页
            batchPageSize: 3
            # 缓存过期时间(ms)
            #expireTime: 30000
            # 缓存最大空闲时间(ms)
            #maxIdleTime: 30000
            # 最大缓存数
            #maxSize: 200
        # 缓存同步策略配置
        cacheSyncPolicy:
            # 策略类型 kafka / redis
            type: redis
            # 缓存更新时通知其他节点的topic名称
            topic: ${spring.application.name}-topic

spring:
    datasource:
        url: *******************************************************************************************************************************************************
        #driverClassName: com.mysql.jdbc.Driver
        driverClassName: com.mysql.cj.jdbc.Driver
        username: root
        password: 123456
        hikari:
            poolName: UserHikariCP
            minimumIdle: 5 #最小空闲连接数量
            idleTimeout: 180000 #空闲连接存活最大时间，默认600000（10分钟）
            maximumPoolSize: 10 #连接池最大连接数，默认是10
            autoCommit: true  #此属性控制从池返回的连接的默认自动提交行为,默认值：true
            maxLifetime: 1800000 #此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            connectionTimeout: 30000 #数据库连接超时时间,默认30秒，即30000
            connectionTestQuery: SELECT 1
    cloud:
        stream:
            # Spring Cloud Stream RocketMQ 配置项
            rocketmq:
                # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
                binder:
                    # RocketMQ Namesrv 地址
                    # name-server: 192.168.0.58:9876;192.168.0.59:9876
                    name-server: 192.168.0.58:9876
                    # 不加, 会报错：Property 'group' is required - producerGroup
                    group: rocketmq-group
                # RocketMQ 自定义 Binding 配置项，对应 RocketMQBindingProperties Map
                bindings:
                    # 生产者
                    user_reg_to_user_invite_politely_output:
                        producer:
                            group: user_reg_to_user_invite_politely_group
                    user_reg_to_user_activity_output:
                        producer:
                            group: user_reg_to_user_activity_group
                    user_reg_to_pay_finance_book_output:
                        producer:
                            group: user_reg_to_pay_finance_book_group
                    more_service_send_user_notice_msg_to_user_output:
                        producer:
                            group: more_service_send_user_notice_msg_to_user_group
                    more_service_bind_phone_to_user_activity_output:
                        producer:
                            group: more_service_bind_phone_to_user_activity_group
                    more_service_bind_email_to_user_activity_output:
                        producer:
                            group: more_service_bind_email_to_user_activity_group
                    more_service_to_user_agent_team_output:
                        producer:
                            group: more_service_to_user_agent_team_group
                    more_service_bind_real_name_to_user_activity_output:
                        producer:
                            group: more_service_bind_real_name_to_user_activity_group
                    more_service_send_user_ws_msg_to_gateway_output:
                        producer:
                            group: more_service_send_user_ws_msg_to_gateway_group
                    more_service_update_user_book_to_pay_output:
                        producer:
                            group: more_service_update_user_book_to_pay_group
                    user_login_to_user_download_gift_activity_output:
                        producer:
                            group: user_login_to_user_download_gift_activity_group
                    #号码池
                    more_service_to_user_phone_pool_output:
                        producer:
                            group: more_service_to_user_phone_pool_group
                    #用户标签
                    user_to_user_label_output:
                        producer:
                            group: user_to_user_label_group
                    # 消费者
                    pay_bind_withdraw_account_to_user_activity_input:
                        consumer:
                            orderly: true
                    more_service_bind_real_name_to_user_activity_input:
                        consumer:
                            orderly: true
                    more_service_bind_phone_to_user_activity_input:
                        consumer:
                            orderly: true
                    more_service_bind_email_to_user_activity_input:
                        consumer:
                            orderly: true
                    more_service_to_user_agent_team_input:
                        consumer:
                            orderly: true
                    more_service_send_user_notice_msg_to_user_input:
                        consumer:
                            orderly: true
                    more_service_bet_recharge_withdraw_commission_to_user_change_data_input:
                        consumer:
                            orderly: true
                    pay_to_user_task_input:
                        consumer:
                            orderly: true
                    pay_to_user_invite_politely_input:
                        consumer:
                            orderly: true
                    pay_to_user_activity_input:
                        consumer:
                            orderly: true
                    user_reg_to_user_invite_politely_input:
                        consumer:
                            orderly: true
                    user_reg_to_user_activity_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_treat_wash_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_task_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_prize_pool_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_invite_politely_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_global_coin_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_treat_commission_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_activity_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_active_user_input:
                        consumer:
                            orderly: true
                    system_close_platform_to_user_input:
                        consumer:
                            orderly: true
                    system_open_platform_to_user_input:
                        consumer:
                            orderly: true
                    user_login_to_user_download_gift_activity_input:
                        consumer:
                            orderly: true
                    game_pull_order_to_user_download_gift_activity_input:
                        consumer:
                            orderly: true
                    pay_to_user_download_gift_activity_input:
                        consumer:
                            orderly: true
                    #号码池
                    more_service_to_user_phone_pool_input:
                        consumer:
                            orderly: true
                    #用户标签
                    user_to_user_label_input:
                        consumer:
                            orderly: true

            # Binding 配置项，对应 BindingProperties Map
            bindings:
                # 生产者
                user_reg_to_user_invite_politely_output:
                    group: user_reg_to_user_invite_politely_group
                    destination: user_reg_to_user_invite_politely_topic
                    content-type: application/json
                user_reg_to_user_activity_output:
                    group: user_reg_to_user_activity_group
                    destination: user_reg_to_user_activity_topic
                    content-type: application/json
                user_reg_to_pay_finance_book_output:
                    group: user_reg_to_pay_finance_book_group
                    destination: user_reg_to_pay_finance_book_topic
                    content-type: application/json
                more_service_send_user_notice_msg_to_user_output:
                    group: more_service_send_user_notice_msg_to_user_group
                    destination: more_service_send_user_notice_msg_to_user_topic
                    content-type: application/json
                more_service_bind_phone_to_user_activity_output:
                    group: more_service_bind_phone_to_user_activity_group
                    destination: more_service_bind_phone_to_user_activity_topic
                    content-type: application/json
                more_service_bind_email_to_user_activity_output:
                    group: more_service_bind_email_to_user_activity_group
                    destination: more_service_bind_email_to_user_activity_topic
                    content-type: application/json
                more_service_to_user_agent_team_output:
                    group: more_service_to_user_agent_team_group
                    destination: more_service_to_user_agent_team_topic
                    content-type: application/json
                more_service_bind_real_name_to_user_activity_output:
                    group: more_service_bind_real_name_to_user_activity_group
                    destination: more_service_bind_real_name_to_user_activity_topic
                    content-type: application/json
                more_service_send_user_ws_msg_to_gateway_output:
                    group: more_service_send_user_ws_msg_to_gateway_group
                    destination: more_service_send_user_ws_msg_to_gateway_topic
                    content-type: application/json
                more_service_update_user_book_to_pay_output:
                    group: more_service_update_user_book_to_pay_group
                    destination: more_service_update_user_book_to_pay_topic
                    content-type: application/json
                user_login_to_user_download_gift_activity_output:
                    group: user_login_to_user_download_gift_activity_group
                    destination: user_login_to_user_download_gift_activity_topic
                    content-type: application/json
                #号码池
                more_service_to_user_phone_pool_output:
                    group: more_service_to_user_phone_pool_group
                    destination: more_service_to_user_phone_pool_topic
                    content-type: application/json
                #用户标签
                user_to_user_label_output:
                    group: user_to_user_label_group
                    destination: user_to_user_label_topic
                    content-type: application/json
                # 消费者
                pay_bind_withdraw_account_to_user_activity_input:
                    group: pay_bind_withdraw_account_to_user_activity_group
                    destination: pay_bind_withdraw_account_to_user_activity_topic
                    content-type: application/json
                more_service_bind_real_name_to_user_activity_input:
                    group: more_service_bind_real_name_to_user_activity_group
                    destination: more_service_bind_real_name_to_user_activity_topic
                    content-type: application/json
                more_service_bind_phone_to_user_activity_input:
                    group: more_service_bind_phone_to_user_activity_group
                    destination: more_service_bind_phone_to_user_activity_topic
                    content-type: application/json
                more_service_bind_email_to_user_activity_input:
                    group: more_service_bind_email_to_user_activity_group
                    destination: more_service_bind_email_to_user_activity_topic
                    content-type: application/json
                more_service_to_user_agent_team_input:
                    group: more_service_to_user_agent_team_group
                    destination: more_service_to_user_agent_team_topic
                    content-type: application/json
                more_service_send_user_notice_msg_to_user_input:
                    group: more_service_send_user_notice_msg_to_user_group
                    destination: more_service_send_user_notice_msg_to_user_topic
                    content-type: application/json
                more_service_bet_recharge_withdraw_commission_to_user_change_data_input:
                    group: more_service_bet_recharge_withdraw_commission_to_user_change_data_group
                    destination: more_service_bet_recharge_withdraw_commission_to_user_change_data_topic
                    content-type: application/json
                pay_to_user_task_input:
                    group: pay_to_user_task_group
                    destination: pay_to_user_task_topic
                    content-type: application/json
                pay_to_user_invite_politely_input:
                    group: pay_to_user_invite_politely_group
                    destination: pay_to_user_invite_politely_topic
                    content-type: application/json
                pay_to_user_activity_input:
                    group: pay_to_user_activity_group
                    destination: pay_to_user_activity_topic
                    content-type: application/json
                user_reg_to_user_invite_politely_input:
                    group: user_reg_to_user_invite_politely_group
                    destination: user_reg_to_user_invite_politely_topic
                    content-type: application/json
                user_reg_to_user_activity_input:
                    group: user_reg_to_user_activity_group
                    destination: user_reg_to_user_activity_topic
                    content-type: application/json
                game_pull_order_to_user_treat_wash_input:
                    group: game_pull_order_to_user_treat_wash_group
                    destination: game_pull_order_to_user_treat_wash_topic
                    content-type: application/json
                game_pull_order_to_user_task_input:
                    group: game_pull_order_to_user_task_group
                    destination: game_pull_order_to_user_task_topic
                    content-type: application/json
                game_pull_order_to_user_prize_pool_input:
                    group: game_pull_order_to_user_prize_pool_group
                    destination: game_pull_order_to_user_prize_pool_topic
                    content-type: application/json
                game_pull_order_to_user_invite_politely_input:
                    group: game_pull_order_to_user_invite_politely_group
                    destination: game_pull_order_to_user_invite_politely_topic
                    content-type: application/json
                game_pull_order_to_user_global_coin_input:
                    group: game_pull_order_to_user_global_coin_group
                    destination: game_pull_order_to_user_global_coin_topic
                    content-type: application/json
                game_pull_order_to_user_treat_commission_input:
                    group: game_pull_order_to_user_treat_commission_group
                    destination: game_pull_order_to_user_treat_commission_topic
                    content-type: application/json
                game_pull_order_to_user_activity_input:
                    group: game_pull_order_to_user_activity_group
                    destination: game_pull_order_to_user_activity_topic
                    content-type: application/json
                game_pull_order_to_user_active_user_input:
                    group: game_pull_order_to_user_active_user_group
                    destination: game_pull_order_to_user_active_user_topic
                    content-type: application/json
                system_close_platform_to_user_input:
                    group: system_close_platform_to_user_group
                    destination: system_close_platform_to_user_topic
                    content-type: application/json
                system_open_platform_to_user_input:
                    group: system_open_platform_to_user_group
                    destination: system_open_platform_to_user_topic
                    content-type: application/json
                user_login_to_user_download_gift_activity_input:
                    group: user_login_to_user_download_gift_activity_group
                    destination: user_login_to_user_download_gift_activity_topic
                    content-type: application/json
                pay_to_user_download_gift_activity_input:
                    group: pay_to_user_download_gift_activity_group
                    destination: pay_to_user_download_gift_activity_topic
                    content-type: application/json
                game_pull_order_to_user_download_gift_activity_input:
                    group: game_pull_order_to_user_download_gift_activity_group
                    destination: game_pull_order_to_user_download_gift_activity_topic
                    content-type: application/json
                #号码池
                more_service_to_user_phone_pool_input:
                    group: more_service_to_user_phone_pool_group
                    destination: more_service_to_user_phone_pool_topic
                    content-type: application/json
                #用户标签
                user_to_user_label_input:
                    group: user_to_user_label_group
                    destination: user_to_user_label_topic
                    content-type: application/json


xiaoyu:
    xxljob:
        enabled: true
        admin:
            address: http://************:18080/xxl-job-admin
            accessToken: default_token
            username: admin
            password: 123456
            contextPath:
        executor:
            appName: run-service-user-executor
            ip:
            port: 60005
            logPath: ../logs/run-service-user/jobhandler
            logRetentionDays: 1
            title: 用户服务-执行器
