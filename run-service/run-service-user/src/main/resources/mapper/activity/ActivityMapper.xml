<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.activity.ActivityMapper">

    <select id="getActivityList"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityEntity">
        select a.id,
               a.title,
               a.h5_picture,
               a.pc_picture,
               a.activity_text,
               a.click_show_texter,
               a.click_jump_url,
               a.click_jump_url,
               a.activity_type_eum,
               a.sort
        from at_activity a
        where IF(#{req.labelId} IS NOT NULL, a.label_id = #{req.labelId}, TRUE)
          and IF(a.status_eum IS NOT NULL, a.status_eum = 0, FALSE)
          and a.activity_type_eum IN (0, 11, 12, 13, 15)
          and IF(a.start_time IS NOT NULL, #{time} >= a.start_time, TRUE)
          and IF(a.end_time IS NOT NULL, #{time} &lt;= a.end_time, TRUE)
        order by a.sort, a.create_time DESC
    </select>

    <select id="findNotNeedApplyActivity"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityEntity">
        select *
        from at_activity a
        where IF(a.needer IS NOT NULL, a.needer = false, FALSE)
          and IF(a.status_eum IS NOT NULL, a.status_eum = 0, FALSE)
    </select>


    <select id="getActivityListTotalCount"
            resultType="java.lang.Long">
        select COUNT(1)
        from at_activity a
        where IF(#{req.labelId} IS NOT NULL, a.label_id = #{req.labelId}, TRUE)
          and IF(a.status_eum IS NOT NULL, a.status_eum = 0, FALSE)
          and a.activity_type_eum IN (0, 11, 12, 13, 15)
          and IF(a.start_time IS NOT NULL, #{time} >= a.start_time, TRUE)
          and IF(a.end_time IS NOT NULL, #{time} &lt;= a.end_time, TRUE)
    </select>

    <select id="getSideActivityList"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivitySideResp">
        select a.id,
               a.title,
               a.min_picture,
               a.click_show_texter,
               a.click_jump_url,
               a.activity_type_eum,
               a.start_time,
               a.end_time,
               a.sort
        from at_activity a
        where IF(a.is_side IS NOT NULL, a.is_side = 1, FALSE)
          and IF(a.status_eum IS NOT NULL, a.status_eum = 0, FALSE)
          and IF(a.activity_type_eum IS NOT NULL, a.activity_type_eum IN (0, 11, 12, 13, 15), FALSE)
        order by a.sort, a.create_time DESC
    </select>


    <select id="findActivityByType"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityEntity">
        select * from at_activity a
        <where>
            a.status_eum = 0
            <if test="activityTypeEum != null">
                and a.activity_type_eum = #{activityTypeEum}
            </if>
        </where>
    </select>


    <select id="findActivityByLabelId"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityEntity">
        select *
        from at_activity a
        where IF(a.needer IS NOT NULL, a.needer = false, FALSE)
          and IF(a.status_eum IS NOT NULL, a.status_eum = 0, FALSE)
          and IF(a.labelId IS NOT NULL, a.labelId = #{labelId}, FALSE)
    </select>

    <select id="getHomeWindowActivity"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityEntity">
        select id,
               title,
               home_window_picture,
               activity_text,
               start_time,
               end_time
        from at_activity a
        where IF(a.is_home_window IS NOT NULL, a.is_home_window = TRUE, FALSE)
          and IF(a.status_eum IS NOT NULL, a.status_eum = 0, FALSE)
          and IF(a.start_time IS NOT NULL, #{time} >= a.start_time, TRUE)
          and IF(a.end_time IS NOT NULL, #{time} &lt;= a.end_time, TRUE)
    </select>

</mapper>
