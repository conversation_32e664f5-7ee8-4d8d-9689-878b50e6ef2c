<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.activity.ActivityRedPacketMapper">

    <resultMap id="activityRedPacketEntity"
               type="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityRedPacketEntity">
        <result column="must_recharger_amount" property="mustRechargeAmount"/>
    </resultMap>

    <select id="findNormalRedPacket"
            parameterType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.activity.vo.req.FindAvailableRedPacketReq"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityRedPacketResp">
        SELECT A.id,
        A.title,
        A.red_type as redType,
        A.amount,
        A.red_num as redNum,
        A.appoint_users as appointUser,
        A.lowest_level as lowestLevel,
        A.highest_level as highestLevel,
        A.hierarchical_id as hierarchicalId
        FROM at_red_packet A
        <where>
            <if test="redIdList != null and redIdList.size() > 0">
                and A.id NOT IN
                <foreach collection="redIdList" item="redId" open="(" separator="," close=")">
                    #{redId}
                </foreach>
            </if>
            AND A.send_type = #{reqParams.sendTypeEum}
            AND #{reqParams.time} >= A.begin_time
            AND #{reqParams.time} &lt;= A.end_time
        </where>
        ORDER BY A.create_time DESC
    </select>

    <select id="queryEnableRedPackets" resultMap="activityRedPacketEntity">
        SELECT a.* FROM at_red_packet a WHERE a.status_eum = 0
        <if test="req.sendType != null">
            AND a.send_type = #{req.sendType}
        </if>
        <if test="req.beginTime != null">
            and a.begin_time &gt;= #{req.beginTime}
        </if>
        <if test="req.endTime != null">
            and ( a.end_time is null or a.end_time &gt;= #{req.endTime} )
        </if>
    </select>


    <select id="queryRedEnvelopeRain"
            parameterType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.activity.vo.req.FindAvailableRedPacketReq"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.activity.ActivityRedPacketEntity">
        -- 原需求需要过滤掉本期已经领取过的红包，现在需要倒计时显示下一波，so ~ 不能直接在这里过滤了
        <!--        SELECT A.* FROM at_red_packet A-->
        <!--        <where>-->
        <!--            A.id not in (-->
        <!--            SELECT B.red_id FROM at_red_packet_pick_up_record B-->
        <!--            WHERE B.user_id = #{reqParams.userId}-->
        <!--            AND A.frequency = B.frequency-->
        <!--            )-->
        <!--            and A.status_eum = 0-->
        <!--            <if test="reqParams.sendTypeEum != null">-->
        <!--                and A.send_type = #{reqParams.sendTypeEum}-->
        <!--            </if>-->
        <!--            <if test="reqParams.time != null">-->
        <!--                AND (A.end_time is null or #{reqParams.time} &lt;= A.end_time)-->
        <!--            </if>-->
        <!--            order by A.next_send_time desc-->
        <!--        </where>-->

        SELECT A.* FROM at_red_packet A
        <where>
            A.status_eum = 0
            <if test="reqParams.sendTypeEum != null">
                and A.send_type = #{reqParams.sendTypeEum}
            </if>
            <if test="reqParams.time != null">
                AND (A.end_time is null or #{reqParams.time} &lt;= A.end_time)
            </if>
            order by A.next_send_time desc
        </where>
    </select>

</mapper>
