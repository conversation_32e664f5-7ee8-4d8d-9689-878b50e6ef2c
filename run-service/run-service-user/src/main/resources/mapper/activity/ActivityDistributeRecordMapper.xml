<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.activity.ActivityDistributeRecordMapper">

    <!--    <select id="findPageTurnRecordsByUserIdAndType"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityTurnPlateAwardRecordResp">-->
    <!--        SELECT A.username,-->
    <!--               C.award_type,-->
    <!--               C.real_award,-->
    <!--               C.award_amount,-->
    <!--               C.award_num,-->
    <!--               C.is_real,-->
    <!--               A.create_time-->
    <!--        FROM at_distribute_record A-->
    <!--                 LEFT JOIN at_turn_plate_item C ON C.id = A.item_id-->
    <!--        WHERE IF(A.activity_type_eum IS NOT NULL, A.activity_type_eum = #{activityTypeEum}, FALSE)-->
    <!--          AND IF(A.item_id IS NOT NULL, TRUE, FALSE)-->
    <!--          AND IF(#{userId} IS NOT NULL, A.user_id = #{userId}, TRUE)-->
    <!--        ORDER BY A.create_time DESC-->
    <!--            LIMIT #{req.pageSize}-->
    <!--        OFFSET #{req.pageNo}-->

    <!--    </select>-->

    <!--    <select id="findPageTurnRecordsTotal" resultType="java.lang.Long">-->
    <!--        SELECT COUNT(1)-->
    <!--        FROM at_distribute_record A-->
    <!--        WHERE IF(A.activity_type_eum IS NOT NULL, A.activity_type_eum = #{activityTypeEum}, TRUE)-->
    <!--          AND IF(#{userId} IS NOT NULL, A.user_id = #{userId}, TRUE)-->
    <!--          AND IF(A.item_id IS NOT NULL, TRUE, FALSE)-->
    <!--    </select>-->

    <select id="findUserTodayTurnPlateRecord"
            resultType="java.lang.Integer">
        SELECT B.award_type
        FROM at_distribute_record A
                 LEFT JOIN at_turn_plate_item B ON B.id = A.item_id
        WHERE IF(A.activity_type_eum IS NOT NULL, A.activity_type_eum = 8, FALSE)
          AND IF(A.user_id IS NOT NULL, A.user_id = #{userId}, FALSE)
          AND IF(A.create_time IS NOT NULL, A.create_time >= #{startTime}, TRUE)
          AND IF(A.create_time IS NOT NULL, A.create_time &lt;= #{endTime}, TRUE)
    </select>

    <select id="findUserTodayLuckyEggRecord"
            resultType="java.lang.Integer">
        SELECT B.award_type
        FROM at_distribute_record A
                 LEFT JOIN at_egg_item B ON B.id = A.item_id
        WHERE IF(A.activity_type_eum IS NOT NULL, A.activity_type_eum = 9, FALSE)
          AND IF(A.user_id IS NOT NULL, A.user_id = #{userId}, FALSE)
          AND IF(A.create_time IS NOT NULL, A.create_time >= #{startTime}, TRUE)
          AND IF(A.create_time IS NOT NULL, A.create_time &lt;= #{endTime}, TRUE)
    </select>

    <select id="findEggAwardRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityLuckyEggAwardRecordResp">
        SELECT A.username,
        C.award_type,
        C.real_award,
        C.award_amount,
        C.award_num,
        C.is_real
        FROM at_distribute_record A
        LEFT JOIN at_egg_item C ON C.id = A.item_id
        WHERE IF(A.activity_type_eum IS NOT NULL, A.activity_type_eum = 9, FALSE)
        <if test="userId != null">
            and A.user_id= #{userId}
        </if>
        AND IF(A.item_id IS NOT NULL, TRUE, FALSE)
        ORDER BY A.create_time DESC
        LIMIT #{req.pageSize}
        OFFSET #{req.pageNo}
    </select>

    <select id="findEggAwardRecordTotalNum" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM at_distribute_record A
        WHERE A.activity_type_eum = 9
        <if test="userId != null">
            and A.user_id= #{userId}
        </if>
        AND IF(A.item_id IS NOT NULL, TRUE, FALSE)
    </select>

</mapper>
