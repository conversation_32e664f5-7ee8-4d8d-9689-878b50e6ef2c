<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.activity.ActivityGoalsItemEntryRecordMapper">

    <select id="sumUserEntryRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.dto.activity.ActivitySectionUserEntryRecordDto">
        SELECT SUM(complete_recharge_amount) AS completeRechargeAmount,
        SUM(complete_bet_amount) AS completeBetAmount,
        user_id,
        username,
        user_code
        FROM at_goals_item_entry_record
        <where>
            and activity_id = #{activityId}
            <if test="dateList != null">
                and entry_date in
                <foreach collection="dateList" item="date" open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
        </where>
        GROUP BY user_id,username,user_code
    </select>

</mapper>
