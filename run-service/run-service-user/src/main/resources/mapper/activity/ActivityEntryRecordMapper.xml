<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.activity.ActivityEntryRecordMapper">

    <select id="getActivityRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.activity.vo.resp.ActivityRecordResp">
        SELECT A.title,
               B.award_amount,
               A.create_time AS applyDate,
               A.apply_status
        FROM at_entry_record A
                 LEFT JOIN at_activity B ON B.id = A.parent_id
        WHERE IF(#{userId} IS NOT NULL, A.user_id = #{userId}, FALSE)
        ORDER BY A.create_time DESC
            LIMIT #{req.pageSize}
        OFFSET #{req.pageNo}
    </select>

    <select id="getActivityRecordTotal" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM at_entry_record
        WHERE IF(#{userId} IS NOT NULL, user_id = #{userId}, FALSE)
    </select>

    <select id="findPageEntryRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.activity.vo.resp.ActivityEntryRecordResp">
        SELECT A.id,
        A.parent_id,
        A.title,
        A.apply_status,
        A.remark,
        A.apply_param,
        B.award_amount,
        B.autoer,
        B.bet_multiple,
        A.user_id,
        A.username,
        A.user_code,
        A.create_time,
        A.update_time,
        A.update_by,
        A.create_by,
        A.lock_backstage_account_id
        FROM at_entry_record A
        LEFT JOIN at_activity B ON A.parent_id = B.id
        <where>
            <if test="req.userCode != null">
                AND A.user_code = #{req.userCode}
            </if>
            <if test="req.username != null">
                AND A.username = #{req.username}
            </if>
            <if test="req.title != null">
                AND A.title = #{req.title}
            </if>
            <if test="req.applyStatusTypeEumList != null and req.applyStatusTypeEumList.size()>0">
                AND A.apply_status in
                <foreach collection="req.applyStatusTypeEumList" item="applyStatusTypeEum" open="(" separator=","
                         close=")">
                    #{applyStatusTypeEum}
                </foreach>
            </if>
            <if test="req.startCreateTime != null">
                AND A.create_time <![CDATA[ >= ]]> #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND A.create_time <![CDATA[ <= ]]> #{req.endCreateTime}
            </if>
        </where>
        ORDER BY A.create_time DESC
        LIMIT #{req.pageSize}
        OFFSET #{req.pageNo}
    </select>

    <select id="findPageEntryRecordTotalNum" resultType="java.lang.Long">
        SELECT COUNT(A.id)
        FROM at_entry_record A
        <where>
            <if test="req.userCode != null">
                AND A.user_code = #{req.userCode}
            </if>
            <if test="req.username != null">
                AND A.username = #{req.username}
            </if>
            <if test="req.title != null">
                AND A.title = #{req.title}
            </if>
            <if test="req.applyStatusTypeEumList != null and req.applyStatusTypeEumList.size()>0">
                AND A.apply_status in
                <foreach collection="req.applyStatusTypeEumList" item="applyStatusTypeEum" open="(" separator=","
                         close=")">
                    #{applyStatusTypeEum}
                </foreach>
            </if>
            <if test="req.startCreateTime != null">
                AND A.create_time <![CDATA[ >= ]]> #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND A.create_time <![CDATA[ <= ]]> #{req.endCreateTime}
            </if>
        </where>
    </select>

</mapper>
