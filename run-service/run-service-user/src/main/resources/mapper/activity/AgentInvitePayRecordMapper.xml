<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentInvitePayRecordMapper">


    <select id="sumPayAmount"
            resultType="java.math.BigDecimal"
    >
        SELECT
        SUM(amount) as amount
        FROM ap_invite_pay_record
        <where>
            user_id = #{userId}
            <if test="startCreateTime != null">
                AND create_time <![CDATA[ >= ]]> #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                AND create_time <![CDATA[ <= ]]> #{endCreateTime}
            </if>
        </where>
    </select>


</mapper>
