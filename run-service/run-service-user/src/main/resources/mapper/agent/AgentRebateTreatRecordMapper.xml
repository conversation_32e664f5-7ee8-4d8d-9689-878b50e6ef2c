<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentRebateTreatRecordMapper">
    <sql id="QueryColumn">
        id
        ,platform_id,create_by,create_time,update_by,update_time,user_id,user_code,username,calculate_type_eum,calculate_mode_eum,category_type_eum,manufacturers_type_eum,sub_type_eum,treat_rebate_bet_amount,treat_rebate_win_amount,treat_rebate_lose_amount,bet_source_user_id,source_user_is_directly_under,agent_index
    </sql>
    <resultMap type="cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentRebateTreatRecordEntity"
               id="TreatRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="platformId" column="platform_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="userCode" column="user_code"/>
        <result property="username" column="username"/>
        <result property="calculateTypeEum" column="calculate_type_eum"/>
        <result property="calculateModeEum" column="calculate_mode_eum"/>
        <result property="categoryTypeEum" column="category_type_eum"/>
        <result property="manufacturersTypeEum" column="manufacturers_type_eum"/>
        <result property="subTypeEum" column="sub_type_eum"
                typeHandler="cn.xiaoyu.discovery.framework.run.service.common.typehandler.GameSubTypeEumTypeHandler"/>
        <result property="treatRebateBetAmount" column="treat_rebate_bet_amount"/>
        <result property="treatRebateWinAmount" column="treat_rebate_win_amount"/>
        <result property="treatRebateLoseAmount" column="treat_rebate_lose_amount"/>
        <result property="betSourceUserId" column="bet_source_user_id"/>
        <result property="sourceUserIsDirectlyUnder" column="source_user_is_directly_under"/>
        <result property="agentIndex" column="agent_index"/>
    </resultMap>

    <resultMap type="cn.xiaoyu.discovery.framework.run.service.user.dto.agent.AgentRebateTreatRecordDto"
               id="TreatRecordDtoMap">
        <result property="userId" column="userId"/>
        <result property="categoryTypeEum" column="categoryTypeEum"/>
        <result property="manufacturersTypeEum" column="manufacturersTypeEum"/>
        <result property="subTypeEum" column="subTypeEum"
                typeHandler="cn.xiaoyu.discovery.framework.run.service.common.typehandler.GameSubTypeEumTypeHandler"/>
        <result property="treatRebateTotalBetAmount" column="treatRebateTotalBetAmount"/>
        <result property="treatRebateTotalWinAmount" column="treatRebateTotalWinAmount"/>
        <result property="treatRebateTotalLoseAmount" column="treatRebateTotalLoseAmount"/>
        <result property="maxAgentIndex" column="maxAgentIndex"/>
    </resultMap>

    <select id="findPageCategorySummaryInfo"
            resultMap="TreatRecordDtoMap">
        SELECT user_id AS userId,
        category_type_eum AS categoryTypeEum,
        SUM(treat_rebate_bet_amount) AS treatRebateTotalBetAmount,
        SUM(treat_rebate_win_amount) AS treatRebateTotalWinAmount,
        SUM(treat_rebate_lose_amount) AS treatRebateTotalLoseAmount,
        MAX(agent_index) AS maxAgentIndex
        FROM ar_treat_record
        <where>
            <if test="commissionDto.startCreateTime != null">
                create_time &gt;= #{commissionDto.startCreateTime }
            </if>
            <if test="commissionDto.endCreateTime != null">
                AND create_time &lt;= #{commissionDto.endCreateTime }
            </if>
            <if test="commissionDto.userIds != null and commissionDto.userIds.size() > 0">
                AND user_id IN
                <foreach collection="commissionDto.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY user_id, category_type_eum
    </select>

    <select id="findPageManufacturersSummaryInfo"
            resultMap="TreatRecordDtoMap">
        SELECT user_id AS userId,
        category_type_eum AS categoryTypeEum,
        manufacturers_type_eum AS manufacturersTypeEum,
        SUM(treat_rebate_bet_amount) AS treatRebateTotalBetAmount,
        SUM(treat_rebate_win_amount) AS treatRebateTotalWinAmount,
        SUM(treat_rebate_lose_amount) AS treatRebateTotalLoseAmount,
        MAX(agent_index) AS maxAgentIndex
        FROM ar_treat_record
        <where>
            <if test="commissionDto.startCreateTime != null">
                create_time &gt;= #{commissionDto.startCreateTime }
            </if>
            <if test="commissionDto.endCreateTime != null">
                AND create_time &lt;= #{commissionDto.endCreateTime }
            </if>
            <if test="commissionDto.userIds != null and commissionDto.userIds.size() > 0">
                AND user_id IN
                <foreach collection="commissionDto.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY user_id, category_type_eum,manufacturers_type_eum
    </select>

    <select id="findPageGameSubSummaryInfo"
            resultMap="TreatRecordDtoMap">
        SELECT user_id AS userId,
        category_type_eum AS categoryTypeEum,
        manufacturers_type_eum AS manufacturersTypeEum,
        sub_type_eum AS subTypeEum ,
        SUM(treat_rebate_bet_amount) AS treatRebateTotalBetAmount,
        SUM(treat_rebate_win_amount) AS treatRebateTotalWinAmount,
        SUM(treat_rebate_lose_amount) AS treatRebateTotalLoseAmount,
        MAX(agent_index) AS maxAgentIndex
        FROM ar_treat_record
        <where>
            <if test="commissionDto.startCreateTime != null">
                create_time &gt;= #{commissionDto.startCreateTime }
            </if>
            <if test="commissionDto.endCreateTime != null">
                AND create_time &lt;= #{commissionDto.endCreateTime }
            </if>
            <if test="commissionDto.userIds != null and commissionDto.userIds.size() > 0">
                AND user_id IN
                <foreach collection="commissionDto.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY user_id, category_type_eum, manufacturers_type_eum, sub_type_eum
    </select>

</mapper>
