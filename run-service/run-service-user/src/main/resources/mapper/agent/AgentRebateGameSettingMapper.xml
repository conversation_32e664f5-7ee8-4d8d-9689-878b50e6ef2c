<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentRebateGameSettingMapper">

    <resultMap
        type="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.commission.vo.resp.AgentRebateGameRateResp"
        id="TreatWashRecordMap">
        <result property="subTypeEum" column="sub_type_eum"
                typeHandler="cn.xiaoyu.discovery.framework.run.service.common.typehandler.GameSubTypeEumTypeHandler"/>
    </resultMap>

    <select id="receiveRebateRate"
            resultMap="TreatWashRecordMap">
        select a.manufacturers_type_eum,
        a.sub_type_eum,
        a.win_amount_start,
        a.win_amount_end,
        a.receive_rebate_rate,
        a.lose_amount_start,
        a.lose_amount_end,
        a.bet_amount_start,
        a.bet_amount_end
        from ar_game_setting a
        <where>
            <if test="req.categoryTypeEum != null">
                and a.category_type_eum = #{req.categoryTypeEum}
            </if>
            <if test="req.manufacturersTypeEum != null">
                and a.manufacturers_type_eum = #{req.manufacturersTypeEum}
            </if>
            <if test="req.subTypeEum != null">
                and a.sub_type_eum = #{req.subTypeEumValue}
            </if>
        </where>
        ORDER BY a.bet_amount_start
        LIMIT #{req.pageSize} OFFSET #{req.pageNo}
    </select>

    <select id="getRebateRateTotal" resultType="java.lang.Long">
        select COUNT(1)
        from ar_game_setting a
        <where>
            <if test="req.categoryTypeEum != null">
                and a.category_type_eum = #{req.categoryTypeEum}
            </if>
            <if test="req.manufacturersTypeEum != null">
                and a.manufacturers_type_eum = #{req.manufacturersTypeEum}
            </if>
            <if test="req.subTypeEum != null">
                and a.sub_type_eum = #{req.subTypeEum}
            </if>
        </where>
    </select>

</mapper>
