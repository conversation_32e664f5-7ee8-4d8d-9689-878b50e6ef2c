<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.user.UserLevelMapper">

    <select id="getAllLevelList"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.activity.vo.resp.ActivityLevelResp">
        select u.id, u.level_eum
        from u_level u
    </select>

</mapper>
