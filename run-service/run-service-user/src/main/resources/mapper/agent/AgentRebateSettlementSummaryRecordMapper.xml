<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentRebateSettlementSummaryRecordMapper">

    <select id="sumSettledCommissionAmount" resultType="java.math.BigDecimal">
        SELECT SUM(settlement_rebate_total_bet_amount)
        FROM ar_settlement_summary_record
        WHERE user_id = #{userId}
    </select>
    <select id="findRebateAmountInfo"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.agent.vo.resp.RebateAmountDataResp">
        SELECT COALESCE(SUM(settlement_rebate_total_bet_amount), 0) AS rebateBetAmount
        , COALESCE(SUM(settlement_rebate_total_win_amount), 0) AS rebateWinAmount
        , COALESCE(SUM(settlement_rebate_total_lose_amount), 0) AS rebateLoseAmount
        FROM ar_settlement_summary_record
        <where>
            <if test="req.startCreateTime != null">
                and settlement_start_time &gt;= #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                and settlement_end_time &lt;= #{req.endCreateTime}
            </if>
            <if test="req.userCode != null">
                and user_code = #{req.userCode}
            </if>
            <if test="req.username != null">
                and username = #{req.username}
            </if>
        </where>
    </select>
    <select id="sumCommissionAmount" resultType="java.math.BigDecimal">
        SELECT sum(settlement_rebate_total_bet_amount) commissionAmount
        from ar_settlement_summary_record
        WHERE user_id =
        #{userId}
        <if test="startTime != null">
            and settlement_start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and settlement_end_time &lt;= #{endTime}
        </if>
    </select>
</mapper>
