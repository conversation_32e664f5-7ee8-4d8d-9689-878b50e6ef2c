<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.user.UserHierarchicalMapper">

    <select id="getAllHierarchicalList"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.activity.vo.resp.ActivityHierarchicalResp">
        select u.id, u.hierarchical_name
        from u_hierarchical u
    </select>

</mapper>
