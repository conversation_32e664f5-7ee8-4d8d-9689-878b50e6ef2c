<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentRebateSettlementRecordMapper">

    <!--    <select id="findPageSettlementInfosReq"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.vo.resp.AgentSettlementInfoResp"-->
    <!--    >-->
    <!--        select u.username, s.settlement_rebate_bet_amount as treatRebateBetAmount, s.create_time as createTime from-->
    <!--        u_user u,-->
    <!--        ar_settlement_record s where-->
    <!--        u.id = s.bet_source_user_id and s.user_id = #{userId}-->
    <!--        <if test="req.userUnderDirectlyEum != null">-->
    <!--            and s.source_user_is_directly_under = #{req.userUnderDirectlyEum}-->
    <!--        </if>-->
    <!--        <if test="req.betSourceUserId != null">-->
    <!--            and s.bet_source_user_id = #{req.betSourceUserId}-->
    <!--        </if>-->
    <!--        <if test="req.startCreateTime != null">-->
    <!--            and s.create_time &gt;= #{req.startCreateTime}-->
    <!--        </if>-->
    <!--        <if test="req.endCreateTime != null">-->
    <!--            and s.create_time &lt;= #{req.endCreateTime}-->
    <!--        </if>-->
    <!--        order by s.create_time desc-->
    <!--    </select>-->

    <!--    <select id="countSettlementInfo"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.agent.vo.resp.CountSettlementInfoResp"-->
    <!--    >-->
    <!--        select COUNT(1) as userCount,-->
    <!--        SUM(s.treat_rebate_bet_amount) as totalTreatRebateBetAmount,-->
    <!--        SUM(s.treat_rebate_bet_amount) as totalSettlementRebateBetAmount,-->
    <!--        s.source_user_is_directly_under as sourceUserIsDirectlyUnder-->
    <!--        from ar_settlement_record s where s.user_id = #{userId}-->
    <!--        <if test="startCreateTime != null">-->
    <!--            and s.create_time &gt;= #{startCreateTime}-->
    <!--        </if>-->
    <!--        <if test="endCreateTime != null">-->
    <!--            and s.create_time &lt;= #{endCreateTime}-->
    <!--        </if>-->
    <!--        group by s.source_user_is_directly_under-->
    <!--    </select>-->

    <!--    查询指定用户时间范围内的已结算佣金-->
</mapper>
