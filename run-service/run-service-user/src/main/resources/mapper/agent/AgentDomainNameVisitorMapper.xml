<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentDomainNameVisitorMapper">

    <select id="findTotalByDomainAndTime" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(A.visitors), 0)
        FROM ap_domain_name_record A
        WHERE IF(#{domainName} IS NOT NULL, A.domain_name = #{domainName}, FALSE)
    </select>

</mapper>
