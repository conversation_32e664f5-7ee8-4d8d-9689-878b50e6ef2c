<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.agent.AgentRebateSettlementJobRecordMapper">

    <select id="getMaxEndDayJobRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.agent.AgentRebateSettlementJobRecordEntity">
        SELECT *
        FROM ar_settlement_job_record a
        where a.end_day_time = (SELECT MAX(end_day_time) FROM ar_settlement_job_record)
    </select>

</mapper>
