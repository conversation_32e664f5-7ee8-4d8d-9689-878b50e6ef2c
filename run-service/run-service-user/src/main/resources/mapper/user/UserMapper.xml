<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.user.UserMapper">

    <!--    <select id="findUserExtById"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.dto.user.UserExtDto">-->
    <!--        SELECT u.id,-->
    <!--               u.user_code,-->
    <!--               u.platform_id,-->
    <!--               u.real_name,-->
    <!--               u.raw_real_name,-->
    <!--               u.id_card,-->
    <!--               u.parent_id,-->
    <!--               u.level_id,-->
    <!--               u.nickname,-->
    <!--               u.age,-->
    <!--               u.sex_eum,-->
    <!--               u.username,-->
    <!--               u.email,-->
    <!--               u.phone_prefix,-->
    <!--               u.phone,-->
    <!--               u.account_status_eum,-->
    <!--               u.tester,-->
    <!--               u.account_type_eum,-->
    <!--               l.level_eum,-->
    <!--               h.hierarchical_name,-->
    <!--               h.allow_recharge,-->
    <!--               h.allow_withdraw,-->
    <!--               h.allow_receive_wash,-->
    <!--               h.allow_receive_agent_commission,-->
    <!--               h.allow_receive_activity_discount-->
    <!--        FROM u_user u-->
    <!--                 LEFT JOIN u_level l ON u.level_id = l.id-->
    <!--                 LEFT JOIN u_hierarchical h ON u.hierarchical_id = h.id-->
    <!--        WHERE u.id = #{userId}-->
    <!--    </select>-->

    <!--    <select id="findUserCommonInfoByIds"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.api.user.vo.resp.FindUserCommonInfoResp">-->
    <!--        select id,user_code,username from u_user where id in-->
    <!--        <foreach collection="ids" item="id" open="(" separator="," close=")">-->
    <!--            #{id}-->
    <!--        </foreach>-->
    <!--    </select>-->

    <!--    <select id="findUserCommonInfoByCodes"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.api.user.vo.resp.FindUserCommonInfoResp">-->
    <!--        select id,user_code,username from u_user where user_code in-->
    <!--        <foreach collection="codes" item="code" open="(" separator="," close=")">-->
    <!--            #{code}-->
    <!--        </foreach>-->
    <!--    </select>-->


    <!-- 会员报表查询条件 -->
    <sql id="findUserReport">
        <where>
            <if test="req.userCode != null">
                and A.user_code = #{req.userCode}
            </if>
            <if test="req.startCreateTime != null">
                and A.create_time &gt;= #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                and A.create_time &lt;= #{req.endCreateTime}
            </if>
            <if test="req.parentId != null">
                <choose>
                    <when test="req.isQueryDirectly">
                        and A.parent_id = #{req.parentId}
                    </when>
                    <otherwise>
                        and FIND_IN_SET(#{req.parentId}, A.parent_ids)
                    </otherwise>
                </choose>
            </if>
            <if test="req.tester != null">
                and A.tester = #{req.tester}
            </if>
            <if test="req.phone != null">
                and A.phone = #{req.phone}
            </if>
        </where>
    </sql>

    <select id="findUserByUserCodeAndStartAndEndTime"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.top.member.vo.resp.UserReportMenuResp">
        select A.id, A.user_code, A.username, A.parent_id, A.create_time AS regTime, A.phone AS phone
        from u_user A
        <include refid="findUserReport"></include>
        ORDER BY A.create_time DESC
        <if test="req.pageNo != null">
            LIMIT #{req.pageSize}
            OFFSET #{req.pageNo}
        </if>
    </select>

    <select id="findUserByRules" resultType="java.lang.Long">
        select COUNT(1)
        from u_user A
        <include refid="findUserReport"></include>
    </select>


    <!--    <select id="findUserIdsByUserCodes"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserEntity">-->
    <!--        select id, user_code-->
    <!--        from u_user-->
    <!--        where user_code in-->
    <!--        <foreach collection="codes" item="code" open="(" separator="," close=")">-->
    <!--            #{code}-->
    <!--        </foreach>-->
    <!--    </select>-->
    <select id="sumDirectTotalUserNum" resultType="java.lang.Long">
        SELECT SUM(1) FROM u_user A
        <where>
            A.parent_id = #{userCode}
            <if test="startCreateTime != null">
                and A.create_time &gt;= #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                and A.create_time &lt;= #{endCreateTime}
            </if>
        </where>
    </select>

    <select id="sumTotalUserNum" resultType="java.lang.Long">
        SELECT SUM(1) FROM u_user A
        <where>
            FIND_IN_SET(#{userCode},parent_ids)
            <if test="startCreateTime != null">
                and A.create_time &gt;= #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                and A.create_time &lt;= #{endCreateTime}
            </if>
        </where>

    </select>

    <select id="sumThreeModelTotalUserNum" resultType="java.lang.Long">
        SELECT SUM(1) FROM u_user A
        <where>
            FIND_IN_SET(#{userCode},parent_ids) = 2
            <if test="startCreateTime != null">
                and A.create_time &gt;= #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                and A.create_time &lt;= #{endCreateTime}
            </if>
        </where>

    </select>
    <!--    <select id="getAgentMember"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.agent.vo.resp.AgentMemberResp">-->
    <!--        SELECT id,-->
    <!--               user_code,-->
    <!--               (SELECT COUNT(1) FROM u_user B WHERE B.parent_id = A.user_code) AS directCount,-->
    <!--               create_time-->
    <!--        FROM u_user A-->
    <!--        WHERE A.parent_id = #{userCode}-->
    <!--    </select>-->

    <select id="findRegUserInfo" parameterType="cn.xiaoyu.discovery.framework.run.common.domain.vo.req.CreateTimeReq"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.api.user.vo.resp.SimpleUserInfoResp">
        select id,
               username,
               recharger
        from u_user
        where create_time &gt;= #{req.startCreateTime}
          AND create_time &lt;= #{req.endCreateTime}
    </select>

    <select id="findFirstRechargeUserId" resultType="java.lang.Long">
        select id
        from u_user
        where recharge_time &gt;= #{req.startCreateTime}
          and recharge_time &lt;= #{req.endCreateTime}
    </select>

    <select id="queryWhetherTesterIdsByReq" resultType="java.lang.Long">
        select u.id from u_user u
        <where>
            <if test="userIds!=null">
                u.id in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            and u.tester = #{tester}
        </where>
    </select>
    <select id="getOthderUserIds" resultType="java.lang.Long">
        SELECT id
        FROM u_user
        WHERE FIND_IN_SET(#{parentId}, parent_ids)
          AND parent_id != #{parentId}
    </select>
    <select id="getUserIdsByParentIds" resultType="java.lang.Long">
        SELECT id
        FROM u_user
        WHERE FIND_IN_SET(#{parentId}, parent_ids)
    </select>
    <select id="getUserIdsByHierarchicalId" resultType="java.lang.Long">
        SELECT id
        FROM u_user
        WHERE hierarchical_id = #{hierarchicalId}
    </select>

    <!--    <select id="findTransferUserInfoByUserCode"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.agent.vo.resp.TransferUserInfoResp">-->
    <!--        SELECT u.platform_id                as platformId,-->
    <!--               u.parent_ids                 as parentIds,-->
    <!--               u.parent_id                  as parentId,-->
    <!--               u.user_code                  as userCode,-->
    <!--               u.username                   as username,-->
    <!--               u.real_name                  as realName,-->
    <!--               u.create_time                as createTime,-->
    <!--               u.tester                     as tester,-->
    <!--               t.directly_under_total_count as directlyUnderTotalCount,-->
    <!--               t.other_total_count          as otherTotalCount-->
    <!--        FROM u_user u-->
    <!--                 LEFT JOIN ar_team t ON u.user_code = t.user_code-->
    <!--        WHERE u.user_code = #{userCode}-->
    <!--    </select>-->
    <!--    <select id="findUserExportData"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.dto.excel.UserInfoDto">-->
    <!--        SELECT A.*,-->
    <!--        B.total_bet_amount AS betAmount,-->
    <!--        B.total_profit_amount AS profitAmount,-->
    <!--        B.total_recharge_amount AS rechargeAmount,-->
    <!--        B.total_withdraw_amount AS withdrawAmount,-->
    <!--        B.balance AS balance,-->
    <!--        B.recharge_count AS rechargeCount-->
    <!--        FROM u_user A-->
    <!--        LEFT JOIN u_data_total B-->
    <!--        ON A.platform_id = B.platform_id-->
    <!--        AND A.id = B.user_id-->
    <!--        <where>-->
    <!--            <if test="req.startCreateTime != null">-->
    <!--                and A.create_time &gt;= #{req.startCreateTime}-->
    <!--            </if>-->
    <!--            <if test="req.endCreateTime != null">-->
    <!--                and A.create_time &lt;= #{req.endCreateTime}-->
    <!--            </if>-->
    <!--            <if test="req.username != null">-->
    <!--                and A.username = #{req.username}-->
    <!--            </if>-->
    <!--            <if test="req.rawRealName != null">-->
    <!--                and A.raw_real_name = #{req.rawRealName}-->
    <!--            </if>-->
    <!--            <if test="req.nickname != null">-->
    <!--                and A.nickname = #{req.nickname}-->
    <!--            </if>-->
    <!--            <if test="req.levelId != null">-->
    <!--                and A.level_id = #{req.levelId}-->
    <!--            </if>-->
    <!--            <if test="req.hierarchicalId != null">-->
    <!--                and A.hierarchical_id = #{req.hierarchicalId}-->
    <!--            </if>-->
    <!--            <if test="req.lastLoginIp != null">-->
    <!--                and A.last_login_ip = #{req.lastLoginIp}-->
    <!--            </if>-->
    <!--            <if test="req.parentId != null">-->
    <!--                and A.parent_id = #{req.parentId}-->
    <!--            </if>-->
    <!--            <if test="req.accountStatusEum != null">-->
    <!--                and A.account_status_eum = #{req.accountStatusEum}-->
    <!--            </if>-->
    <!--            <if test="req.userCodeList != null and req.userCodeList.size() > 0">-->
    <!--                AND A.user_code in-->
    <!--                <foreach collection="req.userCodeList" item="userCode" open="(" separator="," close=")">-->
    <!--                    #{userCode}-->
    <!--                </foreach>-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        ORDER BY A.create_time DESC-->
    <!--    </select>-->
    <!--    <select id="queryUserIdByTester" resultType="java.lang.Long">-->
    <!--        SELECT id-->
    <!--        FROM u_user-->
    <!--        WHERE tester = #{tester}-->
    <!--    </select>-->
    <!--    <select id="queryUserDataByRegTime"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.dto.UserStatisticsDataDto">-->
    <!--        SELECT u.id, recharger, recharge_time, tester-->
    <!--        FROM u_user u-->
    <!--        WHERE u.create_time BETWEEN #{startCreateTime} AND #{endCreateTime}-->
    <!--    </select>-->


    <!--    <select id="findUserCountBy" resultType="java.lang.Long">-->
    <!--        <choose>-->
    <!--            <when test="lowestLevel == null">-->
    <!--                select count(1) from u_user u-->
    <!--                <where>-->
    <!--                    <if test="hierarchicalId != null">-->
    <!--                        and u.hierarchical_id = #{hierarchicalId}-->
    <!--                    </if>-->
    <!--                </where>-->
    <!--            </when>-->
    <!--            <otherwise>-->
    <!--                select count(1) from u_user u-->
    <!--                LEFT JOIN u_level l on l.id = u.level_id-->
    <!--                where l.level_eum BETWEEN #{lowestLevel} and #{highestLevel}-->
    <!--            </otherwise>-->
    <!--        </choose>-->

    <!--    </select>-->

    <!--    id 可能重复，这边把levelId 和 层级ID 分开放-->
    <!--    <select id="queryUserCountGroupByLevelAndHierarchical"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.user.vo.resp.QueryUserCountGroupResp">-->
    <!--        select count(id) as userCount, level_id as levelId, '' as hierarchicalId-->
    <!--        from u_user-->
    <!--        GROUP BY level_id-->
    <!--        UNION-->
    <!--        select count(id) as userCount, '' as levelId, hierarchical_id as hierarchicalId-->
    <!--        from u_user-->
    <!--        GROUP BY hierarchical_id-->
    <!--    </select>-->

    <!--    <select id="selectRepeatPhone"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.user.UserEntity">-->
    <!--        SELECT u.id,-->
    <!--               u.email,-->
    <!--               u.username,-->
    <!--               u.phone,-->
    <!--               u.platform_id,-->
    <!--               u.user_code,-->
    <!--               u.create_time,-->
    <!--               a.coun-->
    <!--        FROM u_user u-->
    <!--                 JOIN(SELECT phone, COUNT(1) as coun-->
    <!--                      FROM u_user-->
    <!--                      WHERE phone is not NULL-->
    <!--                      GROUP BY phone-->
    <!--                      HAVING COUNT(1) > 1) as a-->
    <!--                     ON a.phone = u.phone-->
    <!--        WHERE u.phone is not NULL-->
    <!--    </select>-->


    <!--    <select id="findCurrencyByUserIds"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.api.user.vo.resp.UserIdAndCurrencyResp">-->
    <!--        select u.id as userId,u.currency_eum as currencyEum from u_user u where u.id in-->
    <!--        <foreach collection="userIds" item="id" open="(" separator="," close=")">-->
    <!--            #{id}-->
    <!--        </foreach>-->
    <!--    </select>-->

</mapper>
