<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.user.ChannelLtvRecordMapper">

    <select id="findPageRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.user.ChannelLtvRecordEntity">
        SELECT query_date, channel,
        new_reg_num, reg_and_pay_num, new_reg_and_pay_rate, new_reg_pay_amount, arppu, new_reg_withdraw_num,
        new_reg_withdraw_amount, income, ltv1, ltv3, ltv7, ltv15, ltv30, ltv60, ltv90, ltv120, ltv180
        FROM u_channel_ltv_record
        <where>
            <if test="req.channels != null and req.channels.size() > 0">
                AND channel in
                <foreach collection="req.channels" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>
            <if test="dateList != null and dateList.size() > 0">
                AND query_date in
                <foreach collection="dateList" item="date" open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
        </where>
        ORDER BY query_date DESC, channel ASC
        LIMIT #{req.pageSize}
        OFFSET #{req.pageNo}
    </select>

    <select id="findPageRecordTotal"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.user.ChannelLtvRecordEntity">
        SELECT query_date, channel,
        new_reg_num, reg_and_pay_num, new_reg_and_pay_rate, new_reg_pay_amount, arppu, new_reg_withdraw_num,
        new_reg_withdraw_amount, income, ltv1, ltv3, ltv7, ltv15, ltv30, ltv60, ltv90, ltv120, ltv180
        FROM u_channel_ltv_record
        <where>
            <if test="req.channels != null and req.channels.size() > 0">
                AND channel in
                <foreach collection="req.channels" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>
            <if test="dateList != null and dateList.size() > 0">
                AND query_date in
                <foreach collection="dateList" item="date" open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
        </where>
        ORDER BY query_date DESC, channel ASC
    </select>

</mapper>
