<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.user.logs.UserRegLogMapper">

    <!--    <select id="countDomainNameRegisterNum"-->
    <!--            resultType="java.lang.Long">-->
    <!--        SELECT COUNT(reg_source)-->
    <!--        FROM u_reg_log A-->
    <!--        INNER JOIN u_user B-->
    <!--        ON A.user_id = B.id-->
    <!--        <if test="req.hasTester != null">-->
    <!--            and B.tester = #{req.hasTester}-->
    <!--        </if>-->
    <!--        WHERE (reg_source in-->
    <!--        (SELECT domain_name FROM ap_everyone_domain_name)-->
    <!--        OR reg_source in-->
    <!--        (SELECT domain_name FROM ap_cooperate_domain_name))-->
    <!--        <if test="req.startCreateTime != null">-->
    <!--            and A.create_time &gt;= #{req.startCreateTime}-->
    <!--        </if>-->
    <!--        <if test="req.endCreateTime != null">-->
    <!--            and A.create_time &lt;= #{req.endCreateTime}-->
    <!--        </if>-->
    <!--    </select>-->

    <!--    <select id="getChannelRegData"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.backstage.report.vo.resp.RegChannelResp">-->
    <!--        SELECT A.reg_source,-->
    <!--        A.user_id,-->
    <!--        B.recharger,-->
    <!--        B.recharge_time,-->
    <!--        A.create_time-->
    <!--        FROM u_reg_log A-->
    <!--        INNER JOIN u_user B-->
    <!--        ON A.user_id = B.id-->
    <!--        <where>-->
    <!--            <if test="channels != null and channels.size() > 0">-->
    <!--                A.reg_source in-->
    <!--                <foreach collection="channels" item="channel" open="(" separator="," close=")">-->
    <!--                    #{channel}-->
    <!--                </foreach>-->
    <!--            </if>-->
    <!--            <if test="startTime != null">-->
    <!--                and B.create_time &gt;= #{startTime}-->
    <!--            </if>-->
    <!--            <if test="endTime != null">-->
    <!--                and B.create_time &lt;= #{endTime}-->
    <!--            </if>-->
    <!--        </where>-->

    <!--    </select>-->


    <!--    <select id="findUserByLog"-->
    <!--            resultType="cn.xiaoyu.discovery.framework.run.service.user.dto.user.UserRegLogDto">-->
    <!--        SELECT A.id,-->
    <!--         A.reg_source,-->
    <!--               B.parent_id,-->
    <!--               A.reg_channel_code-->
    <!--        FROM u_reg_log A-->
    <!--        LEFT JOIN u_user B ON A.user_id = B.id-->
    <!--        WHERE B.parent_id IS NOT NULL-->
    <!--          AND A.reg_source IS NOT NULL-->
    <!--          AND A.reg_channel_code IS NOT NULL-->
    <!--    </select>-->

</mapper>
