<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.user.UniversalPromotionEffectRecordMapper">

    <select id="findPageRecordByReq"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.user.UniversalPromotionEffectRecordEntity">
        SELECT id,
        query_date,
        user_code,
        domain_name,
        promotion_code,
        today_reg_num,
        today_recharge_num,
        today_first_recharge_num,
        today_recharge_amount,
        next_day_recharge,
        twice_day_recharge,
        third_day_recharge,
        seven_day_recharge,
        fifteen_day_recharge,
        thirty_day_recharge,
        promotion_total_num,
        total_recharge_amount,
        bet_total_amount,
        bet_today_amount
        FROM u_universal_promotion_record
        <where>
            <if test="req.userCodes != null and req.userCodes.size() > 0">
                AND user_code in
                <foreach collection="req.userCodes" item="userCode" open="(" separator="," close=")">
                    #{userCode}
                </foreach>
            </if>
            <if test="dateList != null and dateList.size() > 0">
                AND query_date in
                <foreach collection="dateList" item="date" open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
            <if test="req.inviteCodes != null and req.inviteCodes.size() > 0">
                AND promotion_code in
                <foreach collection="req.inviteCodes" item="inviteCode" open="(" separator="," close=")">
                    #{inviteCode}
                </foreach>
            </if>
            <if test="req.domainName != null">
                AND domain_name = #{req.domainName}
            </if>
            <if test="req.accumulateStartNum != null">
                AND promotion_total_num >= #{req.accumulateStartNum}
            </if>
            <if test="req.accumulateEndNum != null">
                AND promotion_total_num &lt;= #{req.accumulateEndNum}
            </if>
            <if test="req.rechargeStartAmount != null">
                AND total_recharge_amount >= #{req.rechargeStartAmount}
            </if>
            <if test="req.rechargeEndAmount != null">
                AND total_recharge_amount &lt;= #{req.rechargeEndAmount}
            </if>
        </where>
        ORDER BY query_date DESC, user_code DESC, promotion_code ASC
        LIMIT #{req.pageSize}
        OFFSET #{req.pageNo}
    </select>

    <select id="findPageTotalByReq"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.user.UniversalPromotionEffectRecordEntity">
        SELECT id,
        query_date,
        user_code,
        domain_name,
        promotion_code,
        today_reg_num,
        today_recharge_num,
        today_first_recharge_num,
        today_recharge_amount,
        next_day_recharge,
        twice_day_recharge,
        third_day_recharge,
        seven_day_recharge,
        fifteen_day_recharge,
        thirty_day_recharge,
        promotion_total_num,
        total_recharge_amount,
        bet_total_amount,
        bet_today_amount
        FROM u_universal_promotion_record
        <where>
            <if test="req.userCodes != null and req.userCodes.size() > 0">
                AND user_code in
                <foreach collection="req.userCodes" item="userCode" open="(" separator="," close=")">
                    #{userCode}
                </foreach>
            </if>
            <if test="dateList != null and dateList.size() > 0">
                AND query_date in
                <foreach collection="dateList" item="date" open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
            <if test="req.inviteCodes != null and req.inviteCodes.size() > 0">
                AND promotion_code in
                <foreach collection="req.inviteCodes" item="inviteCode" open="(" separator="," close=")">
                    #{inviteCode}
                </foreach>
            </if>
            <if test="req.domainName != null">
                AND domain_name = #{req.domainName}
            </if>
            <if test="req.accumulateStartNum != null">
                AND promotion_total_num >= #{req.accumulateStartNum}
            </if>
            <if test="req.accumulateEndNum != null">
                AND promotion_total_num &lt;= #{req.accumulateEndNum}
            </if>
            <if test="req.rechargeStartAmount != null">
                AND total_recharge_amount >= #{req.rechargeStartAmount}
            </if>
            <if test="req.rechargeEndAmount != null">
                AND total_recharge_amount &lt;= #{req.rechargeEndAmount}
            </if>
        </where>
        ORDER BY query_date DESC, user_code DESC, promotion_code ASC
    </select>

</mapper>
