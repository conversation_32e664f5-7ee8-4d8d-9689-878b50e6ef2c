<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.task.TaskMapper">

    <select id="getTaskAgingTypeList"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskAgingTypeEum">
        SELECT t.task_aging_type_eum
        FROM t_task t
        WHERE t.status_eum = 0
        GROUP BY t.task_aging_type_eum
        ORDER BY t.task_aging_type_eum
    </select>

    <select id="getTaskTypeList"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.eum.activity.TaskTypeEum">
        SELECT t.task_type_eum
        FROM t_task t
        WHERE t.task_aging_type_eum = #{req.taskAgingTypeEum}
          AND t.status_eum = 0
        ORDER BY t.sort
    </select>

</mapper>
