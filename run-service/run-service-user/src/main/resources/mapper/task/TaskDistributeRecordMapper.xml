<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.task.TaskDistributeRecordMapper">

    <select id="getSumPayAmount"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.task.TaskDistributeRecordEntity">
        SELECT receive_amount
        FROM t_task_distribute_record A
        <where>
            <if test="req.title != null">
                AND A.title = #{req.title}
            </if>
            <if test="req.taskTypeEum != null">
                AND A.task_type_eum = #{req.taskTypeEum}
            </if>
            <if test="req.userCode != null">
                AND A.user_code = #{req.userCode}
            </if>
            <if test="req.username != null">
                AND A.username = #{req.username}
            </if>
            <if test="req.startCreateTime != null">
                AND A.create_time >= #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND A.create_time &lt;= #{req.endCreateTime}
            </if>
        </where>
    </select>

</mapper>












