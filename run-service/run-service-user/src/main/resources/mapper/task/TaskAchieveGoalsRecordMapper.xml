<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.task.TaskAchieveGoalsRecordMapper">

    <select id="findRecordByTaskIdAndItemIdAndTime"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.task.TaskAchieveGoalsRecordEntity">
        SELECT id,
               complete_amount,
               task_status_eum,
               user_id,
               username,
               user_code,
               create_time
        FROM t_task_achieve_goals_record A
        WHERE A.task_id = #{taskId}
          AND A.item_id = #{itemId}
          AND A.user_id = #{userId}
          AND A.aging_range = #{agingRange}
          AND A.create_time >= #{startCreateTime}
          AND A.create_time &lt;= #{endCreateTime}
    </select>


    <select id="getSumTaskAmount"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.entity.task.TaskAchieveGoalsRecordEntity">
        SELECT complete_amount, item_id
        FROM t_task_achieve_goals_record A
        <where>
            <if test="req.title != null">
                AND A.title = #{req.title}
            </if>
            <if test="req.taskStatusEum != null">
                AND A.task_status_eum = #{req.taskStatusEum}
            </if>
            <if test="req.userCode != null">
                AND A.user_code = #{req.userCode}
            </if>
            <if test="req.username != null">
                AND A.username = #{req.username}
            </if>
            <if test="req.startCreateTime != null">
                AND A.create_time >= #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND A.create_time &lt;= #{req.endCreateTime}
            </if>
        </where>
    </select>

    <select id="selectByTime" resultType="java.lang.Long">
        SELECT id
        FROM t_task_achieve_goals_record
        WHERE create_time &gt;= #{startTime}
          AND create_time &lt;= #{endTime}
    </select>

    <update id="batchUpdateRecordList">
        <foreach collection="list" item="item" separator=";">
            UPDATE
            t_task_achieve_goals_record
            SET
            complete_amount = #{item.completeAmount}
            WHERE
            id = #{item.id} AND
            create_time = #{item.createTime}
        </foreach>
    </update>

</mapper>
