<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.wash.WashBatchMapper">

    <select id="getWashStatistics"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.api.wash.resp.GetWashStatisticsResp">
        select
        count(id) as washNum,
        IFNULL(sum(wb.wash_total_amount),0) AS washTotalAmount,
        count(DISTINCT user_id) as washPeoples
        from w_batch wb
        <where>
            <if test="req.startCreateTime != null">
                create_time <![CDATA[ >= ]]> #{req.startCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                and create_time <![CDATA[ <= ]]> #{req.endCreateTime}
            </if>
        </where>
    </select>
    <select id="selectByTime" resultType="java.lang.Long">
        SELECT id
        FROM w_batch
        WHERE create_time &gt;= #{startTime}
          AND create_time &lt;= #{endTime}
    </select>


</mapper>
