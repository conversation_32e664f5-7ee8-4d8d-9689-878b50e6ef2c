<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.wash.WashRecordMapper">

    <sql id="findWhere">
        <if test="req.categoryTypeEum != null">
            and w.category_type_eum = #{req.categoryTypeEum}
        </if>

        <if test="req.manufacturersTypeEum != null">
            and w.manufacturers_type_eum = #{req.manufacturersTypeEum}
        </if>

        <if test="req.subTypeEum != null">
            and w.sub_type_eum = #{req.subTypeEum}
        </if>

        <if test="req.startCreateTime != null and req.endCreateTime != null">
            and w.create_time BETWEEN #{req.startCreateTime} AND #{req.endCreateTime}
        </if>
    </sql>

    <select id="queryUserWashTotal"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashUserTotalResp">
        SELECT
        sum( w.wash_amount ) AS 'washTotalAmount',
        sum( w.wash_bet_amount ) AS 'betTotalAmount'
        FROM
        w_record w where w.user_id = #{req.userId}
        <include refid="findWhere"/>
    </select>

    <select id="queryByBatchId"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashRecordResp">
        SELECT category_type_eum,
               manufacturers_type_eum,
               sub_type_eum as 'subTypeValue', wash_bet_amount,
               wash_rate,
               wash_amount,
               wash_bet_multiple,
               create_time,
               update_time
        FROM w_record w
        where w.batch_id = #{req.batchId}
          AND create_time &gt;= #{req.startCreateTime}
          AND create_time &lt;= #{req.endCreateTime}
    </select>
    <select id="selectByTime" resultType="java.lang.Long">
        SELECT id
        FROM w_record
        WHERE create_time &gt;= #{startTime}
          AND create_time &lt;= #{endTime}
    </select>
</mapper>
