<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.wash.WashControlMapper">

    <sql id="findWhere">
        <where>
            <if test="req.levelEum != null">
                and w.level_eum = #{req.levelEum}
            </if>

            <if test="req.categoryTypeEum != null">
                and w.category_type_eum = #{req.categoryTypeEum}
            </if>

            <if test="req.manufacturersTypeEum != null">
                and w.manufacturers_type_eum = #{req.manufacturersTypeEum}
            </if>

            <if test="req.subTypeEum != null">
                and w.sub_type_eum = #{req.subTypeEum}
            </if>

            <if test="req.startCreateTime != null and req.endCreateTime != null">
                and w.create_time BETWEEN #{req.startCreateTime} AND #{req.endCreateTime}
            </if>
            <if test="req.settingType != null and req.settingType !=''">
                and
                <choose>
                    <when test="req.settingType == 'category_type_eum'">
                        w.category_type_eum is not null and w.manufacturers_type_eum is null and w.sub_type_eum is null
                    </when>
                    <when test="req.settingType == 'manufacturers_type_eum'">
                        w.manufacturers_type_eum is not null and w.sub_type_eum is null
                    </when>
                    <otherwise>
                        w.sub_type_eum is not null
                    </otherwise>
                </choose>
            </if>
        </where>

    </sql>

    <select id="findPageWashControlEntity"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashControlResp">
        select
        w.id,w.level_eum,w.category_type_eum,w.manufacturers_type_eum,w.sub_type_eum as
        'subTypeValue',w.bet_amount_start,w.bet_amount_end,w.wash_rate
        from
        w_control w
        <include refid="findWhere"/>
    </select>


    <select id="findListWashControlEntity"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashControlResp">
        select w.id,w.level_eum,w.sub_type_eum,w.bet_amount_start,w.bet_amount_end,w.wash_rate from w_control w
        <include refid="findWhere"/>
    </select>


</mapper>
