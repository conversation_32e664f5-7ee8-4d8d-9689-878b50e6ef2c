<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xiaoyu.discovery.framework.run.service.user.mapper.wash.TreatWashRecordMapper">

    <resultMap type="cn.xiaoyu.discovery.framework.run.service.user.entity.wash.TreatWashRecordEntity"
               id="TreatWashRecordMap">
        <result property="id" column="id"/>
        <result property="categoryTypeEum" column="category_type_eum"/>
        <result property="manufacturersTypeEum" column="manufacturers_type_eum"/>
        <result property="subTypeEum" column="sub_type_eum"
                typeHandler="cn.xiaoyu.discovery.framework.run.service.common.typehandler.GameSubTypeEumTypeHandler"/>
        <result property="treatWashBetAmount" column="treat_wash_bet_amount"/>
        <result property="statusEum" column="status_eum"/>
        <result property="userId" column="user_id"/>
        <result property="userCode" column="user_code"/>
        <result property="username" column="username"/>
        <result property="platformId" column="platform_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="CommonWhere">
        <where>
            <if test="req.userId != null">
                and w.user_id = #{req.userId}
            </if>

            <if test="req.statusEum != null">
                and w.status_eum = #{req.statusEum}
            </if>

            <if test="req.categoryTypeEum != null">
                and w.category_type_eum = #{req.categoryTypeEum}
            </if>

            <if test="req.manufacturersTypeEum != null">
                and w.manufacturers_type_eum = #{req.manufacturersTypeEum}
            </if>

            <if test="req.subTypeEumValue != null">
                and w.sub_type_eum = #{req.subTypeEumValue}
            </if>
            <if test="req.startCreateTime != null and req.endCreateTime != null">
                AND w.create_time &gt;= #{req.startCreateTime}
                AND w.create_time &lt;= #{req.endCreateTime}
            </if>

        </where>

    </sql>

    <select id="groupFindTreatWashRecord"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashTreatWashRecordResp">
        SELECT ${req.field},sum(treat_wash_bet_amount) as 'treatWashBetAmount',count(1) as 'count'
        FROM w_treat_record w
        <include refid="CommonWhere"/>
        GROUP BY ${req.field}
    </select>

    <select id="findListTreatWashRecordEntity"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.controller.frontdesk.app.vertical.wash.vo.resp.WashTreatWashRecordResp">
        select
        w.id,w.category_type_eum,w.manufacturers_type_eum,w.sub_type_eum as
        'subTypeValue',w.treat_wash_bet_amount,w.user_id,w.user_code,w.username,w.platform_id
        from w_treat_record w
        <include refid="CommonWhere"/>
    </select>

    <select id="sumTreatWashBetAmount" resultType="java.math.BigDecimal">
        select sum(w.treat_wash_bet_amount)
        from w_treat_record w
        <include refid="CommonWhere"/>
    </select>

    <select id="findSubTypeAndBetAmount"
            resultType="cn.xiaoyu.discovery.framework.run.service.user.dto.wash.WashTreatStatisticDto">
        select w.treat_wash_bet_amount as 'treatWashBetAmount',w.sub_type_eum as 'subType'
        from w_treat_record w
        <include refid="CommonWhere"/>
    </select>

    <update id="batchUpdateStatus">
        update w_treat_record set status_eum = #{status} where create_time &gt;= #{startCreateTime} AND
        create_time &lt;= #{endCreateTime} and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="queryUserRecordBySubs" resultMap="TreatWashRecordMap">
        SELECT id,create_time, category_type_eum, manufacturers_type_eum, sub_type_eum, treat_wash_bet_amount, user_id
        FROM w_treat_record
        WHERE user_id = #{req.userId}
        AND status_eum = #{req.statusEum}
        AND create_time &gt;= #{req.startTime}
        AND create_time &lt;= #{req.endTime}
        AND sub_type_eum IN
        <foreach collection="req.subValues" item="sub" open="(" separator="," close=")">
            #{sub}
        </foreach>
    </select>
    <select id="selectByTime" resultType="java.lang.Long">
        SELECT id
        FROM w_treat_record
        WHERE create_time &gt;= #{startTime}
          AND create_time &lt;= #{endTime}
          AND status_eum = 1
    </select>

</mapper>
