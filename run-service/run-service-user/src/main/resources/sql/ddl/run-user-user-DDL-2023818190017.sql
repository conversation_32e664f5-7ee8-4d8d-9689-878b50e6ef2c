DROP TABLE IF EXISTS u_user;
CREATE TABLE u_user
(
    `id`                     BIGINT NOT NULL COMMENT 'id主键',
    `real_name`              VARCHAR(90) COMMENT '真实姓名-支付提现绑卡需使用-也可直接存储在绑卡处-在联动存储此处',
    `raw_real_name`          VARCHAR(90) COMMENT '真实姓名-支付提现绑卡需使用-也可直接存储在绑卡处-在联动存储此处-存没有空格去除音标的',
    `id_card`                VARCHAR(32) COMMENT '身份证id卡-支付提现绑卡需使用-也可直接存储在绑卡处-在联动存储此处',
    `agenter`                TINYINT(1) COMMENT '是否是代理 有下级用户 代表就是代理 也就是邀请有人注册后 需要判断修改邀请人信息进行修改',
    `parent_ids`             VARCHAR(2550) COMMENT '代理链路',
    `anchorer`               TINYINT(1) COMMENT '是否是主播',
    `recharger`              TINYINT(1) COMMENT '是否充值',
    `recharge_time`          BIGINT COMMENT '首次充值时间',
    `withdrawer`             TINYINT(1) COMMENT '是否提现',
    `beter`                  TINYINT(1) COMMENT '是否下注',
    `last_level_change_time` BIGINT COMMENT '最后等级变更时间',
    `user_code`              BIGINT NOT NULL COMMENT '前台用户code',
    `parent_id`              BIGINT COMMENT '邀请来的上级id，默认0/null 代表平台 通过注册的渠道码转用户id获得',
    `level_id`               BIGINT NOT NULL COMMENT '等级id',
    `hierarchical_id`        BIGINT COMMENT '层级id',
    `porder`                 TINYINT(1) COMMENT '是否是正式账号（false游客账号、true正式账号）',
    `clcppz_eum`             INT COMMENT '所属国家枚举',
    `currency_eum`           INT COMMENT '货币',
    `security_password`      VARCHAR(255) COMMENT '安全密码',
    `jpush_register_id`      VARCHAR(255) COMMENT '极光注册id',
    `firebase_token`         VARCHAR(255) COMMENT '谷歌firebase推送token',
    `password`               VARCHAR(255) COMMENT '加密后的密码',
    `nickname`               VARCHAR(90) COMMENT '昵称',
    `age`                    INT COMMENT '年龄',
    `birthday`               VARCHAR(32) COMMENT '生日',
    `sex_eum`                INT    NOT NULL COMMENT '性别',
    `avatar`                 VARCHAR(255) COMMENT '头像-域名后的地址串',
    `google_secret`          VARCHAR(255) COMMENT '绑定的谷歌秘钥',
    `remark`                 VARCHAR(255) COMMENT '备注',
    `username`               VARCHAR(32) COMMENT '用户名',
    `email`                  VARCHAR(32) COMMENT '邮箱',
    `phone_prefix`           VARCHAR(32) COMMENT '国家码',
    `phone`                  VARCHAR(32) COMMENT '手机号码',
    `account_status_eum`     INT    NOT NULL COMMENT '帐号状态',
    `tester`                 TINYINT(1) NOT NULL COMMENT '是否是测试账号',
    `account_type_eum`       INT    NOT NULL COMMENT '帐号类型',
    `reg_ip`                 VARCHAR(32) COMMENT '注册的ip',
    `last_login_ip`          VARCHAR(32) COMMENT '最后登录IP',
    `last_login_time`        BIGINT COMMENT '最后登录时间',
    `freeze_withdraw`        TINYINT(1) DEFAULT 0 COMMENT '提现冻结',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`            BIGINT COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '更新时间',
    `create_by`              VARCHAR(32) COMMENT '更新人',
    PRIMARY KEY (id)
) COMMENT = '用户信息';


CREATE INDEX uuser_p_idx ON u_user (platform_id);
CREATE INDEX uuser_pc_idx ON u_user (platform_id, create_time);
CREATE INDEX uuser_pu_idx ON u_user (platform_id, create_time);
CREATE INDEX uuser_pucode_idx ON u_user (platform_id, user_code);
CREATE INDEX uuser_puname_idx ON u_user (username, platform_id);
CREATE INDEX uuser_pphone_idx ON u_user (phone, platform_id);
CREATE INDEX uuser_pemail_idx ON u_user (email, platform_id);
CREATE INDEX uuser_pparent_idx ON u_user (platform_id, parent_id);

DROP TABLE IF EXISTS u_hierarchical_change_record;
CREATE TABLE u_hierarchical_change_record
(
    `id`                     BIGINT NOT NULL COMMENT 'id主键',
    `before_hierarchical_id` BIGINT NOT NULL COMMENT '改变前的层级id',
    `now_hierarchical_id`    BIGINT NOT NULL COMMENT '改变后的层级id',
    `user_id`                BIGINT NOT NULL COMMENT '用户id',
    `user_code`              BIGINT NOT NULL COMMENT '用户code',
    `username`               VARCHAR(32) COMMENT '用户名',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`            BIGINT COMMENT '更新时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户层级改变记录';


CREATE INDEX uhcr_p_idx ON u_hierarchical_change_record (platform_id);
CREATE INDEX uhcr_pc_idx ON u_hierarchical_change_record (platform_id, create_time);
CREATE INDEX uhcr_puc_idx ON u_hierarchical_change_record (user_code, platform_id);

DROP TABLE IF EXISTS u_hierarchical;
CREATE TABLE u_hierarchical
(
    `id`                              BIGINT NOT NULL COMMENT '主键id',
    `hierarchical_name`               VARCHAR(90) COMMENT '层级名称',
    `defaulter`                       TINYINT(1) NOT NULL COMMENT '是否默认',
    `allow_recharge`                  TINYINT(1) COMMENT '是否允许充值',
    `allow_withdraw`                  TINYINT(1) COMMENT '是否允许提现',
    `allow_receive_wash`              TINYINT(1) COMMENT '是否允许领取返水',
    `allow_receive_agent_commission`  TINYINT(1) COMMENT '是否允许领取代理佣金',
    `allow_receive_activity_discount` TINYINT(1) COMMENT '是否允许领取活动优惠',
    `platform_id`                     BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`                     BIGINT COMMENT '更新时间',
    `update_by`                       VARCHAR(32) COMMENT '更新人',
    `create_time`                     BIGINT NOT NULL COMMENT '创建时间',
    `create_by`                       VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户层级信息';


CREATE INDEX uhierarchical_p_idx ON u_hierarchical (platform_id);
CREATE INDEX uhierarchical_pc_idx ON u_hierarchical (platform_id, create_time);
CREATE INDEX uhierarchical_pu_idx ON u_hierarchical (platform_id, update_time);
CREATE INDEX uhierarchical_pd_idx ON u_hierarchical (platform_id, defaulter);

DROP TABLE IF EXISTS u_level_change_record;
CREATE TABLE u_level_change_record
(
    `id`                 BIGINT NOT NULL COMMENT '主键id',
    `change_source`      VARCHAR(32) COMMENT '最后一笔触发的改变来源',
    `change_source_desc` VARCHAR(255) COMMENT '改变来源说明',
    `before_level_eum`   INT    NOT NULL COMMENT '改变前等级枚举',
    `up_or_down`         TINYINT(1) NOT NULL COMMENT '升级or降级-默认true升级false降级',
    `now_level_eum`      INT COMMENT '改变后等级枚举',
    `user_id`            BIGINT NOT NULL COMMENT '用户id',
    `user_code`          BIGINT NOT NULL COMMENT '用户code',
    `username`           VARCHAR(32) COMMENT '用户名',
    `platform_id`        BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`        BIGINT COMMENT '更新时间',
    `update_by`          VARCHAR(32) COMMENT '更新人',
    `create_time`        BIGINT NOT NULL COMMENT '创建时间',
    `create_by`          VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户等级改变记录';


CREATE INDEX ulevelcr_p_idx ON u_level_change_record (platform_id);
CREATE INDEX ulevelcr_pc_idx ON u_level_change_record (platform_id, create_time);
CREATE INDEX ulevelcr_puc_idx ON u_level_change_record (user_code, platform_id);

DROP TABLE IF EXISTS u_level;
CREATE TABLE u_level
(
    `id`                                       BIGINT NOT NULL COMMENT '主键id',
    `level_eum`                                INT    NOT NULL COMMENT '等级枚举',
    `level_img_url`                            VARCHAR(255) COMMENT '等级图标地址',
    `level_attachment_url`                     VARCHAR(255) COMMENT '等级附件地址-达标图标',
    `level_attachment_url2`                    VARCHAR(255) COMMENT '等级附件地址-未达标图标',
    `defaulter`                                TINYINT(1) NOT NULL COMMENT '是否默认',
    `bet_up_level_amount_start`                DECIMAL(24, 6) COMMENT '打码量升级起',
    `bet_up_level_amount_end`                  DECIMAL(24, 6) COMMENT '打码量升级止',
    `recharge_up_level_amount_start`           DECIMAL(24, 6) COMMENT '充值量升级起',
    `recharge_up_level_amount_end`             DECIMAL(24, 6) COMMENT '充值量升级止',
    `bet_down_level_amount`                    DECIMAL(24, 6) COMMENT '保留不降级的打码量',
    `recharge_down_level_amount`               DECIMAL(24, 6) COMMENT '保留不降级的充值量',
    `promotioner`                              TINYINT(1) NOT NULL COMMENT '是否可领晋级礼金',
    `promotion_amount`                         DECIMAL(24, 6) COMMENT '可领晋级礼金金额',
    `promotion_bet_multiple`                   DECIMAL(24, 6) COMMENT '可领晋级礼金金额的需求打码倍数',
    `dayer`                                    TINYINT(1) NOT NULL COMMENT '是否可领日礼金',
    `day_amount`                               DECIMAL(24, 6) COMMENT '可领日礼金金额',
    `day_bet_multiple`                         DECIMAL(24, 6) COMMENT '可领日礼金金额的需求打码倍数',
    `weeker`                                   TINYINT(1) NOT NULL COMMENT '是否可领周礼金',
    `week_amount`                              DECIMAL(24, 6) COMMENT '可领周礼金金额',
    `week_bet_multiple`                        DECIMAL(24, 6) COMMENT '可领周礼金金额的需求打码倍数',
    `week_recharge_amount`                     DECIMAL(24, 6) COMMENT '可领周礼金需要的充值金额;每个自然周内的累计充值',
    `week_pickup_time`                         INT COMMENT '周礼金可领取时间;1-7：周一到周日',
    `monther`                                  TINYINT(1) NOT NULL COMMENT '是否可领月礼金',
    `month_amount`                             DECIMAL(24, 6) COMMENT '可领月礼金金额',
    `month_bet_multiple`                       DECIMAL(24, 6) COMMENT '可领月礼金金额的需求打码倍数',
    `month_recharge_amount`                    DECIMAL(24, 6) COMMENT '可领月礼金需要的充值金额;每个自然月内累计的充值',
    `month_pickup_time`                        INT COMMENT '月礼金可领取时间;1-31;一个月的开始到结束日期',
    `task_count`                               INT COMMENT '需要完成的任务数-用于刷单类系统',
    `day_first_recharge_discounter`            TINYINT(1) NOT NULL COMMENT '是否开启每日首充赠送',
    `day_first_recharge_discount_rate`         DECIMAL(24, 6) COMMENT '每日首充赠送比例',
    `day_first_recharge_discount_bet_multiple` DECIMAL(24, 6) COMMENT '每日首充赠送金额的需求打码倍数',
    `day_first_recharge_discount_max`          DECIMAL(24, 6) COMMENT '每日首充赠送上限',
    `day_every_recharge_discounter`            TINYINT(1) NOT NULL COMMENT '是否开启每笔充值赠送',
    `day_every_recharge_discount_rate`         DECIMAL(24, 6) COMMENT '每笔充值赠送比例',
    `day_every_recharge_discount_bet_multiple` DECIMAL(24, 6) COMMENT '每笔充值赠送送金额的需求打码倍数',
    `day_every_recharge_discount_max`          DECIMAL(24, 6) COMMENT '每笔充值赠送上限',
    `withdraw_feeer`                           TINYINT(1) NOT NULL COMMENT '是否开启提现手续费',
    `withdraw_fee_rate`                        DECIMAL(24, 6) COMMENT '提现手续费比例',
    `withdraw_fee_setting`                     VARCHAR(900) COMMENT '提现手续费设置',
    `free_fee_count`                           INT DEFAULT 0 COMMENT '免手续费次数',
    `withdraw_single_max_limit`                DECIMAL(24, 6) COMMENT '提现单笔最大限制 空或者0不限制',
    `day_withdraw_total_max_limit`             DECIMAL(24, 6) COMMENT '每日提现汇总最大限制 空或者0不限制',
    `withdraw_total_limit_eum`                 INT COMMENT '提现累计限制类型',
    `withdraw_total_limit_num`                 VARCHAR(255) COMMENT '提现累计限制数额 空或者0不限制',
    `platform_id`                              BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`                              BIGINT COMMENT '更新时间',
    `update_by`                                VARCHAR(32) COMMENT '更新人',
    `create_time`                              BIGINT NOT NULL COMMENT '创建时间',
    `create_by`                                VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户等级信息';


CREATE INDEX ulevel_p_idx ON u_level (platform_id);
CREATE INDEX ulevel_pc_idx ON u_level (platform_id, create_time);
CREATE INDEX ulevel_pu_idx ON u_level (platform_id, update_time);
CREATE INDEX ulevel_plevel_idx ON u_level (platform_id, level_eum);

DROP TABLE IF EXISTS u_level_pick_up_money_record;
CREATE TABLE u_level_pick_up_money_record
(
    `id`                  BIGINT NOT NULL COMMENT '主键id',
    `gift_money_type_eum` INT    NOT NULL COMMENT '礼金类型',
    `level_eum`           INT    NOT NULL COMMENT '领取礼金时的等级',
    `gift_money`          DECIMAL(24, 6) COMMENT '领取礼金的金额',
    `gift_money_multiple` DECIMAL(24, 6) COMMENT '领取礼金金额的打码倍数',
    `user_id`             BIGINT NOT NULL COMMENT '用户id',
    `user_code`           BIGINT NOT NULL COMMENT '用户code',
    `username`            VARCHAR(32) COMMENT '用户名',
    `platform_id`         BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`         BIGINT COMMENT '更新时间',
    `update_by`           VARCHAR(32) COMMENT '更新人',
    `create_time`         BIGINT NOT NULL COMMENT '创建时间',
    `create_by`           VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户等级领钱记录';


CREATE INDEX ulpumr_p_idx ON u_level_pick_up_money_record (platform_id);
CREATE INDEX ulpumr_pc_idx ON u_level_pick_up_money_record (platform_id, create_time);
CREATE INDEX ulpumr_puc_idx ON u_level_pick_up_money_record (user_code, platform_id);
CREATE INDEX ulpumr_puigc_idx ON u_level_pick_up_money_record (user_id, gift_money_type_eum, create_time, platform_id);

DROP TABLE IF EXISTS u_level_up_down_grade_rule_change_record;
CREATE TABLE u_level_up_down_grade_rule_change_record
(
    `id`                        BIGINT NOT NULL COMMENT '主键id',
    `before_can_use_max_level`  INT COMMENT '改变前能使用的最大等级下标',
    `now_can_use_max_level`     INT COMMENT '改变后能使用的最大等级下标',
    `before_up_rule_type_eum`   INT COMMENT '改变前升级规则类型',
    `now_up_rule_type_eum`      INT COMMENT '改变后升级规则类型',
    `before_down_rule_type_eum` INT COMMENT '改变前降级规则类型',
    `now_down_rule_type_eum`    INT COMMENT '改变后降级规则类型',
    `before_open_down`          TINYINT(1) COMMENT '改变前是否开启降级',
    `now_open_down`             TINYINT(1) COMMENT '改变后是否开启降级',
    `platform_id`               BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`               BIGINT COMMENT '更新时间',
    `update_by`                 VARCHAR(32) COMMENT '更新人',
    `create_time`               BIGINT NOT NULL COMMENT '创建时间',
    `create_by`                 VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户等级升级降级规则改变记录';


CREATE INDEX uludgrcr_p_idx ON u_level_up_down_grade_rule_change_record (platform_id);
CREATE INDEX uludgrcr_pc_idx ON u_level_up_down_grade_rule_change_record (platform_id, create_time);

DROP TABLE IF EXISTS u_level_up_down_grade_rule_global_control;
CREATE TABLE u_level_up_down_grade_rule_global_control
(
    `id`                 BIGINT NOT NULL COMMENT '主键id',
    `can_use_max_level`  INT COMMENT '能使用的最大等级下标-比如初始的有20等级-但是租户只要10级-这里设置10即可',
    `up_rule_type_eum`   INT    NOT NULL COMMENT '升级规则类型',
    `down_rule_type_eum` INT    NOT NULL COMMENT '降级规则类型',
    `open_down`          TINYINT(1) NOT NULL COMMENT '是否开启降级',
    `platform_id`        BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`        BIGINT COMMENT '更新时间',
    `update_by`          VARCHAR(32) COMMENT '更新人',
    `create_time`        BIGINT NOT NULL COMMENT '创建时间',
    `create_by`          VARCHAR(32) COMMENT '创建人',
    PRIMARY KEY (id)
) COMMENT = '用户等级升级降级规则全局控制';


CREATE INDEX u_ludgrgc_p_idx ON u_level_up_down_grade_rule_global_control (platform_id);
CREATE INDEX u_ludgrgc_pc_idx ON u_level_up_down_grade_rule_global_control (platform_id, update_time);

DROP TABLE IF EXISTS u_login_control;
CREATE TABLE u_login_control
(
    `id`                                                   BIGINT NOT NULL COMMENT '主键',
    `platform_id`                                          BIGINT NOT NULL COMMENT '平台id',
    `create_by`                                            VARCHAR(32) COMMENT '创建人',
    `create_time`                                          BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                                            VARCHAR(32) COMMENT '更新人',
    `update_time`                                          BIGINT COMMENT '更新时间',
    `enabled_ip_limit_login`                               TINYINT(1) NOT NULL COMMENT '是否开启ip限制登录-默认开启',
    `ip_limit_login_count`                                 INT COMMENT 'ip限制登录账号个数-默认1-前提开启ip限制登录',
    `disabled_login_phone_prefix`                          VARCHAR(255) COMMENT '禁止登录的手机号前缀集合',
    `enabled_commonly_use_device_sms_captcha_verify_login` TINYINT(1) COMMENT '不是常用ip并且不是常用设备登录是否验证绑定手机号验证码-前提打开短信验证码-用户绑定过手机号-默认开启',
    `background_address`                                   VARCHAR(255) COMMENT '背景地址 可以是图片 可以是视频',
    `terminal_type_eum`                                    INT    NOT NULL COMMENT '控制的终端',
    `system_type_eum`                                      INT    NOT NULL COMMENT '控制的系统',
    PRIMARY KEY (id)
) COMMENT = '用户登录控制';


CREATE INDEX ulogin_ctrl_p_idx ON u_login_control (platform_id);
CREATE INDEX ulogin_ctrl_pc_idx ON u_login_control (platform_id, create_time);
CREATE INDEX ulogin_ctrl_pu_idx ON u_login_control (platform_id, update_time);
CREATE INDEX ulogin_ctrl_pst_idx ON u_login_control (platform_id, terminal_type_eum, system_type_eum);

DROP TABLE IF EXISTS u_login_log;
CREATE TABLE u_login_log
(
    `id`                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`             BIGINT NOT NULL COMMENT '平台id',
    `create_by`               VARCHAR(32) COMMENT '创建人',
    `update_by`               VARCHAR(32) COMMENT '更新人',
    `create_time`             BIGINT NOT NULL COMMENT '创建时间',
    `update_time`             BIGINT COMMENT '更新时间',
    `user_id`                 BIGINT COMMENT '用户id【账号不存在时为空】',
    `user_code`               BIGINT COMMENT '用户code【账号不存在时为空】',
    `username`                VARCHAR(32) COMMENT '用户名【账号不存在时为空】',
    `login_ip`                VARCHAR(32) COMMENT '登录ip',
    `login_time`              BIGINT COMMENT '登录时间',
    `login_device_id`         VARCHAR(255) COMMENT '登录的设备 app使用设备id 浏览器使用指纹串',
    `login_ua`                VARCHAR(255) COMMENT '登录的浏览器ua app没有',
    `login_source`            VARCHAR(255) COMMENT '登录来源 浏览器的获取登录站点的域名 app使用渠道码',
    `login_version`           VARCHAR(90) COMMENT '登录的客户端软件版本 app用包的版本号 浏览器用接口版本号',
    `login_os`                VARCHAR(90) COMMENT '登录的客户端系统',
    `login_os_version`        VARCHAR(90) COMMENT '登录的客户端系统版本',
    `login_apn`               VARCHAR(90) COMMENT '登录的客户端网络类型',
    `login_type_eum`          INT COMMENT '登录方式',
    `login_result_eum`        INT COMMENT '登录结果',
    `login_clcppz_eum`        INT COMMENT '登录的国家枚举',
    `login_equipmen_type_eum` INT COMMENT '登录的设备',
    `login_terminal_type_eum` INT COMMENT '登录的终端',
    `login_system_type_eum`   INT COMMENT '登录的系统',
    PRIMARY KEY (id)
) COMMENT = '用户登录日志';


CREATE INDEX ulogin_log_p_idx ON u_login_log (platform_id);
CREATE INDEX ulogin_log_pc_idx ON u_login_log (platform_id, create_time);

DROP TABLE IF EXISTS u_login_switch_control;
CREATE TABLE u_login_switch_control
(
    `id`                                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`                            BIGINT NOT NULL COMMENT '平台id',
    `create_by`                              VARCHAR(32) COMMENT '创建人',
    `create_time`                            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                              VARCHAR(32) COMMENT '更新人',
    `update_time`                            BIGINT COMMENT '更新时间',
    `enabled_equipment_login`                TINYINT(1) COMMENT '是否开启设备号登录-默认关闭',
    `enabled_username_login`                 TINYINT(1) COMMENT '是否开启用户登录-默认开启',
    `enabled_username_base_captcha_login`    TINYINT(1) COMMENT '是否开启用户名登录普通验证码-默认开启',
    `enabled_username_aj_captcha_login`      TINYINT(1) COMMENT '是否开启用户名登录aj验证码-默认关闭',
    `enabled_phone_login`                    TINYINT(1) COMMENT '是否开启手机登录-默认关闭',
    `enabled_phone_must_phone_captcha_login` TINYINT(1) COMMENT '是否开启手机登录必填手机号验证码-前提必须开启短信验证码-开启手机登录-默认开启',
    `enabled_phone_base_captcha_login`       TINYINT(1) COMMENT '是否开启手机登录普通验证码-默认关闭',
    `enabled_phone_aj_captcha_login`         TINYINT(1) COMMENT '是否开启手机登录aj验证码-默认关闭',
    `enabled_email_login`                    TINYINT(1) COMMENT '是否开启邮箱登录-默认关闭',
    `enabled_email_must_email_captcha_login` TINYINT(1) COMMENT '是否开启邮箱登录必填邮箱验证码-前提必须开启邮箱验证码-必须开启邮箱登录-默认开启',
    `enabled_email_base_captcha_login`       TINYINT(1) COMMENT '是否开启邮箱登录普通验证码-默认关闭',
    `enabled_email_aj_captcha_login`         TINYINT(1) COMMENT '是否开启邮箱登录aj验证码-默认关闭',
    `terminal_type_eum`                      INT    NOT NULL COMMENT '控制的终端',
    `system_type_eum`                        INT    NOT NULL COMMENT '控制的系统',
    `enabled_phone_retrieval_password`       TINYINT(1) COMMENT '是否开启手机号找回密码-默认开启',
    `enabled_email_retrieval_password`       TINYINT(1) COMMENT '是否开启邮箱找回密码-默认开启',
    PRIMARY KEY (id)
) COMMENT = '用户登录开关控制';


CREATE INDEX ulwc_p_idx ON u_login_switch_control (platform_id);
CREATE INDEX ulwc_pc_idx ON u_login_switch_control (platform_id, create_time);
CREATE INDEX ulwc_pu_idx ON u_login_switch_control (platform_id, update_time);
CREATE INDEX ulwc_pst_idx ON u_login_switch_control (platform_id, terminal_type_eum, system_type_eum);

DROP TABLE IF EXISTS u_reg_control;
CREATE TABLE u_reg_control
(
    `id`                        BIGINT NOT NULL COMMENT '主键',
    `platform_id`               BIGINT NOT NULL COMMENT '平台id',
    `create_by`                 VARCHAR(32) COMMENT '创建人',
    `create_time`               BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                 VARCHAR(32) COMMENT '更新人',
    `update_time`               BIGINT COMMENT '更新时间',
    `enabled_ip_limit_reg`      TINYINT(1) NOT NULL COMMENT '是否开启ip限制注册-默认开启',
    `ip_limit_reg_count`        INT COMMENT 'ip限制注册账号个数-默认1-前提开启ip限制注册',
    `enabled_device_limit_reg`  TINYINT(1) COMMENT '是否开启设备号限制注册-默认开启',
    `device_limit_reg_count`    INT COMMENT '设备号限制注册账号个数-默认1-前提开启设备号限制注册',
    `disabled_reg_phone_prefix` VARCHAR(255) COMMENT '禁止注册的手机号前缀集合',
    `init_user_code_reg`        INT COMMENT '台子注册用户的初始化用户code-先判断缓存是否存在-在判断数据库最大用户code-否则使用设置的用户code',
    `background_address`        VARCHAR(255) COMMENT '背景地址 可以是图片 可以是视频',
    `privacy_agreement`         TEXT COMMENT '隐私协议',
    `terminal_type_eum`         INT    NOT NULL COMMENT '控制的终端',
    `system_type_eum`           INT    NOT NULL COMMENT '控制的系统',
    PRIMARY KEY (id)
) COMMENT = '用户注册控制';


CREATE INDEX ureg_ctrl_p_idx ON u_reg_control (platform_id);
CREATE INDEX ureg_ctrl_pc_idx ON u_reg_control (platform_id, create_time);
CREATE INDEX ureg_ctrl_pu_idx ON u_reg_control (platform_id, update_time);
CREATE INDEX ureg_ctrl_pts_idx ON u_reg_control (platform_id, terminal_type_eum, system_type_eum);

DROP TABLE IF EXISTS u_reg_log;
CREATE TABLE u_reg_log
(
    `id`                    BIGINT NOT NULL COMMENT '主键',
    `platform_id`           BIGINT NOT NULL COMMENT '平台id',
    `create_by`             VARCHAR(32) COMMENT '创建人',
    `create_time`           BIGINT NOT NULL COMMENT '创建时间',
    `update_by`             VARCHAR(32) COMMENT '更新人',
    `update_time`           BIGINT COMMENT '更新时间',
    `user_id`               BIGINT NOT NULL COMMENT '用户id',
    `user_code`             BIGINT NOT NULL COMMENT '用户code',
    `username`              VARCHAR(32) COMMENT '用户名',
    `reg_type_eum`          INT COMMENT '注册的方式',
    `reg_clcppz_eum`        INT COMMENT '注册的国家枚举',
    `reg_currency_eum`      INT COMMENT '注册的货币 可以为空 为空使用注册国家的默认货币',
    `reg_source`            VARCHAR(90) COMMENT '注册来源 浏览器的获取注册站点的域名 app获取落地页域名',
    `reg_channel_code`      VARCHAR(255) COMMENT '注册渠道邀请码 邀请用户的用户id生成的渠道码 可以反转回上级用户id',
    `reg_device_id`         VARCHAR(255) COMMENT '注册的设备 app使用设备id 浏览器使用指纹串',
    `reg_ua`                TEXT COMMENT '注册的浏览器ua 浏览器有 app为null',
    `reg_version`           VARCHAR(90) COMMENT '注册的客户端软件版本 app用包的版本号 浏览器用接口版本号',
    `reg_os`                VARCHAR(90) COMMENT '注册的客户端系统',
    `reg_os_version`        VARCHAR(90) COMMENT '注册的客户端系统',
    `reg_apn`               VARCHAR(90) COMMENT '注册的客户端网络类型',
    `reg_equipmen_type_eum` INT COMMENT '注册的设备',
    `reg_terminal_type_eum` INT COMMENT '注册的终端',
    `reg_system_type_eum`   INT COMMENT '注册的来源系统',
    `separate_package_id`   VARCHAR(90) COMMENT '源包标识',
    `reg_ip`                VARCHAR(32) COMMENT '注册的ip',
    PRIMARY KEY (id)
) COMMENT = '用户注册日志信息';


CREATE INDEX ureg_log_p_idx ON u_reg_log (platform_id);
CREATE INDEX ureg_log_pc_idx ON u_reg_log (platform_id, create_time);
CREATE INDEX ureg_log_pu_idx ON u_reg_log (platform_id, user_code);

DROP TABLE IF EXISTS u_reg_switch_control;
CREATE TABLE u_reg_switch_control
(
    `id`                                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`                             BIGINT NOT NULL COMMENT '平台id',
    `create_by`                               VARCHAR(32) COMMENT '创建人',
    `create_time`                             BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                               VARCHAR(32) COMMENT '更新人',
    `update_time`                             BIGINT COMMENT '更新时间',
    `enabled_equipment_reg`                   TINYINT(1) COMMENT '是否开启设备号注册-默认关闭',
    `enabled_username_reg`                    TINYINT(1) COMMENT '是否开启用户注册-默认开启',
    `enabled_username_base_captcha_reg`       TINYINT(1) COMMENT '是否开启用户名注册普通验证码-默认开启',
    `enabled_username_aj_captcha_reg`         TINYINT(1) COMMENT '是否开启用户名注册aj验证码-默认关闭',
    `enabled_username_must_phone_reg`         TINYINT(1) COMMENT '是否开启用户名注册必填手机号-默认关闭',
    `enabled_username_must_phone_captcha_reg` TINYINT(1) COMMENT '是否开启用户名注册必填手机号验证码-前提必须开启短信验证码-必须开启必填手机号-默认关闭',
    `enabled_username_must_email_reg`         TINYINT(1) COMMENT '是否开启用户名注册必填邮箱-默认关闭',
    `enabled_username_must_email_captcha_reg` TINYINT(1) COMMENT '是否开启用户名注册必填邮箱验证码-前提必须开启邮箱验证码-必须开启必填邮箱-默认关闭',
    `enabled_phone_reg`                       TINYINT(1) COMMENT '是否开启手机注册-默认关闭',
    `enabled_phone_must_phone_captcha_reg`    TINYINT(1) COMMENT '是否开启手机注册必填手机号验证码-前提必须开启短信验证码-开启手机注册-默认开启',
    `enabled_phone_base_captcha_reg`          TINYINT(1) COMMENT '是否开启手机注册普通验证码-默认关闭',
    `enabled_phone_aj_captcha_reg`            TINYINT(1) COMMENT '是否开启手机注册aj验证码-默认关闭',
    `enabled_phone_must_username_reg`         TINYINT(1) COMMENT '是否开启手机注册必填用户名-默认关闭-开启则用户名使用输入的-关闭用户名为手机号',
    `enabled_phone_must_email_reg`            TINYINT(1) COMMENT '是否开启手机注册必填邮箱-默认关闭-开启则用户名使用输入的邮箱-关闭用户名为手机号',
    `enabled_phone_must_email_captcha_reg`    TINYINT(1) COMMENT '是否开启手机注册必填邮箱验证码-前提必须开启邮箱验证码-必须开启必填邮箱-默认关闭',
    `enabled_email_reg`                       TINYINT(1) COMMENT '是否开启邮箱注册-默认关闭',
    `enabled_email_must_email_captcha_reg`    TINYINT(1) COMMENT '是否开启邮箱注册必填邮箱验证码-前提必须开启邮箱验证码-必须开启邮箱注册-默认开启',
    `enabled_email_base_captcha_reg`          TINYINT(1) COMMENT '是否开启邮箱注册普通验证码-默认关闭',
    `enabled_email_aj_captcha_reg`            TINYINT(1) COMMENT '是否开启邮箱注册aj验证码-默认关闭',
    `enabled_email_must_username_reg`         TINYINT(1) COMMENT '是否开启邮箱注册必填用户名-默认关闭-开启则用户名使用输入的-关闭用户名为邮箱号',
    `enabled_email_must_phone_reg`            TINYINT(1) COMMENT '是否开启邮箱注册必填手机号-默认关闭-开启则用户名使用输入的手机号-关闭用户名为邮箱',
    `enabled_email_must_phone_captcha_reg`    TINYINT(1) COMMENT '是否开启邮箱注册必填手机号验证码-前提必须开启短信验证码-开启必填手机号-默认关闭',
    `enabled_must_invitation_code_reg`        TINYINT(1) COMMENT '是否开启注册必填邀请码-默认关闭',
    `enabled_must_security_passwd_reg`        TINYINT(1) COMMENT '是否开启注册必填安全密码-默认关闭',
    `enabled_must_real_name_reg`              TINYINT(1) COMMENT '是否开启注册必填真实姓名-默认关闭',
    `enabled_must_id_card_reg`                TINYINT(1) COMMENT '是否开启注册必填身份证id卡-默认关闭',
    `terminal_type_eum`                       INT    NOT NULL COMMENT '控制的终端',
    `system_type_eum`                         INT    NOT NULL COMMENT '控制的系统',
    PRIMARY KEY (id)
) COMMENT = '用户注册开关控制';


CREATE INDEX ursc_p_idx ON u_reg_switch_control (platform_id);
CREATE INDEX ursc_pc_idx ON u_reg_switch_control (platform_id, create_time);
CREATE INDEX ursc_pu_idx ON u_reg_switch_control (platform_id, update_time);
CREATE INDEX ursc_pst_idx ON u_reg_switch_control (platform_id, terminal_type_eum, system_type_eum);

DROP TABLE IF EXISTS r_platform_end_day_summary;
CREATE TABLE r_platform_end_day_summary
(
    `id`                             BIGINT NOT NULL COMMENT '主键',
    `platform_id`                    BIGINT NOT NULL COMMENT '平台id',
    `create_by`                      VARCHAR(32) COMMENT '创建人',
    `create_time`                    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                      VARCHAR(32) COMMENT '更新人',
    `update_time`                    BIGINT COMMENT '更新时间',
    `statistical_time`               VARCHAR(90) COMMENT '统计时间-年月日',
    `new_user_count`                 BIGINT COMMENT '新增会员数',
    `new_recharge_user_count`        BIGINT COMMENT '新增会员存款人数',
    `new_recharge_user_amount`       DECIMAL(24, 6) COMMENT '新增会员存款金额',
    `wash_count`                     BIGINT COMMENT '洗码次数',
    `wash_user_count`                BIGINT COMMENT '洗码人数',
    `wash_amount`                    DECIMAL(24, 6) COMMENT '洗码金额',
    `bet_count`                      BIGINT COMMENT '总投注次数',
    `bet_user_count`                 BIGINT COMMENT '总投注人数',
    `bet_amount`                     DECIMAL(24, 6) COMMENT '总投注金额',
    `payout_count`                   BIGINT COMMENT '总派彩次数',
    `payout_user_count`              BIGINT COMMENT '总派彩人数',
    `payout_amount`                  DECIMAL(24, 6) COMMENT '总派彩金额',
    `profit_amount`                  DECIMAL(24, 6) COMMENT '总输赢金额',
    `recharge_count`                 BIGINT COMMENT '总存款次数',
    `recharge_user_count`            BIGINT COMMENT '总存款人数',
    `recharge_amount`                DECIMAL(24, 6) COMMENT '总存款金额',
    `withdraw_count`                 BIGINT COMMENT '总取款次数',
    `withdraw_user_count`            BIGINT COMMENT '总取款人数',
    `withdraw_amount`                DECIMAL(24, 6) COMMENT '总取款金额',
    `fee_count`                      BIGINT COMMENT '平台手续费次数',
    `fee_user_count`                 BIGINT COMMENT '平台手续费人数',
    `fee_amount`                     DECIMAL(24, 6) COMMENT '平台手续费金额',
    `discount_count`                 BIGINT COMMENT '总优惠次数',
    `discount_user_count`            BIGINT COMMENT '总优惠人数',
    `discount_amount`                DECIMAL(24, 6) COMMENT '总优惠金额',
    `operation_up_count`             BIGINT COMMENT '人工上分次数',
    `operation_up_user_count`        BIGINT COMMENT '人工上分人数',
    `operation_up_user_amount`       DECIMAL(24, 6) COMMENT '人工上分金额',
    `operation_down_count`           BIGINT COMMENT '人工下分次数',
    `operation_down_user_count`      BIGINT COMMENT '人工下分人数',
    `operation_down_amount`          DECIMAL(24, 6) COMMENT '人工下分金额',
    `first_recharge_user_count`      BIGINT COMMENT '首存人数',
    `first_recharge_amount`          DECIMAL(24, 6) COMMENT '首存金额',
    `first_recharge_discount_amount` DECIMAL(24, 6) COMMENT '首存优惠',
    `commission_count`               BIGINT COMMENT '佣金转出次数',
    `commission_user_count`          BIGINT COMMENT '佣金转出人数',
    `commission_amount`              DECIMAL(24, 6) COMMENT '佣金转出金额',
    `total_balance_amount`           DECIMAL(24, 6) COMMENT '总余额金额',
    `tester_data`                    TEXT COMMENT '测试账号数据',
    PRIMARY KEY (id)
) COMMENT = '平台日结汇总';


CREATE INDEX pfbrped_pc_idx ON r_platform_end_day_summary (platform_id, statistical_time);

DROP TABLE IF EXISTS r_platform_end_day_summary_control;
CREATE TABLE r_platform_end_day_summary_control
(
    `id`                           BIGINT      NOT NULL COMMENT '主键',
    `platform_id`                  BIGINT      NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    `end_day_start_calculate_time` VARCHAR(90) NOT NULL COMMENT '流水每日开始计算时间 时:分:秒',
    PRIMARY KEY (id)
) COMMENT = '平台日结汇总控制';


CREATE INDEX pfbredsc_pd_idx ON r_platform_end_day_summary_control (platform_id);

DROP TABLE IF EXISTS u_data_total;
CREATE TABLE u_data_total
(
    `id`                           BIGINT NOT NULL COMMENT '主键',
    `total_bet_amount`             DECIMAL(24, 6) COMMENT '累计打码',
    `now_bet_amount`               DECIMAL(24, 6) COMMENT '当前打码',
    `total_profit_amount`          DECIMAL(24, 6) COMMENT '累计输赢',
    `now_profit_amount`            DECIMAL(24, 6) COMMENT '当前输赢',
    `total_recharge_amount`        DECIMAL(24, 6) COMMENT '累计充值',
    `now_recharge_amount`          DECIMAL(24, 6) COMMENT '当前充值',
    `total_withdraw_amount`        DECIMAL(24, 6) COMMENT '累计提现',
    `now_withdraw_amount`          DECIMAL(24, 6) COMMENT '当前提现',
    `balance`                      DECIMAL(24, 6) COMMENT '账本余额',
    `recharge_count`               BIGINT COMMENT '充值次数',
    `today_commission`             DECIMAL(24, 6) COMMENT '今日预估佣金',
    `parent_id`                    BIGINT COMMENT '上级编码',
    `phone`                        VARCHAR(32) COMMENT '手机号码',
    `reg_source`                   VARCHAR(90) COMMENT '注册来源',
    `reg_time`                     BIGINT COMMENT '注册时间',
    `payout_amount`                DECIMAL(24, 6) COMMENT '派彩金额',
    `bet_count`                    BIGINT COMMENT '打码次数',
    `withdraw_count`               BIGINT COMMENT '提现次数',
    `recharge_withdraw_difference` DECIMAL(24, 6) COMMENT '充值提现差',
    `user_id`                      BIGINT NOT NULL COMMENT '用户ID',
    `user_code`                    BIGINT NOT NULL COMMENT '用户CODE',
    `username`                     VARCHAR(32) COMMENT '用户名',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '个人数据汇总表';


CREATE INDEX b_p_idx ON u_data_total (platform_id);

DROP TABLE IF EXISTS u_operate_record;
CREATE TABLE u_operate_record
(
    `id`                BIGINT NOT NULL COMMENT '主键',
    `user_operate_type` INT COMMENT '操作类型',
    `remark`            VARCHAR(255) COMMENT '操作备注',
    `user_id`           BIGINT COMMENT '用户id',
    `user_code`         BIGINT COMMENT '用户code',
    `username`          VARCHAR(32) COMMENT '用户名',
    `platform_id`       BIGINT NOT NULL COMMENT '平台id',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '会员管理操作记录';


CREATE INDEX u_op_uni_idx ON u_operate_record (user_operate_type, create_time, platform_id);
CREATE INDEX u_p_index ON u_operate_record (platform_id);

DROP TABLE IF EXISTS u_open_user;
CREATE TABLE u_open_user
(
    `id`          BIGINT      NOT NULL COMMENT '主键',
    `platform_id` BIGINT      NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `source`      VARCHAR(90) NOT NULL COMMENT '登录的第三方来源;可以用uuid + source唯一确定一个用户',
    `uuid`        VARCHAR(90) COMMENT '用户第三方系统的唯一id;用户第三方系统的唯一id。在调用方集成该组件时，可以用uuid + source唯一确定一个用户',
    `user_id`     BIGINT      NOT NULL COMMENT '用户ID',
    `username`    VARCHAR(32) COMMENT '用户名',
    `user_code`   BIGINT COMMENT '用户编码',
    PRIMARY KEY (id)
) COMMENT = '用户三方登录关联表';


CREATE INDEX uou_pid_idx ON u_open_user (platform_id);
CREATE INDEX uou_psu_idx ON u_open_user (platform_id, source, uuid);
CREATE INDEX uou_pc_idx ON u_open_user (platform_id, create_time);
CREATE INDEX uou_pu_idx ON u_open_user (platform_id, update_time);

DROP TABLE IF EXISTS u_open_user_auth_log;
CREATE TABLE u_open_user_auth_log
(
    `id`            BIGINT NOT NULL COMMENT '主键',
    `platform_id`   BIGINT NOT NULL COMMENT '平台id',
    `create_by`     VARCHAR(32) COMMENT '创建人',
    `create_time`   BIGINT NOT NULL COMMENT '创建时间',
    `update_by`     VARCHAR(32) COMMENT '更新人',
    `update_time`   BIGINT COMMENT '更新时间',
    `source`        VARCHAR(90) COMMENT '登录三方来源',
    `auth_response` TEXT COMMENT '授权返回信息',
    `user_id`       BIGINT NOT NULL COMMENT '用户ID',
    `username`      VARCHAR(32) COMMENT '用户名',
    `user_code`     BIGINT COMMENT '用户编码',
    PRIMARY KEY (id)
) COMMENT = '用户三方授权回调日志';


CREATE INDEX uoual_pid_idx ON u_open_user_auth_log (platform_id);
CREATE INDEX uoual_pc_idx ON u_open_user_auth_log (platform_id, create_time);
CREATE INDEX uoual_puid_idx ON u_open_user_auth_log (platform_id, user_id);

DROP TABLE IF EXISTS u_data_end_day_summary;
CREATE TABLE u_data_end_day_summary
(
    `id`                                  BIGINT NOT NULL COMMENT '主键',
    `platform_id`                         BIGINT NOT NULL COMMENT '平台id',
    `create_by`                           VARCHAR(32) COMMENT '创建人',
    `create_time`                         BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                           VARCHAR(32) COMMENT '更新人',
    `update_time`                         BIGINT COMMENT '更新时间',
    `operate_date`                        VARCHAR(90) COMMENT '操作日期-年月日',
    `user_id`                             BIGINT NOT NULL COMMENT '用户ID',
    `user_code`                           BIGINT COMMENT '用户code',
    `username`                            VARCHAR(32) COMMENT '用户名称',
    `parent_id`                           BIGINT COMMENT '上级编号',
    `parent_ids`                          LONGTEXT COMMENT '代理链路',
    `reg_terminal_type_eum`               INT COMMENT '注册终端',
    `is_tester`                           TINYINT(1) COMMENT '是否测试账号',
    `balance`                             DECIMAL(24, 6) COMMENT '账户总额',
    `total_recharge`                      DECIMAL(24, 6) COMMENT '累计充值',
    `today_recharge`                      DECIMAL(24, 6) COMMENT '今日充值',
    `total_recharge_count`                BIGINT COMMENT '累计充值次数',
    `today_recharge_count`                BIGINT COMMENT '今日充值次数',
    `total_withdraw`                      DECIMAL(24, 6) COMMENT '累计提现',
    `today_withdraw`                      DECIMAL(24, 6) COMMENT '今日提现',
    `total_withdraw_count`                BIGINT COMMENT '累计提现次数',
    `today_withdraw_count`                BIGINT COMMENT '今日提现次数',
    `total_bet`                           DECIMAL(24, 6) COMMENT '累计投注',
    `today_bet`                           DECIMAL(24, 6) COMMENT '今日投注',
    `total_bet_count`                     BIGINT COMMENT '累计投注次数',
    `today_bet_count`                     BIGINT COMMENT '今日投注次数',
    `total_profit`                        DECIMAL(24, 6) COMMENT '累计输赢',
    `today_profit`                        DECIMAL(24, 6) COMMENT '今日输赢',
    `total_payout`                        DECIMAL(24, 6) COMMENT '累计派彩',
    `today_payout`                        DECIMAL(24, 6) COMMENT '今日派彩',
    `total_payout_count`                  BIGINT COMMENT '累计派彩次数',
    `today_payout_count`                  BIGINT COMMENT '今日派彩次数',
    `total_commission`                    DECIMAL(24, 6) COMMENT '累计佣金',
    `today_commission`                    DECIMAL(24, 6) COMMENT '今日佣金',
    `total_roll_out`                      DECIMAL(24, 6) COMMENT '累计佣金转出金额',
    `today_roll_out`                      DECIMAL(24, 6) COMMENT '今日佣金转出金额',
    `total_roll_out_count`                BIGINT COMMENT '累计佣金转出次数',
    `today_roll_out_count`                BIGINT COMMENT '今日佣金转出次数',
    `total_wash`                          DECIMAL(24, 6) COMMENT '累计洗码总额',
    `today_wash`                          DECIMAL(24, 6) COMMENT '今日洗码总额',
    `total_wash_count`                    BIGINT COMMENT '累计洗码次数',
    `today_wash_count`                    BIGINT COMMENT '今日洗码次数',
    `total_task_profit`                   DECIMAL(24, 6) COMMENT '累计任务派彩总额',
    `today_task_profit`                   DECIMAL(24, 6) COMMENT '今日任务派彩总额',
    `total_task_profit_count`             BIGINT COMMENT '累计任务派彩次数',
    `today_task_profit_count`             BIGINT COMMENT '今日任务派彩次数',
    `team_member_count`                   BIGINT COMMENT '团队人数',
    `direct_member_count`                 BIGINT COMMENT '团队直属人数',
    `other_member_count`                  BIGINT COMMENT '团队非直属人数',
    `team_newly_count`                    BIGINT COMMENT '团队新增',
    `direct_newly_count`                  BIGINT COMMENT '直属新增',
    `other_newly_count`                   BIGINT COMMENT '非直属新增',
    `direct_reg_count`                    BIGINT COMMENT '直属新注册会员数',
    `other_reg_count`                     BIGINT COMMENT '非直属新注册会员数',
    `team_bet`                            DECIMAL(24, 6) COMMENT '团队投注量（不含自身）',
    `direct_bet`                          DECIMAL(24, 6) COMMENT '直属投注量',
    `other_bet`                           DECIMAL(24, 6) COMMENT '非直属投注量',
    `team_bet_count`                      BIGINT COMMENT '团队投注总次数（不含自身）',
    `direct_bet_count`                    BIGINT COMMENT '直属投注总次数',
    `other_bet_count`                     BIGINT COMMENT '非直属投注总次数',
    `team_bet_member_count`               BIGINT COMMENT '团队投注总人数（不含自身）',
    `direct_bet_member_count`             BIGINT COMMENT '直属投注人数',
    `other_bet_member_count`              BIGINT COMMENT '非直属投注人数',
    `team_profit`                         DECIMAL(24, 6) COMMENT '团队总输赢（不含自身）',
    `direct_profit`                       DECIMAL(24, 6) COMMENT '直属总输赢',
    `other_profit`                        DECIMAL(24, 6) COMMENT '非直属总输赢',
    `team_payout`                         DECIMAL(24, 6) COMMENT '团队总派彩（不含自身）',
    `direct_payout`                       DECIMAL(24, 6) COMMENT '直属总派彩',
    `other_payout`                        DECIMAL(24, 6) COMMENT '非直属总派彩',
    `team_payout_count`                   BIGINT COMMENT '团队派彩总次数（不含自身）',
    `direct_payout_count`                 BIGINT COMMENT '直属派彩总次数',
    `other_payout_count`                  BIGINT COMMENT '非直属派彩总次数',
    `team_payout_member_count`            BIGINT COMMENT '团队派彩总人数（不含自身）',
    `direct_payout_member_count`          BIGINT COMMENT '直属派彩总人数',
    `other_payout_member_count`           BIGINT COMMENT '非直属派彩总人数',
    `team_roll_out`                       DECIMAL(24, 6) COMMENT '团队佣金转出金额（不含自身）',
    `direct_roll_out`                     DECIMAL(24, 6) COMMENT '直属佣金转出金额',
    `other_roll_out`                      DECIMAL(24, 6) COMMENT '非直属佣金转出金额',
    `team_roll_out_count`                 BIGINT COMMENT '团队佣金转出次数（不含自身）',
    `direct_roll_out_count`               BIGINT COMMENT '直属佣金转出次数',
    `other_roll_out_count`                BIGINT COMMENT '非直属佣金转出次数',
    `team_roll_out_member_count`          BIGINT COMMENT '团队佣金转出人数（不含自身）',
    `direct_roll_out_member_count`        BIGINT COMMENT '直属佣金转出人数',
    `other_roll_out_member_count`         BIGINT COMMENT '非直属佣金转出人数',
    `team_recharge`                       DECIMAL(24, 6) COMMENT '团队充值总额（不含自身）',
    `direct_recharge`                     DECIMAL(24, 6) COMMENT '直属充值总额',
    `other_recharge`                      DECIMAL(24, 6) COMMENT '非直属充值总额',
    `team_recharge_count`                 BIGINT COMMENT '团队充值总次数（不含自身）',
    `direct_recharge_count`               BIGINT COMMENT '直属充值总次数',
    `other_charge_count`                  BIGINT COMMENT '非直属充值总次数',
    `team_recharge_member_count`          BIGINT COMMENT '团队充值总人数（不含自身）',
    `direct_recharge_member_count`        BIGINT COMMENT '直属充值总人数',
    `other_recharge_member_count`         BIGINT COMMENT '非直属充值总人数',
    `team_reg_recharge`                   DECIMAL(24, 6) COMMENT '团队新注册会员充值总额（不含自身）',
    `team_reg_recharge_count`             BIGINT COMMENT '团队新注册会员充值总次数（不含自身）',
    `team_reg_recharger_count`            BIGINT COMMENT '团队新注册会员充值总人数（不含自身）',
    `team_reg_first_recharge`             DECIMAL(24, 6) COMMENT '团队新注册会员首充总额（不含自身）',
    `team_reg_first_recharger_count`      BIGINT COMMENT '团队新注册会员首充总人数（不含自身）',
    `team_reg_second_recharge`            DECIMAL(24, 6) COMMENT '团队新注册会员二充总额（不含自身）',
    `team_reg_second_recharger_count`     BIGINT COMMENT '团队新注册会员二充总人数（不含自身）',
    `team_not_reg_recharge`               DECIMAL(24, 6) COMMENT '团队非新注册会员充值总额（不含自身）',
    `team_not_reg_recharge_count`         BIGINT COMMENT '团队非新注册会员充值总次数（不含自身）',
    `team_not_reg_recharger_count`        BIGINT COMMENT '团队非新注册会员充值总人数（不含自身）',
    `team_not_reg_first_recharge`         DECIMAL(24, 6) COMMENT '团队非新注册会员首充总额（不含自身）',
    `team_not_reg_first_recharger_count`  BIGINT COMMENT '团队非新注册会员首充总人数（不含自身）',
    `team_not_reg_second_recharge`        DECIMAL(24, 6) COMMENT '团队非新注册会员二充总额（不含自身）',
    `team_not_reg_second_recharger_count` BIGINT COMMENT '团队非新注册会员二充总人数（不含自身）',
    `team_discount`                       DECIMAL(24, 6) COMMENT '团队优惠总额（不含自身）',
    `team_discount_count`                 BIGINT COMMENT '团队优惠总人数（不含自身）',
    `team_first_discount`                 DECIMAL(24, 6) COMMENT '团队首充优惠总额（不含自身）',
    `team_first_discount_count`           BIGINT COMMENT '团队首充优惠总人数（不含自身）',
    `team_second_discount`                DECIMAL(24, 6) COMMENT '团队二充优惠总额（不含自身）',
    `team_second_discount_count`          BIGINT COMMENT '团队二充优惠总人数（不含自身）',
    `team_withdraw`                       DECIMAL(24, 6) COMMENT '团队提现总额（不含自身）',
    `direct_withdraw`                     DECIMAL(24, 6) COMMENT '直属提现总额',
    `other_withdraw`                      DECIMAL(24, 6) COMMENT '非直属提现总额',
    `team_withdraw_count`                 BIGINT COMMENT '团队提现总次数（不含自身）',
    `direct_withdraw_count`               BIGINT COMMENT '直属提现总次数',
    `other_withdraw_count`                BIGINT COMMENT '非直属提现总次数',
    `team_withdraw_member_count`          BIGINT COMMENT '团队提现总人数（不含自身）',
    `direct_withdraw_member_count`        BIGINT COMMENT '直属提现总人数',
    `other_withdraw_member_count`         BIGINT COMMENT '非直属提现总人数',
    `team_wash`                           DECIMAL(24, 6) COMMENT '团队洗码量（不含自身）',
    `direct_wash`                         DECIMAL(24, 6) COMMENT '直属洗码量',
    `other_wash`                          DECIMAL(24, 6) COMMENT '非直属洗码量',
    `team_wash_count`                     BIGINT COMMENT '团队洗码总次数（不含自身）',
    `direct_wash_count`                   BIGINT COMMENT '直属洗码总次数',
    `other_wash_count`                    BIGINT COMMENT '非直属洗码总次数',
    `team_wash_member_count`              BIGINT COMMENT '团队洗码总人数（不含自身）',
    `direct_wash_member_count`            BIGINT COMMENT '直属洗码总人数',
    `other_wash_member_count`             BIGINT COMMENT '非直属洗码总人数',
    `team_task`                           DECIMAL(24, 6) COMMENT '团队任务派彩总额（不含自身）',
    `direct_task`                         DECIMAL(24, 6) COMMENT '直属任务派彩总额',
    `other_task`                          DECIMAL(24, 6) COMMENT '非直属任务派彩总额',
    `team_task_count`                     BIGINT COMMENT '团队任务派彩总次数（不含自身）',
    `direct_task_count`                   BIGINT COMMENT '直属任务派彩总次数',
    `other_task_count`                    BIGINT COMMENT '非直属任务派彩总次数',
    `team_task_member_count`              BIGINT COMMENT '团队任务派彩总人数（不含自身）',
    `direct_task_member_count`            BIGINT COMMENT '直属任务派彩总人数',
    `other_task_member_count`             BIGINT COMMENT '非直属任务派彩总人数',
    PRIMARY KEY (id)
) COMMENT = '用户数据日结表';


CREATE INDEX udeds_p_u_idx ON u_data_end_day_summary (platform_id, user_id);
CREATE INDEX udeds_p_o_idx ON u_data_end_day_summary (platform_id, operate_date);
CREATE INDEX udeds_p_p_idx ON u_data_end_day_summary (platform_id, parent_id);
CREATE INDEX udeds_p_u_o_p_idx ON u_data_end_day_summary (platform_id, operate_date, user_id, parent_id);

DROP TABLE IF EXISTS u_data_end_day_summary_control;
CREATE TABLE u_data_end_day_summary_control
(
    `id`                           BIGINT      NOT NULL COMMENT '主键',
    `platform_id`                  BIGINT      NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    `end_day_start_calculate_time` VARCHAR(90) NOT NULL COMMENT '流水每日开始计算时间 时:分:秒',
    PRIMARY KEY (id)
) COMMENT = '用户数据日结总控制';


CREATE INDEX udedsc_p_idx ON u_data_end_day_summary_control (platform_id);

DROP TABLE IF EXISTS u_info_switch_control;
CREATE TABLE u_info_switch_control
(
    `id`                BIGINT NOT NULL COMMENT '主键',
    `platform_id`       BIGINT NOT NULL COMMENT '平台id',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    `terminal_type_eum` INT    NOT NULL COMMENT '控制的终端',
    `system_type_eum`   INT    NOT NULL COMMENT '控制的系统',
    `binding_email`     TINYINT(1) COMMENT '绑定邮箱-默认开启',
    `binding_phone`     TINYINT(1) COMMENT '绑定手机号-默认开启',
    PRIMARY KEY (id)
) COMMENT = '用户信息修改开关控制';


CREATE INDEX ursc_p_idx ON u_info_switch_control (platform_id);
CREATE INDEX ursc_pc_idx ON u_info_switch_control (platform_id, create_time);
CREATE INDEX ursc_pu_idx ON u_info_switch_control (platform_id, update_time);
CREATE INDEX ursc_pst_idx ON u_info_switch_control (platform_id, terminal_type_eum, system_type_eum);

DROP TABLE IF EXISTS r_platform_end_day_record;
CREATE TABLE r_platform_end_day_record
(
    `id`                           BIGINT NOT NULL COMMENT '主键',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    `end_day_status`               TINYINT(1) COMMENT '每日日结状态',
    `end_day_time`                 VARCHAR(90) COMMENT '日结时间-年-月-日',
    `end_day_start_calculate_time` VARCHAR(90) COMMENT '每日日结结算日期',
    PRIMARY KEY (id)
) COMMENT = '平台日结记录';


CREATE INDEX idx_platform_time ON r_platform_end_day_record (platform_id, end_day_time);

DROP TABLE IF EXISTS u_invite_code_failure_log;
CREATE TABLE u_invite_code_failure_log
(
    `id`                        BIGINT NOT NULL COMMENT '主键',
    `invite_code`               VARCHAR(32) COMMENT '邀请码',
    `invited_user_code`         BIGINT COMMENT '被邀请用户code',
    `invite_failure_reason_eum` INT COMMENT '邀请码失败原因',
    `user_id`                   BIGINT COMMENT '用户id',
    `user_code`                 BIGINT COMMENT '用户code',
    `username`                  VARCHAR(32) COMMENT '用户名',
    `platform_id`               BIGINT NOT NULL COMMENT '平台id',
    `create_by`                 VARCHAR(32) COMMENT '创建人',
    `create_time`               BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                 VARCHAR(32) COMMENT '更新人',
    `update_time`               BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '邀请码失败日志';


CREATE INDEX uf_log_p_idx ON u_invite_code_failure_log (platform_id);
CREATE INDEX uf_log_pc_idx ON u_invite_code_failure_log (platform_id, create_time);
CREATE INDEX uf_log_pu_idx ON u_invite_code_failure_log (platform_id, user_code);

DROP TABLE IF EXISTS u_excel_data;
CREATE TABLE u_excel_data
(
    `id`                    BIGINT NOT NULL COMMENT 'id主键',
    `user_code`             BIGINT COMMENT '会员ID',
    `real_name`             VARCHAR(255) COMMENT '姓名',
    `username`              VARCHAR(255) COMMENT '会员账号',
    `hierarchical_name`     VARCHAR(255) COMMENT '层级',
    `vip_name`              VARCHAR(255) COMMENT '会员等级',
    `status`                VARCHAR(255) COMMENT '状态',
    `nick_name`             VARCHAR(255) COMMENT '昵称',
    `phone`                 VARCHAR(255) COMMENT '电话',
    `parent_id`             VARCHAR(255) COMMENT '上级ID',
    `total_recharge_amount` DECIMAL(24, 6) COMMENT '累计充值',
    `total_withdraw_amount` DECIMAL(24, 6) COMMENT '累计提现',
    `total_profit_amount`   DECIMAL(24, 6) COMMENT '累计输赢',
    `login_equipment`       VARCHAR(255) COMMENT '登录设备',
    `channel_id`            BIGINT COMMENT '渠道ID',
    `login_ip`              VARCHAR(255) COMMENT '登录ip',
    `login_address`         VARCHAR(255) COMMENT '登录地址',
    `login_time`            VARCHAR(255) COMMENT '登录时间',
    `reg_ip`                VARCHAR(255) COMMENT '注册ip',
    `reg_address`           VARCHAR(255) COMMENT '注册地址',
    `reg_time`              VARCHAR(255) COMMENT '注册时间',
    `reg_robot_code`        VARCHAR(255) COMMENT '注册机器码',
    `reg_source`            VARCHAR(255) COMMENT '注册来源',
    `account_amount`        DECIMAL(24, 6) COMMENT '账号余额',
    `safe_box_amount`       DECIMAL(24, 6) COMMENT '保险箱余额',
    `platform_id`           BIGINT NOT NULL COMMENT '平台id/租户id',
    `update_time`           BIGINT COMMENT '创建时间',
    `update_by`             VARCHAR(32) COMMENT '创建人',
    `create_time`           BIGINT NOT NULL COMMENT '更新时间',
    `create_by`             VARCHAR(32) COMMENT '更新人',
    PRIMARY KEY (id)
) COMMENT = 'k8用户数据';


CREATE INDEX u_e_pid_idx ON u_excel_data (platform_id);

DROP TABLE IF EXISTS u_clear_data_control;
CREATE TABLE u_clear_data_control
(
    `id`                       BIGINT      NOT NULL COMMENT '主键',
    `job_start_calculate_time` VARCHAR(90) NOT NULL COMMENT '定时任务开始时间',
    `platform_id`              BIGINT      NOT NULL COMMENT '平台id',
    `create_by`                VARCHAR(32) COMMENT '创建人',
    `create_time`              BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`                VARCHAR(32) COMMENT '更新人',
    `update_time`              BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据清除控制表';


CREATE INDEX ucc_p_idx ON u_clear_data_control (platform_id);

DROP TABLE IF EXISTS u_clear_data_record;
CREATE TABLE u_clear_data_record
(
    `id`           BIGINT NOT NULL COMMENT '主键',
    `clear_status` TINYINT(1) COMMENT '清理状态',
    `clear_date`   VARCHAR(90) COMMENT '清理日期-年-月-日',
    `platform_id`  BIGINT NOT NULL COMMENT '平台id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据清理记录表';


CREATE INDEX ucr_pt_idx ON u_clear_data_record (platform_id, clear_date);

DROP TABLE IF EXISTS r_platform_today_summary_record;
CREATE TABLE r_platform_today_summary_record
(
    `id`              BIGINT NOT NULL COMMENT '主键',
    `platform_id`     BIGINT NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    `summary_status`  TINYINT(1) COMMENT '汇总状态',
    `summary_time`    VARCHAR(90) COMMENT '汇总时间-年-月-日',
    `data_start_time` BIGINT COMMENT '数据时间范围的开始时间',
    `data_end_time`   BIGINT COMMENT '数据时间范围的结束时间',
    `consuming_time`  BIGINT COMMENT '耗时',
    PRIMARY KEY (id)
) COMMENT = '平台今日汇总记录';


CREATE INDEX idx_pt ON r_platform_today_summary_record (platform_id, summary_time);

DROP TABLE IF EXISTS u_event_exception_log;
CREATE TABLE u_event_exception_log
(
    `id`                   BIGINT NOT NULL COMMENT '主键',
    `platform_id`          BIGINT NOT NULL COMMENT '平台id',
    `create_by`            VARCHAR(32) COMMENT '创建人',
    `create_time`          BIGINT NOT NULL COMMENT '创建时间',
    `update_by`            VARCHAR(32) COMMENT '更新人',
    `update_time`          BIGINT COMMENT '更新时间',
    `user_id`              BIGINT COMMENT '用户ID',
    `user_code`            BIGINT COMMENT '用户编码',
    `username`             VARCHAR(90) COMMENT '用户名',
    `event_name`           VARCHAR(90) COMMENT '事件名称',
    `exception_message`    TEXT COMMENT '错误消息',
    `client_equipmen_type` VARCHAR(32) COMMENT '设备类型',
    `client_origin`        VARCHAR(255) COMMENT '客户端origin',
    `client_ip`            VARCHAR(32) COMMENT '客户端ip',
    PRIMARY KEY (id)
) COMMENT = '事件错误日志';


CREATE INDEX idx_p ON u_event_exception_log (platform_id);

DROP TABLE IF EXISTS r_platform_profit_summary_record;
CREATE TABLE r_platform_profit_summary_record
(
    `id`              BIGINT      NOT NULL COMMENT '主键',
    `platform_id`     BIGINT      NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    `summary_status`  TINYINT(1) DEFAULT 0 COMMENT '汇总状态',
    `summary_time`    VARCHAR(90) NOT NULL COMMENT '汇总时间-年-月-日',
    `data_start_time` BIGINT DEFAULT 0 COMMENT '数据时间范围的开始时间',
    `data_end_time`   BIGINT DEFAULT 0 COMMENT '数据时间范围的结束时间',
    `consuming_time`  BIGINT DEFAULT 0 COMMENT '耗时',
    PRIMARY KEY (id)
) COMMENT = '平台利润汇总记录';


CREATE INDEX idx_pt ON r_platform_profit_summary_record (platform_id, summary_time);

DROP TABLE IF EXISTS r_platform_profit_summary;
CREATE TABLE r_platform_profit_summary
(
    `id`                 BIGINT      NOT NULL COMMENT '主键',
    `platform_id`        BIGINT      NOT NULL COMMENT '平台id',
    `create_by`          VARCHAR(32) COMMENT '创建人',
    `create_time`        BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`          VARCHAR(32) COMMENT '更新人',
    `update_time`        BIGINT COMMENT '更新时间',
    `record_id`          BIGINT      NOT NULL COMMENT '汇总记录表ID',
    `statistical_time`   VARCHAR(90) NOT NULL COMMENT '统计时间-年月日',
    `recharge_amount`    DECIMAL(24, 6) DEFAULT 0 COMMENT '实收金额',
    `withdraw_amount`    DECIMAL(24, 6) DEFAULT 0 COMMENT '提现金额',
    `inout_diff`         DECIMAL(24, 6) DEFAULT 0 COMMENT '充提差（含测试数据）',
    `real_inout_diff`    DECIMAL(24, 6) DEFAULT 0 COMMENT '真实充提差（不包含测试数据）',
    `withdraw_rate`      DECIMAL(24, 6) DEFAULT 0 COMMENT '提现比',
    `bet_amount`         DECIMAL(24, 6) DEFAULT 0 COMMENT '总投注量',
    `payout_amount`      DECIMAL(24, 6) DEFAULT 0 COMMENT '总派奖',
    `bet_rate`           DECIMAL(24, 6) DEFAULT 0 COMMENT '流水比',
    `payout_rate`        DECIMAL(24, 6) DEFAULT 0 COMMENT '返奖率',
    `profit_amount`      DECIMAL(24, 6) DEFAULT 0 COMMENT '流水利润',
    `discount_amount`    DECIMAL(24, 6) DEFAULT 0 COMMENT '活动产出',
    `real_profit_amount` DECIMAL(24, 6) DEFAULT 0 COMMENT '真实利润',
    PRIMARY KEY (id)
) COMMENT = '平台利润汇总';


CREATE INDEX idx_pt ON r_platform_profit_summary (platform_id, statistical_time);

DROP TABLE IF EXISTS rc_rule_config;
CREATE TABLE rc_rule_config
(
    `id`           BIGINT NOT NULL COMMENT '主键',
    `platform_id`  BIGINT NOT NULL COMMENT '平台id',
    `rule_type`    INT COMMENT '规则类型',
    `rule_title`   VARCHAR(255) COMMENT '标题',
    `rule_content` LONGTEXT COMMENT '内容',
    `sort`         INT COMMENT '排序',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '规则配置';

CREATE INDEX rc_p_idx ON rc_rule_config (platform_id);
CREATE INDEX rc_pc_idx ON rc_rule_config (platform_id, create_time);
CREATE INDEX rc_prt_idx ON rc_rule_config (platform_id, rule_type);

DROP TABLE IF EXISTS u_label;
CREATE TABLE u_label
(
    `id`                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`             BIGINT NOT NULL COMMENT '平台id',
    `label_code`              BIGINT NOT NULL COMMENT '标签编码',
    `label_name`              VARCHAR(255) COMMENT '标签名称',
    `label_type_eum`          INT COMMENT '标签类型',
    `status_eum`              INT    NOT NULL COMMENT '状态',
    `label_img_url`           VARCHAR(255) COMMENT '标签图标地址',
    `start_amount`            DECIMAL(24, 6) COMMENT '开始范围',
    `end_amount`              DECIMAL(24, 6) COMMENT '结束范围',
    `precondition_type_eum`   VARCHAR(255) COMMENT '前置条件类型(1无,2充值,3打码)',
    `bet_start_amount`        DECIMAL(24, 6) COMMENT '打码量开始范围',
    `bet_end_amount`          DECIMAL(24, 6) COMMENT '打码量结束范围',
    `recharge_start_amount`   DECIMAL(24, 6) COMMENT '充值量开始范围',
    `recharge_end_amount`     DECIMAL(24, 6) COMMENT '充值量结束范围',
    `effective_type_eum`      INT COMMENT '有效类型(2倒计时,3指定时间)',
    `effective_time_unit_eum` INT COMMENT '有效时间单位(时-有效类型是[倒计时]的必填)',
    `duration`                INT COMMENT '有效时长(有效类型是[倒计时]的必填)',
    `appoint_time`            BIGINT COMMENT '指定过期时间(有效类型是[指定时间]的必填)',
    `quality_type_eum`        VARCHAR(32) COMMENT '品质类型',
    `effect_status_eum`       INT COMMENT '标签作用是否有效(0激活,1失效)',
    `effect_eum`              INT COMMENT '标签作用',
    `effect_value`            DECIMAL(24, 6) COMMENT '作用值',
    `usage_user_count`        BIGINT COMMENT '作用/使用人数',
    `create_by`               VARCHAR(32) COMMENT '创建人',
    `create_time`             BIGINT NOT NULL COMMENT '创建时间',
    `update_by`               VARCHAR(32) COMMENT '更新人',
    `update_time`             BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '用户标签';

CREATE INDEX l_p_idx ON u_label (platform_id);
CREATE INDEX l_pc_idx ON u_label (create_time, platform_id);
CREATE INDEX l_plts_idx ON u_label (platform_id, label_type_eum, status_eum);
CREATE INDEX l_plc_idx ON u_label (platform_id, label_code);

DROP TABLE IF EXISTS u_user_label;
CREATE TABLE u_user_label
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `user_id`        VARCHAR(255) COMMENT '用户ID',
    `user_code`      BIGINT COMMENT '用户编码',
    `username`       VARCHAR(90) COMMENT '用户名',
    `label_type_eum` INT COMMENT '标签类型',
    `label_id`       BIGINT COMMENT '标签主键',
    `get_the_time`   BIGINT COMMENT '获得标签的时间',
    `effective_time` BIGINT COMMENT '有效时间',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '用户和标签关联';

CREATE INDEX ul_p_idx ON u_user_label (platform_id);
CREATE INDEX ul_pc_idx ON u_user_label (platform_id, create_time);
CREATE INDEX ul_pu_idx ON u_user_label (platform_id, update_time);
CREATE INDEX ul_pui_idx ON u_user_label (platform_id, user_id);
CREATE INDEX ul_pli_idx ON u_user_label (platform_id, label_id);
CREATE INDEX ul_pult_idx ON u_user_label (platform_id, user_id, label_type_eum);

DROP TABLE IF EXISTS u_user_label_job_record;
CREATE TABLE u_user_label_job_record
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `start_datetime` BIGINT COMMENT '任务开始执行时间',
    `end_datetime`   BIGINT COMMENT '任务结束执行时间',
    `job_status_eum` INT COMMENT '任务状态;-1执行中-2正常结束-3异常结束-4待执行',
    `record_type`    INT COMMENT '任务类型:1设置标签,2取消标签',
    `msg`            LONGTEXT COMMENT '消息json',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '用户标签job任务记录';

CREATE INDEX uljr_p_idx ON u_user_label_job_record (platform_id);
CREATE INDEX uljr_pc_idx ON u_user_label_job_record (platform_id, create_time);
CREATE INDEX uljr_pr_idx ON u_user_label_job_record (platform_id, record_type);
CREATE INDEX uljr_pjr_idx ON u_user_label_job_record (platform_id, job_status_eum, record_type);
