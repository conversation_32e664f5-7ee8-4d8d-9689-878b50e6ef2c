DROP TABLE IF EXISTS u_login_log;
CREATE TABLE u_login_log
(
    `id`                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`             BIGINT NOT NULL COMMENT '平台id',
    `create_by`               VARCHAR(32) COMMENT '创建人',
    `update_by`               VARCHAR(32) COMMENT '更新人',
    `create_time`             BIGINT NOT NULL COMMENT '创建时间',
    `update_time`             BIGINT COMMENT '更新时间',
    `user_id`                 BIGINT COMMENT '用户id【账号不存在时为空】',
    `user_code`               BIGINT COMMENT '用户code【账号不存在时为空】',
    `username`                VARCHAR(32) COMMENT '用户名【账号不存在时为空】',
    `login_ip`                VARCHAR(32) COMMENT '登录ip',
    `login_time`              BIGINT COMMENT '登录时间',
    `login_device_id`         VARCHAR(255) COMMENT '登录的设备 app使用设备id 浏览器使用指纹串',
    `login_ua`                VARCHAR(255) COMMENT '登录的浏览器ua app没有',
    `login_source`            VARCHAR(255) COMMENT '登录来源 浏览器的获取登录站点的域名 app使用渠道码',
    `login_version`           VARCHAR(90) COMMENT '登录的客户端软件版本 app用包的版本号 浏览器用接口版本号',
    `login_os`                VARCHAR(90) COMMENT '登录的客户端系统',
    `login_os_version`        VARCHAR(90) COMMENT '登录的客户端系统版本',
    `login_apn`               VARCHAR(90) COMMENT '登录的客户端网络类型',
    `login_type_eum`          INT COMMENT '登录方式',
    `login_result_eum`        INT COMMENT '登录结果',
    `login_clcppz_eum`        INT COMMENT '登录的国家枚举',
    `login_equipmen_type_eum` INT COMMENT '登录的设备',
    `login_terminal_type_eum` INT COMMENT '登录的终端',
    `login_system_type_eum`   INT COMMENT '登录的系统',
    PRIMARY KEY (id)
) COMMENT = '用户登录日志';


CREATE INDEX ulogin_log_p_idx ON u_login_log (platform_id);
CREATE INDEX ulogin_log_pc_idx ON u_login_log (platform_id, create_time);

DROP TABLE IF EXISTS u_reg_log;
CREATE TABLE u_reg_log
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `user_id`                BIGINT NOT NULL COMMENT '用户id',
    `user_code`              BIGINT NOT NULL COMMENT '用户code',
    `username`               VARCHAR(32) COMMENT '用户名',
    `reg_type_eum`           INT COMMENT '注册的方式',
    `reg_clcppz_eum`         INT COMMENT '注册的国家枚举',
    `reg_currency_eum`       INT COMMENT '注册的货币 可以为空 为空使用注册国家的默认货币',
    `reg_source`             VARCHAR(90) COMMENT '注册来源 浏览器的获取注册站点的域名 app获取落地页域名',
    `reg_channel_code`       VARCHAR(255) COMMENT '注册渠道邀请码 邀请用户的用户id生成的渠道码 可以反转回上级用户id',
    `reg_device_id`          VARCHAR(255) COMMENT '注册的设备 app使用设备id 浏览器使用指纹串',
    `reg_ua`                 TEXT COMMENT '注册的浏览器ua 浏览器有 app为null',
    `reg_version`            VARCHAR(90) COMMENT '注册的客户端软件版本 app用包的版本号 浏览器用接口版本号',
    `reg_os`                 VARCHAR(90) COMMENT '注册的客户端系统',
    `reg_os_version`         VARCHAR(90) COMMENT '注册的客户端系统',
    `reg_apn`                VARCHAR(90) COMMENT '注册的客户端网络类型',
    `reg_equipmen_type_eum`  INT COMMENT '注册的设备',
    `reg_terminal_type_eum`  INT COMMENT '注册的终端',
    `reg_system_type_eum`    INT COMMENT '注册的来源系统',
    `separate_package_id`    VARCHAR(90) COMMENT '源包标识',
    `reg_ip`                 VARCHAR(32) COMMENT '注册的ip',
    `app_pack_serial_number` BIGINT COMMENT 'app打包的序号',
    PRIMARY KEY (id)
) COMMENT = '用户注册日志信息';


CREATE INDEX ureg_log_p_idx ON u_reg_log (platform_id);
CREATE INDEX ureg_log_pc_idx ON u_reg_log (platform_id, create_time);
CREATE INDEX ureg_log_pu_idx ON u_reg_log (platform_id, user_code);

DROP TABLE IF EXISTS u_open_user_auth_log;
CREATE TABLE u_open_user_auth_log
(
    `id`            BIGINT NOT NULL COMMENT '主键',
    `platform_id`   BIGINT NOT NULL COMMENT '平台id',
    `create_by`     VARCHAR(32) COMMENT '创建人',
    `create_time`   BIGINT NOT NULL COMMENT '创建时间',
    `update_by`     VARCHAR(32) COMMENT '更新人',
    `update_time`   BIGINT COMMENT '更新时间',
    `source`        VARCHAR(90) COMMENT '登录三方来源',
    `auth_response` TEXT COMMENT '授权返回信息',
    `user_id`       BIGINT NOT NULL COMMENT '用户ID',
    `username`      VARCHAR(32) COMMENT '用户名',
    `user_code`     BIGINT COMMENT '用户编码',
    PRIMARY KEY (id)
) COMMENT = '用户三方授权回调日志';


CREATE INDEX uoual_pid_idx ON u_open_user_auth_log (platform_id);
CREATE INDEX uoual_pc_idx ON u_open_user_auth_log (platform_id, create_time);
CREATE INDEX uoual_puid_idx ON u_open_user_auth_log (platform_id, user_id);

DROP TABLE IF EXISTS u_invite_code_failure_log;
CREATE TABLE u_invite_code_failure_log
(
    `id`                        BIGINT NOT NULL COMMENT '主键',
    `invite_code`               VARCHAR(32) COMMENT '邀请码',
    `invited_user_code`         BIGINT COMMENT '被邀请用户code',
    `invite_failure_reason_eum` INT COMMENT '邀请码失败原因',
    `user_id`                   BIGINT COMMENT '用户id',
    `user_code`                 BIGINT COMMENT '用户code',
    `username`                  VARCHAR(32) COMMENT '用户名',
    `platform_id`               BIGINT NOT NULL COMMENT '平台id',
    `create_by`                 VARCHAR(32) COMMENT '创建人',
    `create_time`               BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                 VARCHAR(32) COMMENT '更新人',
    `update_time`               BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '邀请码失败日志';


CREATE INDEX uf_log_p_idx ON u_invite_code_failure_log (platform_id);
CREATE INDEX uf_log_pc_idx ON u_invite_code_failure_log (platform_id, create_time);
CREATE INDEX uf_log_pu_idx ON u_invite_code_failure_log (platform_id, user_code);

DROP TABLE IF EXISTS u_event;
CREATE TABLE u_event
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    `user_id`        BIGINT COMMENT '用户ID',
    `user_code`      BIGINT COMMENT '用户编码',
    `username`       VARCHAR(90) COMMENT '用户名',
    `serial_number`  BIGINT COMMENT 'app打包序号',
    `event_type_eum` INT COMMENT '事件类型',
    `pixel_id`       VARCHAR(255) COMMENT '像素ID;fbid',
    `pixel_type`     VARCHAR(90) COMMENT '像素类型',
    PRIMARY KEY (id)
) COMMENT = '用户事件';


CREATE INDEX u_event_pidx ON u_event (platform_id);
CREATE INDEX u_event_pcidx ON u_event (platform_id, create_time);
CREATE INDEX u_event_puidx ON u_event (platform_id, update_time);
CREATE INDEX u_event_pucidx ON u_event (platform_id, user_code, create_time);

DROP TABLE IF EXISTS u_event_exception_log;
CREATE TABLE u_event_exception_log
(
    `id`                   BIGINT NOT NULL COMMENT '主键',
    `platform_id`          BIGINT NOT NULL COMMENT '平台id',
    `create_by`            VARCHAR(32) COMMENT '创建人',
    `create_time`          BIGINT NOT NULL COMMENT '创建时间',
    `update_by`            VARCHAR(32) COMMENT '更新人',
    `update_time`          BIGINT COMMENT '更新时间',
    `user_id`              BIGINT COMMENT '用户ID',
    `user_code`            BIGINT COMMENT '用户编码',
    `username`             VARCHAR(90) COMMENT '用户名',
    `event_name`           VARCHAR(90) COMMENT '事件名称',
    `exception_message`    TEXT COMMENT '错误消息',
    `client_equipmen_type` VARCHAR(32) COMMENT '设备类型',
    `client_origin`        VARCHAR(255) COMMENT '客户端origin',
    `client_ip`            VARCHAR(32) COMMENT '客户端ip',
    PRIMARY KEY (id)
) COMMENT = '事件错误日志';


CREATE INDEX u_eel_p_idx ON u_event_exception_log (platform_id);
CREATE INDEX u_eel_pc_idx ON u_event_exception_log (platform_id, create_time);
CREATE INDEX u_eel_pu_idx ON u_event_exception_log (platform_id, update_time);
CREATE INDEX u_eel_pcucode_idx ON u_event_exception_log (platform_id, create_time, user_code);

DROP TABLE IF EXISTS u_download_app_log;
CREATE TABLE u_download_app_log
(
    `id`                  BIGINT NOT NULL COMMENT '主键',
    `platform_id`         BIGINT NOT NULL COMMENT '平台id',
    `create_by`           VARCHAR(32) COMMENT '创建人',
    `create_time`         BIGINT NOT NULL COMMENT '创建时间',
    `update_by`           VARCHAR(32) COMMENT '更新人',
    `update_time`         BIGINT COMMENT '更新时间',
    `user_id`             BIGINT COMMENT '用户id【账号不存在时为空】',
    `user_code`           BIGINT COMMENT '用户code【账号不存在时为空】',
    `username`            VARCHAR(32) COMMENT '用户名【账号不存在时为空】',
    `download_origin_eum` INT COMMENT '下载源',
    `ip`                  VARCHAR(32) COMMENT 'ip',
    `device_id`           VARCHAR(255) COMMENT '设备 app使用设备id 浏览器使用指纹串',
    `ua`                  VARCHAR(255) COMMENT '浏览器ua',
    `source`              VARCHAR(255) COMMENT '来源',
    `version`             VARCHAR(90) COMMENT '客户端软件版本',
    `apn`                 VARCHAR(90) COMMENT '客户端网络类型',
    `clcppz_eum`          INT COMMENT '国家枚举',
    `equipmen_type_eum`   INT COMMENT '设备类型',
    `terminal_type_eum`   INT COMMENT '终端',
    `system_type_eum`     INT COMMENT '系统类型',
    PRIMARY KEY (id)
) COMMENT = '用户下载app日志';

