DROP TABLE IF EXISTS r_platform_home_user_count_data;
CREATE TABLE r_platform_home_user_count_data
(
    `id`                BIGINT NOT NULL COMMENT '主键',
    `platform_id`       BIGINT NOT NULL COMMENT '平台id',
    `end_date`          VARCHAR(255) COMMENT '所属日期',
    `user_code`         BIGINT COMMENT '用户code',
    `is_reg`            TINYINT(1) COMMENT '是否当天注册',
    `is_recharge`       TINYINT(1) COMMENT '是否充值',
    `is_withdraw`       TINYINT(1) COMMENT '是否提现',
    `is_bet`            TINYINT(1) COMMENT '是否投注',
    `is_first_recharge` TINYINT(1) COMMENT '是否首次充值',
    `recharge_count`    INT COMMENT '充值次数',
    `withdraw_count`    INT COMMENT '提现次数',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '首页用户信息数据表';


CREATE INDEX pucd_pu_idx ON r_platform_home_user_count_data (user_code, platform_id);
CREATE INDEX pucd_pc_idx ON r_platform_home_user_count_data (create_time, platform_id);
CREATE INDEX pucd_ep_idx ON r_platform_home_user_count_data (end_date, platform_id);

DROP TABLE IF EXISTS r_home_user_count_data_job_record;
CREATE TABLE r_home_user_count_data_job_record
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `start_datetime` BIGINT COMMENT '任务开始执行时间',
    `job_status`     TINYINT(1) COMMENT '任务状态',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '首页用户次数数据job任务记录';


CREATE INDEX hucdjr_p_idx ON r_home_user_count_data_job_record (platform_id);
CREATE INDEX hucdjr_ps_idx ON r_home_user_count_data_job_record (platform_id, start_datetime);

DROP TABLE IF EXISTS r_platform_home_data;
CREATE TABLE r_platform_home_data
(
    `id`                        BIGINT NOT NULL COMMENT '主键',
    `platform_id`               BIGINT NOT NULL COMMENT '平台id',
    `end_date`                  VARCHAR(255) COMMENT '所属日期',
    `first_recharge_amount`     DECIMAL(24, 6) COMMENT '首存金额',
    `recharge_discount`         DECIMAL(24, 6) COMMENT '充值赠送',
    `recharge_amount`           DECIMAL(24, 6) COMMENT '充值金额',
    `withdraw_amount`           DECIMAL(24, 6) COMMENT '出款金额',
    `real_withdraw_amount`      DECIMAL(24, 6) COMMENT '实际出款金额',
    `bet_amount`                DECIMAL(24, 6) COMMENT '游戏下注金额',
    `payout_amount`             DECIMAL(24, 6) COMMENT '游戏派奖金额',
    `profit_amount`             DECIMAL(24, 6) COMMENT '游戏输赢金额',
    `user_shared_reg_num`       INT COMMENT '玩家分享-邀请的注册用户数',
    `register_time_num`         BIGINT COMMENT '注册人数',
    `register_total_num`        BIGINT COMMENT '总注册人数',
    `bet_time_num`              BIGINT COMMENT '投注人数',
    `bet_total_num`             BIGINT COMMENT '总投注人数',
    `recharge_time_num`         BIGINT COMMENT '充值人数',
    `recharge_total_num`        BIGINT COMMENT '总充值人数',
    `recharge_first_time_num`   BIGINT COMMENT '首次充值人数',
    `recharge_first_total_num`  BIGINT COMMENT '首次充值人数总数',
    `twice_recharge_time_num`   BIGINT COMMENT '两次及以上充值人数',
    `twice_recharge_total_num`  BIGINT COMMENT '两次及以上充值总人数',
    `thrice_recharge_time_num`  BIGINT COMMENT '三次及以上充值人数',
    `thrice_recharge_total_num` BIGINT COMMENT '三次及以上充值总人数',
    `withdraw_time_num`         BIGINT COMMENT '提现人数',
    `withdraw_total_num`        BIGINT COMMENT '累计提现人数',
    `twice_withdraw_time_num`   BIGINT COMMENT '两次及以上提现人数',
    `twice_withdraw_total_num`  BIGINT COMMENT '两次及以上提现总人数',
    `thrice_withdraw_time_num`  BIGINT COMMENT '三次及以上提现人数',
    `thrice_withdraw_total_num` BIGINT COMMENT '三次及以上提现总人数',
    `create_by`                 VARCHAR(32) COMMENT '创建人',
    `create_time`               BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                 VARCHAR(32) COMMENT '更新人',
    `update_time`               BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '首页数据表';


CREATE INDEX phd_pc_idx ON r_platform_home_data (create_time, platform_id);

