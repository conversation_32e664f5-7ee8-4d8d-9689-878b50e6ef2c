DROP TABLE IF EXISTS ar_control_settings;
CREATE TABLE ar_control_settings
(
    `id`                    BIGINT NOT NULL COMMENT '主键',
    `platform_id`           BIGINT NOT NULL COMMENT '平台id',
    `create_by`             VARCHAR(90) COMMENT '创建人',
    `create_time`           BIGINT NOT NULL COMMENT '创建时间',
    `update_by`             VARCHAR(90) COMMENT '更新人',
    `update_time`           BIGINT COMMENT '更新时间',
    `rate_control_type_eum` INT    NOT NULL COMMENT '比例计算和显示类型',
    PRIMARY KEY (id)
) COMMENT = '返佣比例控制设置';


CREATE INDEX arcs_p_idx ON ar_control_settings (platform_id);

DROP TABLE IF EXISTS ar_settlement_record;
CREATE TABLE ar_settlement_record
(
    `id`                            BIGINT NOT NULL COMMENT '主键',
    `platform_id`                   BIGINT NOT NULL COMMENT '平台id',
    `create_by`                     VARCHAR(90) COMMENT '创建人',
    `create_time`                   BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                     VARCHAR(90) COMMENT '更新人',
    `update_time`                   BIGINT COMMENT '更新时间',
    `user_id`                       BIGINT NOT NULL COMMENT '用户id',
    `user_code`                     BIGINT NOT NULL COMMENT '用户code',
    `username`                      VARCHAR(90) COMMENT '用户名',
    `calculate_type_eum`            INT    NOT NULL COMMENT '佣金计算类型-打码or输or赢',
    `calculate_mode_eum`            INT    NOT NULL COMMENT '佣金计算模式-无限级or3级',
    `category_type_eum`             INT COMMENT '大类',
    `manufacturers_type_eum`        INT COMMENT '厂商',
    `sub_type_eum`                  INT COMMENT '小类-子游戏',
    `treat_rebate_bet_amount`       DECIMAL(24, 6) COMMENT '待返佣的打码金额-按有效投注计算有效',
    `treat_rebate_win_amount`       DECIMAL(24, 6) COMMENT '待返佣的赢金额-按赢计算有效',
    `treat_rebate_lose_amount`      DECIMAL(24, 6) COMMENT '待返佣的输金额-按输计算有效',
    `settlement_rebate_bet_amount`  DECIMAL(24, 6) COMMENT '有效投注额结算的佣金-按有效投注计算有效',
    `settlement_rebate_win_amount`  DECIMAL(24, 6) COMMENT '赢金额结算的佣金-按赢计算有效',
    `settlement_rebate_lose_amount` DECIMAL(24, 6) COMMENT '输金额结算的佣金-按输计算有效',
    `receive_rebate_rate`           DECIMAL(24, 6) COMMENT '结算的返佣比例',
    `bet_source_user_id`            BIGINT COMMENT '创造业绩的来源用户id',
    `direct_receive_rebate_rate`    DECIMAL(24, 6) COMMENT '直属返佣比例',
    `indirect_receive_rebate_rate`  DECIMAL(24, 6) COMMENT '非直属返佣比例',
    `source_user_is_directly_under` TINYINT(1) COMMENT '创造业绩的来源用户id是否是直属用户',
    `agent_index`                   INT COMMENT '代理层级',
    `treat_record_id`               BIGINT COMMENT '对应的待结算记录',
    `summary_record_id`             BIGINT COMMENT '汇总表的ID',
    `settlement_formula`            VARCHAR(255) COMMENT '佣金结算比例计算公式',
    PRIMARY KEY (id)
) COMMENT = '佣金结算记录';


CREATE INDEX arsr_p_idx ON ar_settlement_record (platform_id);
CREATE INDEX arsr_pc_idx ON ar_settlement_record (platform_id, create_time);
CREATE INDEX arsr_pcategory_idx ON ar_settlement_record (platform_id, category_type_eum);
CREATE INDEX arsr_pmanufacturers_idx ON ar_settlement_record (platform_id, manufacturers_type_eum);
CREATE INDEX arsr_psub_type_idx ON ar_settlement_record (platform_id, sub_type_eum);
CREATE INDEX arsr_puser_directly_idx ON ar_settlement_record (platform_id, user_id, bet_source_user_id);
CREATE INDEX arsr_puser_directly_ctime_idx ON ar_settlement_record (platform_id, user_id, source_user_is_directly_under, create_time);

DROP TABLE IF EXISTS ar_team_change_record;
CREATE TABLE ar_team_change_record
(
    `id`                                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`                             BIGINT NOT NULL COMMENT '平台id',
    `create_by`                               VARCHAR(90) COMMENT '创建人',
    `create_time`                             BIGINT NOT NULL COMMENT '创建时间',
    `update_time`                             BIGINT COMMENT '更新时间',
    `update_by`                               VARCHAR(90) COMMENT '更新人',
    `user_id`                                 BIGINT NOT NULL COMMENT '用户id',
    `user_code`                               BIGINT NOT NULL COMMENT '用户code',
    `username`                                VARCHAR(90) COMMENT '用户名',
    `before_team_total_count`                 INT COMMENT '改变前团队总人数=直属+非直属（非直属也就是直属的下级发展的人数）',
    `now_team_total_count`                    INT COMMENT '改变后团队总人数=直属+非直属（非直属也就是直属的下级发展的人数）',
    `before_directly_under_total_count`       INT COMMENT '改变前直属总人数',
    `now_directly_under_total_count`          INT COMMENT '改变后直属总人数',
    `before_other_total_count`                INT COMMENT '改变前非直属总人数',
    `now_other_total_count`                   INT COMMENT '改变后非直属总人数',
    `before_team_total_achievement`           DECIMAL(24, 6) COMMENT '改变前团队总业绩=直属+非直属',
    `now_team_total_achievement`              DECIMAL(24, 6) COMMENT '改变后团队总业绩=直属+非直属',
    `before_directly_under_total_achievement` DECIMAL(24, 6) COMMENT '改变前直属总业绩',
    `now_directly_under_total_achievement`    DECIMAL(24, 6) COMMENT '改变后直属总业绩',
    `before_other_total_achievement`          DECIMAL(24, 6) COMMENT '改变前非直属总业绩',
    `now_other_total_achievement`             DECIMAL(24, 6) COMMENT '改变后非直属总业绩',
    `before_team_total_recharge`              DECIMAL(24, 6) COMMENT '改变前团队总充值=直属+非直属',
    `now_team_total_recharge`                 DECIMAL(24, 6) COMMENT '改变后团队总充值=直属+非直属',
    `before_team_total_bet`                   DECIMAL(24, 6) COMMENT '改变前团队总有效投注=直属+非直属',
    `now_team_total_bet`                      DECIMAL(24, 6) COMMENT '改变后团队总有效投注=直属+非直属',
    `before_team_total_win_lose`              DECIMAL(24, 6) COMMENT '改变前团队总输赢=直属+非直属',
    `now_team_total_win_lose`                 DECIMAL(24, 6) COMMENT '改变后团队总输赢=直属+非直属',
    PRIMARY KEY (id)
) COMMENT = '代理团队信息改变记录';


CREATE INDEX artcr_p_idx ON ar_team_change_record (platform_id);
CREATE INDEX artcr_pc_idx ON ar_team_change_record (platform_id, create_time);
CREATE INDEX artcr_puid_idx ON ar_team_change_record (platform_id, user_id);

DROP TABLE IF EXISTS ar_team;
CREATE TABLE ar_team
(
    `id`                               BIGINT NOT NULL COMMENT '主键',
    `platform_id`                      BIGINT NOT NULL COMMENT '平台id',
    `create_by`                        VARCHAR(90) COMMENT '创建人',
    `create_time`                      BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                        VARCHAR(90) COMMENT '更新人',
    `update_time`                      BIGINT COMMENT '更新时间',
    `user_id`                          BIGINT NOT NULL COMMENT '用户id',
    `user_code`                        BIGINT NOT NULL COMMENT '用户code',
    `username`                         VARCHAR(90) COMMENT '用户名',
    `team_total_count`                 INT COMMENT '团队总人数=直属+非直属（非直属也就是直属的下级发展的人数）',
    `directly_under_total_count`       INT COMMENT '直属总人数',
    `other_total_count`                INT COMMENT '非直属总人数',
    `team_total_achievement`           DECIMAL(24, 6) COMMENT '团队总业绩=直属+非直属',
    `directly_under_total_achievement` DECIMAL(24, 6) COMMENT '直属总业绩',
    `other_total_achievement`          DECIMAL(24, 6) COMMENT '非直属总业绩',
    `team_total_recharge`              DECIMAL(24, 6) COMMENT '团队总充值=直属+非直属',
    `team_total_bet`                   DECIMAL(24, 6) COMMENT '团队总有效投注=直属+非直属',
    `team_total_win_lose`              DECIMAL(24, 6) COMMENT '团队总输赢=直属+非直属',
    `team_recharge_count`              INT COMMENT '团队充值人数',
    `channel_info`                     TEXT COMMENT '渠道信息',
    PRIMARY KEY (id)
) COMMENT = '代理团队';


CREATE INDEX art_p_idx ON ar_team (platform_id);
CREATE INDEX art_pc_idx ON ar_team (platform_id, create_time);
CREATE INDEX art_puid_idx ON ar_team (platform_id, user_id);
CREATE INDEX art_pucode_idx ON ar_team (platform_id, user_code);

DROP TABLE IF EXISTS ar_terminal_control;
CREATE TABLE ar_terminal_control
(
    `id`                                BIGINT NOT NULL COMMENT '主键',
    `platform_id`                       BIGINT NOT NULL COMMENT '平台id',
    `create_by`                         VARCHAR(90) COMMENT '创建人',
    `create_time`                       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                         VARCHAR(90) COMMENT '更新人',
    `update_time`                       BIGINT COMMENT '更新时间',
    `promote_unlimited_bet_img_url`     VARCHAR(255) COMMENT '无限级有效投注量推广说明图地址',
    `promote_unlimited_lose_img_url`    VARCHAR(255) COMMENT '无限级输量推广说明图地址',
    `promote_unlimited_win_img_url`     VARCHAR(255) COMMENT '无限级赢量推广说明图地址',
    `promote_three_floors_bet_img_url`  VARCHAR(255) COMMENT '关联3级有效投注量推广说明图地址',
    `promote_three_floors_lose_img_url` VARCHAR(255) COMMENT '关联3级输量推广说明图地址',
    `promote_three_floors_win_img_url`  VARCHAR(255) COMMENT '关联3级赢量推广说明图地址',
    `terminal_type_eum`                 INT    NOT NULL COMMENT '控制的终端',
    `system_type_eum`                   INT    NOT NULL COMMENT '控制的系统',
    PRIMARY KEY (id)
) COMMENT = '代理终端控制';


CREATE INDEX artc_p_idx ON ar_terminal_control (platform_id);
CREATE INDEX artc_pc_idx ON ar_terminal_control (platform_id, create_time);
CREATE INDEX artc_pts_idx ON ar_terminal_control (platform_id, terminal_type_eum, system_type_eum);

DROP TABLE IF EXISTS ar_treat_record;
CREATE TABLE ar_treat_record
(
    `id`                            BIGINT NOT NULL COMMENT '主键',
    `platform_id`                   BIGINT NOT NULL COMMENT '平台id',
    `create_by`                     VARCHAR(90) COMMENT '创建人',
    `create_time`                   BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                     VARCHAR(90) COMMENT '更新人',
    `update_time`                   BIGINT COMMENT '更新时间',
    `user_id`                       BIGINT NOT NULL COMMENT '用户id',
    `user_code`                     BIGINT COMMENT '用户code',
    `username`                      VARCHAR(90) COMMENT '用户名',
    `calculate_type_eum`            INT COMMENT '佣金计算类型-打码or输or赢',
    `calculate_mode_eum`            INT COMMENT '佣金计算模式-无限级or3级',
    `category_type_eum`             INT COMMENT '大类',
    `manufacturers_type_eum`        INT COMMENT '厂商',
    `sub_type_eum`                  INT COMMENT '小类-子游戏',
    `treat_rebate_bet_amount`       DECIMAL(24, 6) COMMENT '待返佣的打码金额-按有效投注计算有效',
    `treat_rebate_win_amount`       DECIMAL(24, 6) COMMENT '待返佣的赢金额-按赢计算有效',
    `treat_rebate_lose_amount`      DECIMAL(24, 6) COMMENT '待返佣的输金额-按输计算有效',
    `bet_source_user_id`            BIGINT COMMENT '创造业绩的来源用户id',
    `source_user_is_directly_under` TINYINT(1) COMMENT '创造业绩的来源用户id是否是直属用户',
    `agent_index`                   INT COMMENT '创造业绩的代理链路索引-从0开始，0：直属，1：直属的上级，2：直属上级的上级...',
    PRIMARY KEY (id)
) COMMENT = '待结算返佣记录';


CREATE INDEX artr_p_idx ON ar_treat_record (platform_id);
CREATE INDEX artr_pc_idx ON ar_treat_record (platform_id, create_time);
CREATE INDEX artr_puid_idx ON ar_treat_record (platform_id, user_id);

DROP TABLE IF EXISTS ar_book_change_record;
CREATE TABLE ar_book_change_record
(
    `id`                                 BIGINT NOT NULL COMMENT 'id主键',
    `before_treat_receive_rebate_amount` DECIMAL(24, 6) COMMENT '改变前待领取佣金',
    `now_treat_receive_rebate_amount`    DECIMAL(24, 6) COMMENT '改变后待领取佣金',
    `before_receive_rebate_total_amount` DECIMAL(24, 6) COMMENT '改变前已领取佣金总额',
    `now_receive_rebate_total_amount`    DECIMAL(24, 6) COMMENT '改变后已领取佣金总额',
    `book_change_type_eum`               INT    NOT NULL COMMENT '改变类型的枚举',
    `change_rebate_amount`               DECIMAL(24, 6) COMMENT '改变的佣金',
    `into_settlement_record_ids`         VARCHAR(255) COMMENT '入账记录的改变来源ids-如多个结算单结算加的佣金结算记录id',
    `user_id`                            BIGINT NOT NULL COMMENT '用户id',
    `user_code`                          BIGINT NOT NULL COMMENT '用户code',
    `username`                           VARCHAR(32) COMMENT '用户名',
    `platform_id`                        BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                          VARCHAR(32) COMMENT '创建人',
    `create_time`                        BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                          VARCHAR(32) COMMENT '更新人',
    `update_time`                        BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '佣金账本改变记录';


CREATE INDEX arbcr_p_idx ON ar_book_change_record (platform_id);
CREATE INDEX arbcr_pc_idx ON ar_book_change_record (platform_id, create_time);
CREATE INDEX arbcr_pcuid_idx ON ar_book_change_record (platform_id, create_time, user_id);

DROP TABLE IF EXISTS ar_book;
CREATE TABLE ar_book
(
    `id`                          BIGINT NOT NULL COMMENT 'id主键',
    `treat_receive_rebate_amount` DECIMAL(24, 6) COMMENT '待领取佣金-每日结算累加-用户领取减去加到用户余额',
    `receive_rebate_total_amount` DECIMAL(24, 6) COMMENT '已领取佣金总额',
    `last_receive_rebate_amount`  DECIMAL(24, 6) COMMENT '最后一次领取佣金金额',
    `last_receive_rebate_time`    BIGINT COMMENT '最后一次领取佣金时间',
    `user_id`                     BIGINT NOT NULL COMMENT '用户id',
    `user_code`                   BIGINT NOT NULL COMMENT '用户code',
    `username`                    VARCHAR(32) COMMENT '用户名',
    `platform_id`                 BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                   VARCHAR(32) COMMENT '创建人',
    `create_time`                 BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                   VARCHAR(32) COMMENT '更新人',
    `update_time`                 BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '代理返佣账本';


CREATE INDEX arb_p_idx ON ar_book (platform_id);
CREATE INDEX arb_pc_idx ON ar_book (platform_id, create_time);
CREATE INDEX arb_puid_idx ON ar_book (platform_id, user_id);

DROP TABLE IF EXISTS ar_control_change_record;
CREATE TABLE ar_control_change_record
(
    `id`                              BIGINT NOT NULL COMMENT 'id主键',
    `before_day_start_calculate_time` VARCHAR(32) COMMENT '改变前佣金每日开始计算时间 时:分:秒',
    `now_day_start_calculate_time`    VARCHAR(32) COMMENT '改变后佣金每日开始计算时间 时:分:秒',
    `before_calculate_type_eum`       INT COMMENT '改变前佣金计算类型-打码or输or赢',
    `now_calculate_type_eum`          INT COMMENT '改变后佣金计算类型-打码or输or赢',
    `before_calculate_mode_eum`       INT COMMENT '改变前佣金计算模式-无限级or3级',
    `now_calculate_mode_eum`          INT COMMENT '改变后佣金计算模式-无限级or3级',
    `before_commission_bet_multiple`  DECIMAL(24, 6) COMMENT '改变前领取佣金所需的打码倍数',
    `now_commission_bet_multiple`     DECIMAL(24, 6) COMMENT '改变后领取佣金所需的打码倍数',
    `before_no_recharge_rebate`       TINYINT(1) COMMENT '改变前未充值是否返佣给上级代理',
    `now_no_recharge_rebate`          TINYINT(1) COMMENT '改变后未充值是否返佣给上级代理',
    `before_system_type_eum`          INT COMMENT '改变前控制的系统',
    `now_system_type_eum`             INT COMMENT '改变后控制的系统',
    `platform_id`                     BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                       VARCHAR(32) COMMENT '创建人',
    `create_time`                     BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                       VARCHAR(32) COMMENT '更新人',
    `update_time`                     BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '代理返佣设置修改记录';


CREATE INDEX arccr_p_idx ON ar_control_change_record (platform_id);
CREATE INDEX arccr_pc_idx ON ar_control_change_record (platform_id, create_time);

DROP TABLE IF EXISTS ar_control;
CREATE TABLE ar_control
(
    `id`                       BIGINT     NOT NULL COMMENT 'id主键',
    `day_start_calculate_time` VARCHAR(32) COMMENT '佣金每日开始计算时间 时:分:秒',
    `calculate_type_eum`       INT        NOT NULL COMMENT '佣金计算类型-打码or输or赢',
    `calculate_mode_eum`       INT        NOT NULL COMMENT '佣金计算模式-无限级or3级',
    `commission_bet_multiple`  DECIMAL(24, 6) COMMENT '领取佣金所需的打码倍数',
    `no_recharge_rebate`       TINYINT(1) NOT NULL COMMENT '未充值是否返佣给上级代理',
    `system_type_eum`          INT        NOT NULL COMMENT '控制的系统',
    `platform_id`              BIGINT     NOT NULL COMMENT '平台id/租户id',
    `create_by`                VARCHAR(32) COMMENT '创建人',
    `create_time`              BIGINT     NOT NULL COMMENT '创建时间',
    `update_by`                VARCHAR(32) COMMENT '更新人',
    `update_time`              BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '代理返佣控制 每个系统对应一条数据 没有初始一条数据';


CREATE INDEX arc_p_idx ON ar_control (platform_id);
CREATE INDEX arc_pc_idx ON ar_control (platform_id, create_time);
CREATE INDEX arc_ps_idx ON ar_control (platform_id, system_type_eum);

DROP TABLE IF EXISTS ar_game_setting;
CREATE TABLE ar_game_setting
(
    `id`                           BIGINT NOT NULL COMMENT 'id主键',
    `category_type_eum`            INT COMMENT '大类',
    `manufacturers_type_eum`       INT COMMENT '厂商',
    `sub_type_eum`                 INT COMMENT '小类-子游戏',
    `bet_amount_start`             DECIMAL(24, 6) COMMENT '有效投注起',
    `bet_amount_end`               DECIMAL(24, 6) COMMENT '有效投注止',
    `win_amount_start`             DECIMAL(24, 6) COMMENT '赢起',
    `win_amount_end`               DECIMAL(24, 6) COMMENT '赢止',
    `lose_amount_start`            DECIMAL(24, 6) COMMENT '输起',
    `lose_amount_end`              DECIMAL(24, 6) COMMENT '输止',
    `receive_rebate_rate`          DECIMAL(24, 6) COMMENT '直属-返佣比例0.01格式',
    `indirect_receive_rebate_rate` DECIMAL(24, 6) COMMENT '非直属-返佣比例0.01格式',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '游戏返佣比例设置';


CREATE INDEX args_p_idx ON ar_game_setting (platform_id);
CREATE INDEX args_pc_idx ON ar_game_setting (platform_id, create_time);
CREATE INDEX args_pcte_idx ON ar_game_setting (platform_id, category_type_eum);
CREATE INDEX args_pctemte_idx ON ar_game_setting (platform_id, category_type_eum, manufacturers_type_eum);
CREATE INDEX args_pste_idx ON ar_game_setting (platform_id, sub_type_eum);

DROP TABLE IF EXISTS ap_cooperate_domain_name;
CREATE TABLE ap_cooperate_domain_name
(
    `id`                         BIGINT     NOT NULL COMMENT 'id主键',
    `agent_domain_name_type_eum` INT COMMENT '域名类型',
    `httpser`                    TINYINT(1) NOT NULL COMMENT '域名是否是https否则http',
    `domain_name`                VARCHAR(32) COMMENT '域名或者ip',
    `global_defaulter`           TINYINT(1) NOT NULL COMMENT '是否推广页生效',
    `landing_defaulter`          TINYINT(1) NOT NULL COMMENT '是否落地页生效',
    `opener`                     TINYINT(1) NOT NULL COMMENT '是否开启',
    `reg_total_num`              BIGINT COMMENT '通过此域名渠道注册的人数，统计的是单个代理渠道的总注册人数',
    `user_id`                    BIGINT     NOT NULL COMMENT '用户id',
    `user_code`                  BIGINT     NOT NULL COMMENT '用户code',
    `username`                   VARCHAR(32) COMMENT '用户名',
    `platform_id`                BIGINT     NOT NULL COMMENT '平台id/租户id',
    `create_by`                  VARCHAR(32) COMMENT '创建人',
    `create_time`                BIGINT     NOT NULL COMMENT '创建时间',
    `update_by`                  VARCHAR(32) COMMENT '更新人',
    `update_time`                BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '推广合作代理落地页域名-推广合作代理分享的域名';


CREATE INDEX apcdn_p_idx ON ap_cooperate_domain_name (platform_id);
CREATE INDEX apcdn_pc_idx ON ap_cooperate_domain_name (platform_id, create_time);
CREATE INDEX apcdn_puc_idx ON ap_cooperate_domain_name (user_code, platform_id);
CREATE INDEX apcdn_ld_idx ON ap_cooperate_domain_name (user_id, landing_defaulter, opener, platform_id);
CREATE INDEX apcdn_gd_idx ON ap_cooperate_domain_name (user_code, global_defaulter, opener, platform_id);

DROP TABLE IF EXISTS ap_everyone_domain_name;
CREATE TABLE ap_everyone_domain_name
(
    `id`                         BIGINT     NOT NULL COMMENT 'id主键',
    `agent_domain_name_type_eum` INT COMMENT '域名类型',
    `domain_name_code`           BIGINT COMMENT '域名编码',
    `httpser`                    TINYINT(1) NOT NULL COMMENT '域名是否是https否则http',
    `domain_name`                VARCHAR(32) COMMENT '域名或者ip',
    `global_defaulter`           TINYINT(1) NOT NULL COMMENT '是否推广页生效',
    `landing_defaulter`          TINYINT(1) NOT NULL COMMENT '是否落地页生效',
    `opener`                     TINYINT(1) NOT NULL COMMENT '是否开启',
    `reg_total_num`              BIGINT COMMENT '通过此域名渠道注册的人数, 是全民代理每个下级渠道合并的总数',
    `platform_id`                BIGINT     NOT NULL COMMENT '平台id/租户id',
    `create_by`                  VARCHAR(32) COMMENT '创建人',
    `create_time`                BIGINT     NOT NULL COMMENT '创建时间',
    `update_by`                  VARCHAR(32) COMMENT '更新人',
    `update_time`                BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '全民落地页域名-全民分享的域名';


CREATE INDEX apedn_p_idx ON ap_everyone_domain_name (platform_id);
CREATE INDEX apedn_pc_idx ON ap_everyone_domain_name (platform_id, create_time);
CREATE INDEX apedn_gd_idx ON ap_everyone_domain_name (global_defaulter, opener, platform_id);
CREATE INDEX apedn_ld_idx ON ap_everyone_domain_name (landing_defaulter, opener, platform_id);
CREATE INDEX apedn_dmc_idx ON ap_everyone_domain_name (domain_name_code, opener, platform_id);

DROP TABLE IF EXISTS ap_share_type_control;
CREATE TABLE ap_share_type_control
(
    `id`                     BIGINT     NOT NULL COMMENT 'id主键',
    `promote_share_type_eum` INT        NOT NULL COMMENT '类型枚举',
    `type_img_url`           TEXT COMMENT '分享枚举类型对应的图片地址',
    `open_or_close`          TINYINT(1) NOT NULL COMMENT '开启 or 关闭',
    `share_open_app_url`     TEXT COMMENT '分享打开三方app的链接地址',
    `share_desc`             VARCHAR(900) COMMENT '分享描述-分享的文本信息',
    `platform_id`            BIGINT     NOT NULL COMMENT '平台id/租户id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT     NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '客户端推广分享类型控制开关 查询为空也代表关闭';


CREATE INDEX apstc_p_idx ON ap_share_type_control (platform_id);
CREATE INDEX apstc_pc_idx ON ap_share_type_control (platform_id, create_time);

DROP TABLE IF EXISTS ap_invite_control;
CREATE TABLE ap_invite_control
(
    `id`                   BIGINT         NOT NULL COMMENT 'id主键',
    `status_eum`           INT            NOT NULL COMMENT '状态',
    `text`                 TEXT COMMENT '邀请有礼描述(富文本)',
    `must_recharger`       TINYINT(1)     NOT NULL COMMENT '是否必须充值',
    `must_recharge_amount` DECIMAL(24, 6) COMMENT '必须充值金额',
    `must_beter`           TINYINT(1)     NOT NULL COMMENT '是否必须投注',
    `must_bet_amount`      DECIMAL(24, 6) COMMENT '必须投注金额',
    `bet_multiple`         DECIMAL(24, 6) NOT NULL COMMENT '赠送金额的打码倍数',
    `platform_id`          BIGINT         NOT NULL COMMENT '平台id/租户id',
    `create_by`            VARCHAR(32) COMMENT '创建人',
    `create_time`          BIGINT         NOT NULL COMMENT '创建时间',
    `update_by`            VARCHAR(32) COMMENT '更新人',
    `update_time`          BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '邀请有礼控制';


CREATE INDEX ai_pid_uni_idx ON ap_invite_control (platform_id);
CREATE INDEX ai_crt_uni_idx ON ap_invite_control (platform_id, create_time);
CREATE INDEX ai_sts_uni_idx ON ap_invite_control (status_eum, platform_id);

DROP TABLE IF EXISTS ap_invite_item;
CREATE TABLE ap_invite_item
(
    `id`           BIGINT NOT NULL COMMENT 'id主键',
    `invite_num`   INT    NOT NULL COMMENT '邀请人数',
    `award_amount` DECIMAL(24, 6) COMMENT '奖励金额',
    `platform_id`  BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '邀请有礼明细';


CREATE INDEX aiv_pid_uni_idx ON ap_invite_item (platform_id);
CREATE INDEX aiv_crt_uni_idx ON ap_invite_item (platform_id, create_time);

DROP TABLE IF EXISTS ap_invite_record;
CREATE TABLE ap_invite_record
(
    `id`          BIGINT NOT NULL COMMENT 'id主键',
    `invite_num`  INT    NOT NULL COMMENT '邀请人数',
    `user_codes`  VARCHAR(255) COMMENT '已邀请的用户userCode集合',
    `user_id`     BIGINT NOT NULL COMMENT '用户id',
    `user_code`   BIGINT NOT NULL COMMENT '用户code',
    `username`    VARCHAR(32) COMMENT '用户名',
    `platform_id` BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '邀请记录';


CREATE INDEX apr_pid_uni_idx ON ap_invite_record (platform_id);
CREATE INDEX apr_crt_uni_idx ON ap_invite_record (platform_id, create_time);

DROP TABLE IF EXISTS ap_invite_pay_record;
CREATE TABLE ap_invite_pay_record
(
    `id`          BIGINT NOT NULL COMMENT 'id主键',
    `invite_num`  INT    NOT NULL COMMENT '邀请达标门槛(人)',
    `amount`      DECIMAL(24, 6) COMMENT '邀请奖励',
    `user_id`     BIGINT NOT NULL COMMENT '用户id',
    `user_code`   BIGINT NOT NULL COMMENT '用户code',
    `username`    VARCHAR(32) COMMENT '用户名',
    `platform_id` BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '邀请奖励发放记录';


CREATE INDEX aip_pid_uni_idx ON ap_invite_pay_record (platform_id);
CREATE INDEX aip_crt_uni_idx ON ap_invite_pay_record (platform_id, create_time);
CREATE INDEX aip_uid_uni_idx ON ap_invite_pay_record (user_id, platform_id);
CREATE INDEX aip_ucode_uni_idx ON ap_invite_pay_record (user_code, platform_id);

DROP TABLE IF EXISTS ap_domain_name_record;
CREATE TABLE ap_domain_name_record
(
    `id`          BIGINT NOT NULL COMMENT 'id主键',
    `domain_name` VARCHAR(255) COMMENT '访问域名',
    `visitors`    INT COMMENT '访问人数',
    `visit_date`  VARCHAR(255) COMMENT '访问日期',
    `platform_id` BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '域名访问统计表';


CREATE INDEX ad_crt_idx ON ap_domain_name_record (create_time);
CREATE INDEX ad_dn_idx ON ap_domain_name_record (domain_name, visit_date, platform_id);
CREATE INDEX ad_pid_idx ON ap_domain_name_record (platform_id);

DROP TABLE IF EXISTS ar_transfer_record;
CREATE TABLE ar_transfer_record
(
    `id`                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`             BIGINT NOT NULL COMMENT '平台id',
    `create_by`               VARCHAR(32) COMMENT '创建人',
    `create_time`             BIGINT NOT NULL COMMENT '创建时间',
    `update_by`               VARCHAR(32) COMMENT '更新人',
    `update_time`             BIGINT COMMENT '更新时间',
    `transferred_user_id`     BIGINT COMMENT '被转移用户ID',
    `transferred_username`    VARCHAR(32) COMMENT '被转移用户名',
    `transferred_user_code`   BIGINT COMMENT '被转移用户Code',
    `before_parent_user_id`   BIGINT COMMENT '改变前父级用户ID',
    `before_parent_username`  VARCHAR(32) COMMENT '改变前父级用户名',
    `before_parent_user_code` BIGINT COMMENT '改变前父级用户Code',
    `now_parent_user_id`      BIGINT COMMENT '改变后新父级用户ID',
    `now_parent_username`     VARCHAR(32) COMMENT '改变后父级用户名',
    `now_parent_user_code`    BIGINT COMMENT '改变后父级用户code',
    `system_remark`           VARCHAR(255) COMMENT '系统备注',
    `remark`                  VARCHAR(255) COMMENT '备注',
    PRIMARY KEY (id)
) COMMENT = '代理转移记录';

DROP TABLE IF EXISTS ar_settlement_summary_record;
CREATE TABLE ar_settlement_summary_record
(
    `id`                                  BIGINT NOT NULL COMMENT '主键',
    `platform_id`                         BIGINT NOT NULL COMMENT '平台id',
    `create_by`                           VARCHAR(32) COMMENT '创建人',
    `create_time`                         BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                           VARCHAR(32) COMMENT '更新人',
    `update_time`                         BIGINT COMMENT '更新时间',
    `user_id`                             BIGINT NOT NULL COMMENT '用户id',
    `user_code`                           BIGINT COMMENT '用户code',
    `username`                            VARCHAR(90) COMMENT '用户名',
    `calculate_type_eum`                  INT COMMENT '佣金计算类型-打码or输or赢',
    `calculate_mode_eum`                  INT COMMENT '佣金计算模式-无限级or3级',
    `category_type_eum`                   INT COMMENT '大类',
    `manufacturers_type_eum`              INT COMMENT '厂商',
    `sub_type_eum`                        INT COMMENT '小类-子游戏',
    `treat_rebate_total_bet_amount`       DECIMAL(24, 6) COMMENT '待返佣的总打码金额-按有效投注计算有效',
    `treat_rebate_total_win_amount`       DECIMAL(24, 6) COMMENT '待返佣的总赢金额-按赢计算有效',
    `treat_rebate_total_lose_amount`      DECIMAL(24, 6) COMMENT '待返佣的总输金额-按输计算有效',
    `settlement_rebate_total_bet_amount`  DECIMAL(24, 6) COMMENT '总有效投注额结算的佣金-按有效投注计算有效',
    `settlement_rebate_total_win_amount`  DECIMAL(24, 6) COMMENT '总赢金额结算的佣金-按赢计算有效',
    `settlement_rebate_total_lose_amount` DECIMAL(24, 6) COMMENT '总输金额结算的佣金-按输计算有效',
    `receive_rebate_rate`                 DECIMAL(24, 6) COMMENT '结算的返佣比例',
    `direct_receive_rebate_rate`          DECIMAL(24, 6) COMMENT '直属返佣比例',
    `indirect_receive_rebate_rate`        DECIMAL(24, 6) COMMENT '非直属返佣比例',
    `agent_index`                         INT COMMENT '创造业绩的代理链路索引-从0开始，0：直属，1：直属的上级，2：直属上级的上级...',
    `settlement_start_time`               BIGINT COMMENT '结算的开始时间;用于查询',
    `settlement_end_time`                 BIGINT COMMENT '结算的结算时间;用于查询',
    `settlement_formula`                  VARCHAR(255) COMMENT '佣金结算比例计算公式',
    PRIMARY KEY (id)
) COMMENT = '佣金结算汇总记录;用户ID、agentIndex、比例计算类型（按大类或厂商或子类）三个条件唯一';


CREATE INDEX assr_pid ON ar_settlement_summary_record (platform_id);
CREATE INDEX assr_pusetime ON ar_settlement_summary_record (platform_id, settlement_start_time, settlement_end_time,
                                                            user_code, username);

DROP TABLE IF EXISTS ar_settlement_job_record;
CREATE TABLE ar_settlement_job_record
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    `end_day_time`   VARCHAR(32) COMMENT '任务日结的日期;日结时间-年月日',
    `job_start_time` VARCHAR(32) COMMENT '任务执行开始时间;执行开始时间-年-月-日 时:分:秒',
    `job_end_time`   VARCHAR(32) COMMENT '任务执行结束时间;执行结束时间-年-月-日 时:分:秒',
    `job_status_eum` INT COMMENT '任务状态;-1执行中-2正常结束-3异常结束',
    `settlement_num` BIGINT COMMENT '结算的记录数;结算的记录数',
    PRIMARY KEY (id)
) COMMENT = '佣金日结任务记录表';


CREATE INDEX asjr_pid ON ar_settlement_job_record (platform_id);
CREATE INDEX asjr_ptime ON ar_settlement_job_record (platform_id, end_day_time);

DROP TABLE IF EXISTS ap_domain_name_promote_record;
CREATE TABLE ap_domain_name_promote_record
(
    `id`                BIGINT NOT NULL COMMENT 'id主键',
    `domain_name`       VARCHAR(255) COMMENT '访问域名',
    `invite_code`       VARCHAR(90) COMMENT '上级用户的邀请码',
    `invited_user_code` BIGINT COMMENT '被邀请用户的userCode',
    `user_id`           BIGINT NOT NULL COMMENT '用户id',
    `user_code`         BIGINT COMMENT '用户code',
    `username`          VARCHAR(90) COMMENT '用户名',
    `platform_id`       BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '代理域名推广记录';


CREATE INDEX adp_uc_idx ON ap_domain_name_promote_record (platform_id, invited_user_code);
CREATE INDEX adp_ic_idx ON ap_domain_name_promote_record (platform_id, invite_code);
CREATE INDEX adp_ucp_idx ON ap_domain_name_promote_record (user_code, platform_id);
CREATE INDEX adp_dcrt_idx ON ap_domain_name_promote_record (domain_name, create_time, platform_id);

DROP TABLE IF EXISTS ar_team_realtime_data;
CREATE TABLE ar_team_realtime_data
(
    `id`                                          BIGINT NOT NULL COMMENT '主键',
    `platform_id`                                 BIGINT NOT NULL COMMENT '平台id',
    `create_by`                                   VARCHAR(32) COMMENT '创建人',
    `create_time`                                 BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                                   VARCHAR(32) COMMENT '更新人',
    `update_time`                                 BIGINT COMMENT '更新时间',
    `user_id`                                     BIGINT NOT NULL COMMENT '用户id',
    `user_code`                                   BIGINT NOT NULL COMMENT '用户code',
    `username`                                    VARCHAR(90) COMMENT '用户名',
    `reg_terminal_type_eum`                       INT COMMENT '注册终端',
    `parent_id`                                   BIGINT COMMENT '上级编号',
    `team_member_count`                           INT COMMENT '团队总人数',
    `direct_member_count`                         INT COMMENT '直属总人数',
    `other_member_count`                          INT COMMENT '非直属总人数',
    `team_today_count`                            INT COMMENT '团队新增',
    `direct_today_count`                          INT COMMENT '直属新增',
    `other_today_count`                           INT COMMENT '非直属新增',
    `team_bet_amount`                             DECIMAL(24, 6) COMMENT '团队打码量',
    `mine_bet_amount`                             DECIMAL(24, 6) COMMENT '自营打码量',
    `team_bet_count`                              INT COMMENT '打码人数',
    `commission_amount`                           DECIMAL(24, 6) COMMENT '佣金金额',
    `today_commission_amount`                     DECIMAL(24, 6) COMMENT '今日预估佣金',
    `history_commission_amount`                   DECIMAL(24, 6) COMMENT '历史总佣金',
    `recharge_count`                              INT COMMENT '充值总人数',
    `recharge_amount`                             DECIMAL(24, 6) COMMENT '充值总金额',
    `first_recharge_count`                        INT COMMENT '首充人数',
    `first_recharge_amount`                       DECIMAL(24, 6) COMMENT '首充金额',
    `second_recharge_count`                       INT COMMENT '二充人数',
    `second_recharge_amount`                      DECIMAL(24, 6) COMMENT '二充金额',
    `discount_count`                              INT COMMENT '优惠人数',
    `discount_amount`                             DECIMAL(24, 6) COMMENT '优惠金额',
    `first_discount_count`                        INT COMMENT '首充优惠人数',
    `first_discount_amount`                       DECIMAL(24, 6) COMMENT '首充优惠金额',
    `not_today_recharge_count`                    INT COMMENT '非当天注册充值人数',
    `not_today_recharge_amount`                   DECIMAL(24, 6) COMMENT '非当天注册充值金额',
    `withdraw_count`                              INT COMMENT '提现人数',
    `withdraw_amount`                             DECIMAL(24, 6) COMMENT '提现金额',
    `wash_count`                                  INT COMMENT '洗码人数',
    `wash_amount`                                 DECIMAL(24, 6) COMMENT '洗码金额',
    `task_count`                                  INT COMMENT '任务派彩人数',
    `task_amount`                                 DECIMAL(24, 6) COMMENT '任务派彩金额',
    `bet_user_ids`                                LONGTEXT COMMENT '打码用户ID集',
    `recharge_user_ids`                           LONGTEXT COMMENT '充值用户ID集',
    `first_recharge_user_ids`                     LONGTEXT COMMENT '首充用户ID集',
    `second_recharge_user_ids`                    LONGTEXT COMMENT '二充用户ID集',
    `discount_user_ids`                           LONGTEXT COMMENT '优惠用户ID集',
    `first_discount_user_ids`                     LONGTEXT COMMENT '首充优惠用户ID集',
    `not_today_recharge_user_ids`                 LONGTEXT COMMENT '非当天注册充值用户ID集',
    `withdraw_user_ids`                           LONGTEXT COMMENT '提现用户ID集',
    `wash_user_ids`                               LONGTEXT COMMENT '洗码用户ID集',
    `task_user_ids`                               LONGTEXT COMMENT '任务派彩用户ID集',
    `direct_first_recharge_count`                 INT COMMENT '直属首充人数',
    `direct_first_recharge_amount`                DECIMAL(24, 6) COMMENT '直属首充金额',
    `other_first_recharge_count`                  INT COMMENT '非直属首充人数',
    `other_first_recharge_amount`                 DECIMAL(24, 6) COMMENT '非直属首充金额',
    `direct_today_first_recharge_count`           INT COMMENT '今日注册直属首充人数',
    `direct_today_first_recharge_amount`          DECIMAL(24, 6) COMMENT '今日注册直属首充金额',
    `other_today_first_recharge_count`            INT COMMENT '今日注册非直属首充人数',
    `other_today_first_recharge_amount`           DECIMAL(24, 6) COMMENT '今日注册非直属首充金额',
    `direct_bet_count`                            INT COMMENT '直属打码人数',
    `direct_bet_amount`                           DECIMAL(24, 6) COMMENT '直属打码金额',
    `other_bet_count`                             INT COMMENT '非直属打码人数',
    `other_bet_amount`                            DECIMAL(24, 6) COMMENT '非直属打码金额',
    `direct_withdraw_count`                       INT COMMENT '直属提现人数',
    `direct_withdraw_amount`                      DECIMAL(24, 6) COMMENT '直属提现金额',
    `other_withdraw_count`                        INT COMMENT '非直属提现人数',
    `other_withdraw_amount`                       DECIMAL(24, 6) COMMENT '非直属提现金额',
    `team_login_count`                            INT COMMENT '团队登录人数',
    `direct_login_count`                          INT COMMENT '直属登录人数',
    `other_login_count`                           INT COMMENT '非直属登录人数',
    `login_user_ids`                              LONGTEXT COMMENT '登录用户ID集',
    `direct_recharge_count`                       INT COMMENT '直属充值人数',
    `direct_recharge_amount`                      DECIMAL(24, 6) COMMENT '直属充值金额',
    `other_recharge_count`                        INT COMMENT '非直属充值人数',
    `other_recharge_amount`                       DECIMAL(24, 6) COMMENT '非直属充值金额',
    `direct_first_recharge_users_recharge_amount` DECIMAL(24, 6) COMMENT '今日直属首存客户存款总额',
    `other_first_recharge_users_recharge_amount`  DECIMAL(24, 6) COMMENT '今日非直属首存客户存款总额',
    `direct_first_recharge_user_ids`              LONGTEXT COMMENT '直属首充用户ID集',
    `other_first_recharge_user_ids`               LONGTEXT COMMENT '非直属首充用户ID集',
    `channel_info`                                TEXT COMMENT '渠道信息',
    PRIMARY KEY (id)
) COMMENT = '代理团队实时数据';


CREATE INDEX atrd_p_idx ON ar_team_realtime_data (platform_id);
CREATE INDEX atrd_pu_idx ON ar_team_realtime_data (platform_id, user_code);

DROP TABLE IF EXISTS ar_team_history_data;
CREATE TABLE ar_team_history_data
(
    `id`                                          BIGINT NOT NULL COMMENT '主键',
    `platform_id`                                 BIGINT NOT NULL COMMENT '平台id',
    `create_by`                                   VARCHAR(32) COMMENT '创建人',
    `create_time`                                 BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                                   VARCHAR(32) COMMENT '更新人',
    `update_time`                                 BIGINT COMMENT '更新时间',
    `user_id`                                     BIGINT NOT NULL COMMENT '用户id',
    `user_code`                                   BIGINT NOT NULL COMMENT '用户code',
    `username`                                    VARCHAR(90) COMMENT '用户名',
    `end_date`                                    VARCHAR(32) COMMENT '所属日期',
    `team_today_count`                            INT COMMENT '团队新增',
    `direct_today_count`                          INT COMMENT '直属新增',
    `team_bet_count`                              INT COMMENT '打码人数',
    `team_bet_amount`                             DECIMAL(24, 6) COMMENT '团队打码量',
    `direct_bet_amount`                           DECIMAL(24, 6) COMMENT '直属打码量',
    `other_bet_amount`                            DECIMAL(24, 6) COMMENT '非直属打码量',
    `mine_bet_amount`                             DECIMAL(24, 6) COMMENT '自营打码量',
    `team_commission_amount`                      DECIMAL(24, 6) COMMENT '团队总佣金',
    `direct_commission_amount`                    DECIMAL(24, 6) COMMENT '直属佣金',
    `other_commission_amount`                     DECIMAL(24, 6) COMMENT '非直属佣金',
    `recharge_count`                              INT COMMENT '充值总人数',
    `recharge_amount`                             DECIMAL(24, 6) COMMENT '充值总金额',
    `first_recharge_count`                        INT COMMENT '首充人数',
    `first_recharge_amount`                       DECIMAL(24, 6) COMMENT '首充金额',
    `second_recharge_count`                       INT COMMENT '二充人数',
    `second_recharge_amount`                      DECIMAL(24, 6) COMMENT '二充金额',
    `discount_count`                              INT COMMENT '优惠人数',
    `discount_amount`                             DECIMAL(24, 6) COMMENT '优惠金额',
    `first_discount_count`                        INT COMMENT '首充优惠人数',
    `first_discount_amount`                       DECIMAL(24, 6) COMMENT '首充优惠金额',
    `not_today_recharge_count`                    INT COMMENT '非当天注册充值人数',
    `not_today_recharge_amount`                   DECIMAL(24, 6) COMMENT '非当天注册充值金额',
    `withdraw_count`                              INT COMMENT '提现人数',
    `withdraw_amount`                             DECIMAL(24, 6) COMMENT '提现金额',
    `wash_count`                                  INT COMMENT '洗码人数',
    `wash_amount`                                 DECIMAL(24, 6) COMMENT '洗码金额',
    `task_count`                                  INT COMMENT '任务派彩人数',
    `task_amount`                                 DECIMAL(24, 6) COMMENT '任务派彩金额',
    `direct_first_recharge_count`                 INT COMMENT '直属首存人数',
    `direct_first_recharge_amount`                DECIMAL(24, 6) COMMENT '直属首存金额',
    `other_first_recharge_count`                  INT COMMENT '非直属首存人数',
    `other_first_recharge_amount`                 DECIMAL(24, 6) COMMENT '非直属首存金额',
    `direct_today_first_recharge_count`           INT COMMENT '当日注册直属首存人数',
    `direct_today_first_recharge_amount`          DECIMAL(24, 6) COMMENT '当日注册直属首存金额',
    `other_today_first_recharge_count`            INT COMMENT '当日注册非直属首存人数',
    `other_today_first_recharge_amount`           DECIMAL(24, 6) COMMENT '当日注册非直属首存金额',
    `other_today_count`                           INT COMMENT '非直属新增',
    `team_login_count`                            INT COMMENT '团队登录人数',
    `direct_login_count`                          INT COMMENT '直属登录人数',
    `other_login_count`                           INT COMMENT '非直属登录人数',
    `direct_recharge_count`                       INT COMMENT '直属充值人数',
    `direct_recharge_amount`                      DECIMAL(24, 6) COMMENT '直属充值金额',
    `other_recharge_count`                        INT COMMENT '非直属充值人数',
    `other_recharge_amount`                       DECIMAL(24, 6) COMMENT '非直属充值金额',
    `direct_withdraw_count`                       INT COMMENT '直属提现人数',
    `direct_withdraw_amount`                      DECIMAL(24, 6) COMMENT '直属提现金额',
    `other_withdraw_count`                        INT COMMENT '非直属提现人数',
    `other_withdraw_amount`                       DECIMAL(24, 6) COMMENT '非直属提现金额',
    `direct_bet_count`                            INT COMMENT '直属打码人数',
    `other_bet_count`                             INT COMMENT '非直属打码人数',
    `direct_first_recharge_users_recharge_amount` DECIMAL(24, 6) COMMENT '今日直属首存客户存款总额',
    `other_first_recharge_users_recharge_amount`  DECIMAL(24, 6) COMMENT '今日非直属首存客户存款总额',
    `channel_info`                                TEXT COMMENT '渠道信息',
    PRIMARY KEY (id)
) COMMENT = '代理团队历史数据';


CREATE INDEX athd_p_idx ON ar_team_history_data (platform_id);
CREATE INDEX athd_pu_idx ON ar_team_history_data (platform_id, user_code);

DROP TABLE IF EXISTS ar_team_realtime_job_record;
CREATE TABLE ar_team_realtime_job_record
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    `start_datetime` BIGINT COMMENT '任务开始执行时间',
    `job_status`     TINYINT(1) COMMENT '状态',
    PRIMARY KEY (id)
) COMMENT = '代理团队实时数据任务记录';


CREATE INDEX atrjr_p_idx ON ar_team_realtime_job_record (platform_id);
CREATE INDEX atrjr_ps_idx ON ar_team_realtime_job_record (platform_id, start_datetime);

DROP TABLE IF EXISTS ar_team_history_job_record;
CREATE TABLE ar_team_history_job_record
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `job_status`  TINYINT(1) COMMENT '任务状态',
    `job_date`    VARCHAR(90) COMMENT '任务日期-年-月-日',
    PRIMARY KEY (id)
) COMMENT = '代理团队历史记录任务记录';


CREATE INDEX athjr_p_idx ON ar_team_history_job_record (platform_id);
CREATE INDEX athjr_pd_idx ON ar_team_history_job_record (platform_id, job_date);

DROP TABLE IF EXISTS ar_team_history_job_control;
CREATE TABLE ar_team_history_job_control
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `start_time`  VARCHAR(32) COMMENT '每日开始计算时间 时:分:秒',
    PRIMARY KEY (id)
) COMMENT = '代理团队历史记录任务控制';


CREATE INDEX athjc_p_idx ON ar_team_history_job_control (platform_id);

DROP TABLE IF EXISTS ar_team_bet_statistics;
CREATE TABLE ar_team_bet_statistics
(
    `id`                  BIGINT NOT NULL COMMENT '主键',
    `platform_id`         BIGINT NOT NULL COMMENT '平台id',
    `create_by`           VARCHAR(32) COMMENT '创建人',
    `create_time`         BIGINT NOT NULL COMMENT '创建时间',
    `update_by`           VARCHAR(32) COMMENT '更新人',
    `update_time`         BIGINT COMMENT '更新时间',
    `user_id`             BIGINT NOT NULL COMMENT '用户id',
    `user_code`           BIGINT NOT NULL COMMENT '用户code',
    `username`            VARCHAR(90) COMMENT '用户名',
    `end_date`            VARCHAR(90) COMMENT '统计日期-年-月-日',
    `end_date_start_time` BIGINT COMMENT '统计日期开始时间',
    `end_date_end_time`   BIGINT COMMENT '统计日期结束时间',
    `category_type_eum`   INT COMMENT '大类',
    `under_count`         INT COMMENT '总计人数',
    `total_bet_amount`    DECIMAL(24, 6) COMMENT '总打码量',
    `direct_bet_amount`   DECIMAL(24, 6) COMMENT '直属打码量',
    `other_bet_amount`    DECIMAL(24, 6) COMMENT '非直属打码量',
    `mine_bet_amount`     DECIMAL(24, 6) COMMENT '自营打码量',
    `commission_amount`   DECIMAL(24, 6) COMMENT '佣金金额',
    PRIMARY KEY (id)
) COMMENT = '代理团队业绩统计';


CREATE INDEX bs_p_idx ON ar_team_bet_statistics (platform_id);
CREATE INDEX bs_pd_idx ON ar_team_bet_statistics (platform_id, end_date);

DROP TABLE IF EXISTS ar_team_bet_statistics_job_control;
CREATE TABLE ar_team_bet_statistics_job_control
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `start_time`  VARCHAR(32) COMMENT '每日开始计算时间 时:分:秒',
    PRIMARY KEY (id)
) COMMENT = '代理团队业绩统计任务控制';


CREATE INDEX bsjc_p_idx ON ar_team_bet_statistics_job_control (platform_id);

DROP TABLE IF EXISTS ar_team_bet_statistics_job_record;
CREATE TABLE ar_team_bet_statistics_job_record
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `job_status`  TINYINT(1) COMMENT '任务状态',
    `job_date`    VARCHAR(90) COMMENT '任务日期-年-月-日',
    PRIMARY KEY (id)
) COMMENT = '代理团队业绩统计执行记录';


CREATE INDEX bsjr_p_idx ON ar_team_bet_statistics_job_record (platform_id);
CREATE INDEX bsjr_pd_idx ON ar_team_bet_statistics_job_record (platform_id, job_date);

DROP TABLE IF EXISTS ar_team_bet_data;
CREATE TABLE ar_team_bet_data
(
    `id`                  BIGINT NOT NULL COMMENT '主键',
    `platform_id`         BIGINT NOT NULL COMMENT '平台id',
    `create_by`           VARCHAR(32) COMMENT '创建人',
    `create_time`         BIGINT NOT NULL COMMENT '创建时间',
    `update_by`           VARCHAR(32) COMMENT '更新人',
    `update_time`         BIGINT COMMENT '更新时间',
    `user_id`             BIGINT NOT NULL COMMENT '用户id',
    `user_code`           BIGINT NOT NULL COMMENT '用户code',
    `username`            VARCHAR(90) COMMENT '用户名',
    `end_date`            VARCHAR(90) COMMENT '统计日期-年-月-日',
    `end_date_start_time` BIGINT COMMENT '统计日期开始时间',
    `end_date_end_time`   BIGINT COMMENT '统计日期结束时间',
    `category_type_eum`   INT COMMENT '大类',
    `under_id`            BIGINT COMMENT '下级用户ID',
    `under_code`          BIGINT COMMENT '下级用户编码',
    `under_name`          VARCHAR(90) COMMENT '下级用户名',
    `bet_amount`          DECIMAL(24, 6) COMMENT '打码量',
    `commission_amount`   DECIMAL(24, 6) COMMENT '佣金',
    `directer`            TINYINT(1) COMMENT '是否直属',
    PRIMARY KEY (id)
) COMMENT = '代理团队业绩数据';


CREATE INDEX bd_p_idx ON ar_team_bet_data (platform_id);
CREATE INDEX bd_pd_idx ON ar_team_bet_data (platform_id, end_date);
CREATE INDEX bd_pu_idx ON ar_team_bet_data (platform_id, user_id);

DROP TABLE IF EXISTS ar_team_bet_data_job_control;
CREATE TABLE ar_team_bet_data_job_control
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `start_time`  VARCHAR(32) COMMENT '每日开始计算时间 时:分:秒',
    PRIMARY KEY (id)
) COMMENT = '代理团队业绩数据执行控制';


CREATE INDEX bdjr_p_idx ON ar_team_bet_data_job_control (platform_id);

DROP TABLE IF EXISTS ar_team_bet_data_job_record;
CREATE TABLE ar_team_bet_data_job_record
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `job_status`  TINYINT(1) COMMENT '任务状态',
    `job_date`    VARCHAR(90) COMMENT '任务日期-年-月-日',
    PRIMARY KEY (id)
) COMMENT = '代理团队业绩数据执行记录';


CREATE INDEX bdjr_p_idx ON ar_team_bet_data_job_record (platform_id);
CREATE INDEX bdjr_pd_idx ON ar_team_bet_data_job_record (platform_id, job_date);

DROP TABLE IF EXISTS ar_phone_share_config;
CREATE TABLE ar_phone_share_config
(
    `id`                   BIGINT     NOT NULL COMMENT 'id主键',
    `data_type_eum`        INT COMMENT '数据类型（不展示，仅用于区分数据）',
    `phone_share_type_eum` INT        NOT NULL COMMENT '分享类型枚举',
    `type_img_url`         TEXT COMMENT 'icon图片地址',
    `whether_open`         TINYINT(1) NOT NULL COMMENT '是否开启',
    `share_desc`           VARCHAR(900) COMMENT '分享描述',
    `phone_display_rules`  TEXT COMMENT '号码展示规则(一个台子只会有一条)',
    `platform_id`          BIGINT     NOT NULL COMMENT '平台id/租户id',
    `create_by`            VARCHAR(32) COMMENT '创建人',
    `create_time`          BIGINT     NOT NULL COMMENT '创建时间',
    `update_by`            VARCHAR(32) COMMENT '更新人',
    `update_time`          BIGINT COMMENT '更新时间',
    `whether_batch_send`   TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否群发（默认单发）',
    PRIMARY KEY (id)
) COMMENT = '号码分享控制';


CREATE INDEX apsc_pidct_idx ON ar_phone_share_config (platform_id, create_time);

DROP TABLE IF EXISTS ar_phone_pool;
CREATE TABLE ar_phone_pool
(
    `id`               BIGINT     NOT NULL COMMENT '主键',
    `platform_id`      BIGINT     NOT NULL COMMENT '平台id',
    `create_by`        VARCHAR(32) COMMENT '创建人',
    `create_time`      BIGINT     NOT NULL COMMENT '创建时间',
    `update_by`        VARCHAR(32) COMMENT '更新人',
    `update_time`      BIGINT COMMENT '更新时间',
    `phone_type_eum`   VARCHAR(255) COMMENT '号码数据类型（生成的/导入的）',
    `general_batch_id` BIGINT COMMENT '号码生成批次ID',
    `phone`            VARCHAR(32) COMMENT '电话/手机号码',
    `whether_used`     TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否被使用',
    `bind_time`        BIGINT COMMENT '绑定时间',
    `register_time`    BIGINT COMMENT '注册时间',
    `distribute_count` BIGINT COMMENT '分配次数',
    `status_eum`       INT        NOT NULL DEFAULT 0 COMMENT '状态',
    PRIMARY KEY (id)
) COMMENT = '号码池/号码库';


CREATE INDEX phonepool_pidct_idx ON ar_phone_pool (platform_id, create_time);
CREATE INDEX phonepool_pidsedc_idx ON ar_phone_pool (platform_id, status_eum, distribute_count);
CREATE INDEX phonepool_pidphone_idx ON ar_phone_pool (platform_id, phone);

