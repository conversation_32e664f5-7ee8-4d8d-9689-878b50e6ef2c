DROP TABLE IF EXISTS s_sms_record;
CREATE TABLE s_sms_record(
    `id` BIGINT NOT NULL   COMMENT '主键' ,
    `platform_id` BIGINT NOT NULL   COMMENT '平台id' ,
    `phone` VARCHAR(255)    COMMENT '手机号码' ,
    `send_status` TINYINT(1)    COMMENT '发送状态' ,
    `content` VARCHAR(255)    COMMENT '发送内容' ,
    `msg_type_eum` INT    COMMENT '短信类型' ,
    `failure_reason` TEXT    COMMENT '发送失败原因' ,
    `create_by` VARCHAR(32)    COMMENT '创建人' ,
    `create_time` BIGINT NOT NULL   COMMENT '创建时间' ,
    `update_by` VARCHAR(32)    COMMENT '更新人' ,
    `update_time` BIGINT    COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '短信发送记录';


CREATE INDEX sr_p_idx ON s_sms_record(platform_id);
CREATE INDEX sr_pc_idx ON s_sms_record(platform_id,create_time);
CREATE INDEX sr_pp_idx ON s_sms_record(platform_id,phone);

