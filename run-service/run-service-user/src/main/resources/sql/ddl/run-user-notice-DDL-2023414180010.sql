DROP TABLE IF EXISTS n_announce;
CREATE TABLE n_announce
(
    `id`                 BIGINT       NOT NULL COMMENT '主键',
    `notice_status_eum`  INT          NOT NULL COMMENT '状态',
    `title`              VARCHAR(90)  NOT NULL COMMENT '标题',
    `content`            TEXT         NOT NULL COMMENT '内容',
    `announce_level_eum` INT          NOT NULL COMMENT '公告等级枚举',
    `terminal_type_eums` VARCHAR(255) NOT NULL COMMENT '客户端类型枚举',
    `img_url`            TEXT COMMENT '图片地址',
    `link_url`           TEXT COMMENT '跳转链接',
    `send_time`          BIGINT COMMENT '定时发送时间',
    `expire_time`        BIGINT COMMENT '到期时间',
    `system_type_eum`    INT COMMENT '控制的系统',
    `sort`               INT          NOT NULL COMMENT '排序',
    `platform_id`        BIGINT       NOT NULL COMMENT '平台id/租户id',
    `create_by`          VARCHAR(32)  NOT NULL COMMENT '创建人',
    `create_time`        BIGINT       NOT NULL COMMENT '创建时间',
    `update_by`          VARCHAR(32) COMMENT '更新人',
    `update_time`        BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '公告消息';


CREATE INDEX a_p_idx ON n_announce (platform_id);
CREATE INDEX a_pc_idx ON n_announce (platform_id, create_time);

DROP TABLE IF EXISTS n_site_letter;
CREATE TABLE n_site_letter
(
    `id`                       BIGINT      NOT NULL COMMENT '主键',
    `notice_status_eum`        INT         NOT NULL COMMENT '状态',
    `title`                    VARCHAR(90) NOT NULL COMMENT '主题',
    `content`                  TEXT        NOT NULL COMMENT '内容',
    `site_letter_receiver_eum` INT         NOT NULL COMMENT '站内信接收者类型',
    `specify_user_ids`         VARCHAR(255) COMMENT '指定会员ID集，多个用英文,隔开 关联user表的ID',
    `specify_hierarchical_id`  BIGINT COMMENT '指定层级ID 关联hierarchical表的ID',
    `specify_level_ids`        VARCHAR(255) COMMENT '指定等级ID集，多个用英文,隔开 关联level表的ID',
    `specify_parent_ids`       VARCHAR(255) COMMENT '指定上级ID集，多个用英文,隔开 关联user表的userCode',
    `send_time`                BIGINT COMMENT '定时发送时间',
    `expire_time`              BIGINT COMMENT '到期时间',
    `system_type_eum`          INT COMMENT '控制的系统',
    `platform_id`              BIGINT      NOT NULL COMMENT '平台id/租户id',
    `create_by`                VARCHAR(32) NOT NULL COMMENT '创建人',
    `create_time`              BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`                VARCHAR(32) COMMENT '更新人',
    `update_time`              BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '站内信消息';


CREATE INDEX sl_p_idx ON n_site_letter (platform_id);
CREATE INDEX sl_pc_idx ON n_site_letter (platform_id, create_time);

DROP TABLE IF EXISTS n_service_message;
CREATE TABLE n_service_message
(
    `id`                           BIGINT      NOT NULL COMMENT '主键',
    `title`                        VARCHAR(90) NOT NULL COMMENT '主题',
    `content`                      TEXT        NOT NULL COMMENT '内容',
    `attach_info`                  VARCHAR(255) COMMENT '附加信息',
    `send_status`                  TINYINT(1) COMMENT '消息发送状态',
    `service_message_type_eum`     INT COMMENT '服务发送消息大类',
    `service_message_sub_type_eum` INT COMMENT '服务发送消息小类',
    `link_url`                     TEXT COMMENT '重定向链接',
    `source_id`                    BIGINT COMMENT '来源ID',
    `system_type_eum`              INT COMMENT '控制的系统',
    `user_id`                      BIGINT      NOT NULL COMMENT '用户ID',
    `user_code`                    BIGINT COMMENT '用户code',
    `username`                     VARCHAR(32) COMMENT '用户名称',
    `platform_id`                  BIGINT      NOT NULL COMMENT '平台id/租户id',
    `create_by`                    VARCHAR(32) NOT NULL COMMENT '创建人',
    `create_time`                  BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '服务消息';


CREATE INDEX sm_p_idx ON n_service_message (platform_id);
CREATE INDEX sm_pu_idx ON n_service_message (platform_id, user_code);
CREATE INDEX sm_puc_idx ON n_service_message (platform_id, user_code, create_time);

DROP TABLE IF EXISTS n_template;
CREATE TABLE n_template
(
    `id`              BIGINT      NOT NULL COMMENT '主键',
    `notice_type_eum` INT         NOT NULL COMMENT '消息类型',
    `title_temp`      VARCHAR(90) NOT NULL COMMENT '标题模板',
    `content_temp`    TEXT        NOT NULL COMMENT '内容模板',
    `can_use`         TINYINT(1) NOT NULL COMMENT '是否可用',
    `platform_id`     BIGINT      NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) NOT NULL COMMENT '创建人',
    `create_time`     BIGINT      NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '消息模板';


CREATE INDEX t_p_idx ON n_template (platform_id);
CREATE INDEX t_pc_idx ON n_template (platform_id, create_time);
CREATE INDEX t_pn_idx ON n_template (platform_id, notice_type_eum);

DROP TABLE IF EXISTS n_read_record;
CREATE TABLE n_read_record
(
    `id`                BIGINT       NOT NULL COMMENT '主键',
    `notice_type_eum`   VARCHAR(255) NOT NULL COMMENT '通知类型',
    `notice_id`         BIGINT       NOT NULL COMMENT '通知ID',
    `title`             VARCHAR(90) COMMENT '通知标题',
    `content`           TEXT COMMENT '通知内容',
    `read_time`         BIGINT       NOT NULL COMMENT '读取时间',
    `ip`                VARCHAR(32) COMMENT '读取的ip-header传递的',
    `terminal_type_eum` INT COMMENT '读取的终端-header传递的',
    `system_type_eum`   INT COMMENT '控制的系统',
    `deleted`           TINYINT(1) DEFAULT 0 COMMENT '是否已删除',
    `user_id`           BIGINT       NOT NULL COMMENT '用户ID',
    `user_code`         BIGINT COMMENT '用户code',
    `username`          VARCHAR(32) COMMENT '用户名称',
    `platform_id`       BIGINT COMMENT '平台id/租户id',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '消息读取记录';


CREATE INDEX rr_p_idx ON n_read_record (platform_id);
CREATE UNIQUE INDEX rr_punn_idx ON n_read_record (platform_id, user_id, notice_type_eum, notice_id);

DROP TABLE IF EXISTS n_tg_setting;
CREATE TABLE n_tg_setting
(
    `id`              BIGINT NOT NULL COMMENT '主键',
    `platform_id`     BIGINT NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    `chat_id`         VARCHAR(32) COMMENT '发送消息的聊天ID',
    `robot_token`     VARCHAR(90) COMMENT '机器人的API令牌',
    `notice_type_eum` INT COMMENT '消息通知类型;日常、异常等',
    `remark`          VARCHAR(90) COMMENT '备注',
    PRIMARY KEY (id)
) COMMENT = 'TG相关配置';


CREATE INDEX n_tg_notice_p_idx ON n_tg_setting (platform_id);
CREATE INDEX n_tg_notice_pc_idx ON n_tg_setting (platform_id, create_time);
CREATE INDEX n_tg_notice_pu_idx ON n_tg_setting (platform_id, update_time);

