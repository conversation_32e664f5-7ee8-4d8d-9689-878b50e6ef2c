DROP TABLE IF EXISTS t_task;
CREATE TABLE t_task
(
    `id`                  BIGINT         NOT NULL COMMENT 'id主键',
    `task_icon`           VARCHAR(255) COMMENT '任务图标',
    `task_type_eum`       INT            NOT NULL COMMENT '任务类型',
    `task_aging_type_eum` INT            NOT NULL COMMENT '任务时效类型',
    `aging_range`         VARCHAR(90)    NOT NULL COMMENT '任务时效范围',
    `auto_continue`       TINYINT(1) NOT NULL COMMENT '是否自动延续',
    `bet_multiple`        DECIMAL(24, 6) NOT NULL COMMENT '打码倍数',
    `rules`               TEXT COMMENT '任务规则',
    `status_eum`          INT            NOT NULL COMMENT '状态',
    `sort`                INT COMMENT '排序',
    `platform_id`         BIGINT COMMENT '平台id/租户id',
    `create_by`           VARCHAR(32) COMMENT '创建人',
    `create_time`         BIGINT COMMENT '创建时间',
    `update_by`           VARCHAR(32) COMMENT '更新人',
    `update_time`         BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '任务';


CREATE INDEX t_type_uni_idx ON t_task (task_type_eum);
CREATE INDEX t_status_uni_idx ON t_task (status_eum);
CREATE INDEX t_pid_uni_idx ON t_task (platform_id);
CREATE INDEX t_crt_uni_idx ON t_task (platform_id, create_time);
CREATE INDEX t_tim_uni_idx ON t_task (task_aging_type_eum);

DROP TABLE IF EXISTS t_task_achieve_goals_record;
CREATE TABLE t_task_achieve_goals_record
(
    `id`              BIGINT      NOT NULL COMMENT 'id主键',
    `task_id`         BIGINT COMMENT '任务id',
    `item_id`         BIGINT COMMENT '明细id',
    `title`           VARCHAR(90) COMMENT '任务明细标题',
    `aging_range`     VARCHAR(90) NOT NULL COMMENT '任务时效范围',
    `complete_amount` DECIMAL(24, 6) COMMENT '已完成金额',
    `task_status_eum` INT COMMENT '任务完成状态',
    `user_id`         BIGINT COMMENT '用户id',
    `user_code`       BIGINT COMMENT '用户code',
    `username`        VARCHAR(90) COMMENT '用户名',
    `platform_id`     BIGINT COMMENT '平台id/租户id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '任务达标记录';


CREATE INDEX tar_uid_uni_idx ON t_task_achieve_goals_record (user_code, platform_id);
CREATE INDEX tar_pid_uni_idx ON t_task_achieve_goals_record (platform_id);
CREATE INDEX tar_status_uni_idx ON t_task_achieve_goals_record (task_status_eum, platform_id);
CREATE INDEX tar_crt_uni_idx ON t_task_achieve_goals_record (platform_id, create_time);
CREATE INDEX tar_ti_uni_idx ON t_task_achieve_goals_record (task_id, item_id, user_id, platform_id);
CREATE INDEX tar_iu_uni_idx ON t_task_achieve_goals_record (item_id, user_id, platform_id);
CREATE INDEX tar_taid_uni_idx ON t_task_achieve_goals_record (task_id, platform_id);
CREATE INDEX tar_agt_uni_idx ON t_task_achieve_goals_record (aging_range);

DROP TABLE IF EXISTS t_task_distribute_record;
CREATE TABLE t_task_distribute_record
(
    `id`             BIGINT NOT NULL COMMENT 'id主键',
    `task_id`        BIGINT COMMENT '任务id',
    `item_id`        BIGINT COMMENT '任务明细id',
    `title`          VARCHAR(255) COMMENT '任务明细标题',
    `task_type_eum`  INT COMMENT '任务类型',
    `receive_amount` DECIMAL(24, 6) COMMENT '领取金额',
    `bet_multiple`   DECIMAL(24, 6) COMMENT '打码倍数',
    `user_id`        BIGINT COMMENT '用户id',
    `user_code`      BIGINT COMMENT '用户code',
    `username`       VARCHAR(90) COMMENT '用户名',
    `platform_id`    BIGINT COMMENT '平台id/租户id',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '任务派奖记录';


CREATE INDEX tdr_uid_uni_idx ON t_task_distribute_record (user_id, platform_id);
CREATE INDEX tdr_pid_uni_idx ON t_task_distribute_record (platform_id);
CREATE INDEX tdr_crt_uni_idx ON t_task_distribute_record (platform_id, create_time);
CREATE INDEX tdr_type_uni_idx ON t_task_distribute_record (task_type_eum, platform_id);

DROP TABLE IF EXISTS t_task_item;
CREATE TABLE t_task_item
(
    `id`                     BIGINT NOT NULL COMMENT 'id主键',
    `task_id`                BIGINT COMMENT '任务id',
    `title`                  VARCHAR(255) COMMENT '任务明细标题',
    `game_sub_type_eum_list` TEXT COMMENT '活动所属游戏',
    `target_amount`          DECIMAL(24, 6) COMMENT '完成该任务所需金额',
    `award_amount`           DECIMAL(24, 6) COMMENT '奖励金额',
    `platform_id`            BIGINT COMMENT '平台id/租户id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '任务明细';


CREATE INDEX tr_tid_uni_idx ON t_task_item (task_id);
CREATE INDEX tr_pid_uni_idx ON t_task_item (platform_id);
CREATE INDEX tr_crt_uni_idx ON t_task_item (platform_id, create_time);

