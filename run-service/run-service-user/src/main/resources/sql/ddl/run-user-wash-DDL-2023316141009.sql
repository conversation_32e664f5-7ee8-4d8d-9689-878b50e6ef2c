DROP TABLE IF EXISTS w_control_settings;
CREATE TABLE w_control_settings
(
    `id`                    BIGINT NOT NULL COMMENT '主键',
    `platform_id`           BIGINT NOT NULL COMMENT '平台id',
    `create_by`             VARCHAR(32) COMMENT '创建人',
    `create_time`           BIGINT NOT NULL COMMENT '创建时间',
    `update_by`             VARCHAR(32) COMMENT '更新人',
    `update_time`           BIGINT COMMENT '更新时间',
    `rate_control_type_eum` INT    NOT NULL COMMENT '比例计算和显示类型',
    `wash_bet_multiple`     INT    NOT NULL COMMENT '洗码金额的需求打码倍数',
    PRIMARY KEY (id)
) COMMENT = '洗码比例控制设置';


CREATE INDEX wce_p_idx ON w_control_settings (platform_id);

DROP TABLE IF EXISTS w_control;
CREATE TABLE w_control
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `level_eum`              INT    NOT NULL COMMENT '等级枚举',
    `category_type_eum`      INT COMMENT '大类',
    `manufacturers_type_eum` INT COMMENT '厂商',
    `sub_type_eum`           INT COMMENT '小类-子游戏',
    `bet_amount_start`       DECIMAL(24, 6) COMMENT '有效投注起',
    `bet_amount_end`         DECIMAL(24, 6) COMMENT '有效投注止',
    `wash_rate`              DECIMAL(24, 6) COMMENT '洗码比例0.0001格式',
    PRIMARY KEY (id)
) COMMENT = '洗码/返水设置-先找小类（子游戏）-再找厂商-最后找大类-没有则';


CREATE INDEX wc_p_idx ON w_control (platform_id);

DROP TABLE IF EXISTS w_treat_record;
CREATE TABLE w_treat_record
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `user_id`                BIGINT NOT NULL COMMENT '用户id',
    `user_code`              BIGINT NOT NULL COMMENT '用户code',
    `username`               VARCHAR(32) COMMENT '用户名',
    `category_type_eum`      INT COMMENT '大类',
    `manufacturers_type_eum` INT COMMENT '厂商',
    `sub_type_eum`           INT COMMENT '小类-子游戏',
    `treat_wash_bet_amount`  DECIMAL(24, 6) COMMENT '待洗码的打码金额',
    `status_eum`             INT    NOT NULL COMMENT '状态',
    PRIMARY KEY (id)
) COMMENT = '待洗码记录';


CREATE INDEX wtr_p_idx ON w_treat_record (platform_id);
CREATE INDEX wtr_u_idx ON w_treat_record (user_id, platform_id);

DROP TABLE IF EXISTS w_record;
CREATE TABLE w_record
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `user_id`                BIGINT NOT NULL COMMENT '用户id',
    `user_code`              BIGINT NOT NULL COMMENT '用户code',
    `username`               VARCHAR(32) COMMENT '用户名',
    `batch_id`               BIGINT COMMENT '批次id',
    `category_type_eum`      INT COMMENT '大类',
    `manufacturers_type_eum` INT COMMENT '厂商',
    `sub_type_eum`           INT COMMENT '小类-子游戏',
    `level_eum`              INT COMMENT '洗码时的等级',
    `wash_bet_amount`        DECIMAL(24, 6) COMMENT '洗码的打码金额',
    `wash_rate`              DECIMAL(24, 6) COMMENT '洗码时刻的洗码比例0.0001格式',
    `wash_amount`            DECIMAL(24, 6) COMMENT '洗码金额',
    `wash_bet_multiple`      DECIMAL(24, 6) COMMENT '洗码金额的需求打码倍数',
    PRIMARY KEY (id)
) COMMENT = '已洗码记录';


CREATE INDEX wr_p_idx ON w_record (platform_id);
CREATE INDEX wr_u_idx ON w_record (user_id, platform_id);
CREATE INDEX wr_u_type_time ON w_record (user_id, category_type_eum, manufacturers_type_eum, sub_type_eum, create_time,
                                         platform_id);
CREATE INDEX wr_b_idx ON w_record (batch_id, platform_id);

DROP TABLE IF EXISTS w_total_record;
CREATE TABLE w_total_record
(
    `id`                BIGINT         NOT NULL COMMENT '主键',
    `platform_id`       BIGINT         NOT NULL COMMENT '平台id',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT         NOT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    `user_id`           BIGINT         NOT NULL COMMENT '用户id',
    `user_code`         BIGINT         NOT NULL COMMENT '用户code',
    `username`          VARCHAR(32) COMMENT '用户名',
    `wash_total_amount` DECIMAL(24, 6) NOT NULL COMMENT '洗码总金额',
    `bet_total_amount`  DECIMAL(24, 6) NOT NULL COMMENT '总投注额',
    `last_wash_amount`  DECIMAL(24, 6) COMMENT '最后一次洗码金额',
    `last_wash_time`    BIGINT COMMENT '最后一次洗码时间',
    `last_bet_amount`   DECIMAL(24, 6) COMMENT '最后一次洗码投注金额',
    PRIMARY KEY (id)
) COMMENT = '已洗码总额记录';


CREATE INDEX wr_p_idx ON w_total_record (platform_id);
CREATE INDEX wr_u_idx ON w_total_record (user_id, platform_id);

DROP TABLE IF EXISTS w_batch;
CREATE TABLE w_batch
(
    `id`                BIGINT NOT NULL COMMENT '主键',
    `platform_id`       BIGINT NOT NULL COMMENT '平台id',
    `create_by`         VARCHAR(32) COMMENT '创建人',
    `create_time`       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(32) COMMENT '更新人',
    `update_time`       BIGINT COMMENT '更新时间',
    `user_id`           BIGINT NOT NULL COMMENT '用户id',
    `user_code`         BIGINT NOT NULL COMMENT '用户code',
    `username`          VARCHAR(32) COMMENT '用户名',
    `wash_total_amount` DECIMAL(24, 6) COMMENT '洗码总金额',
    `bet_total_amount`  DECIMAL(24, 6) COMMENT '总投注额',
    `wash_bet_multiple` DECIMAL(24, 6) COMMENT '洗码金额的需求打码倍数',
    PRIMARY KEY (id)
) COMMENT = '洗码批次';


CREATE INDEX wb_p_idx ON w_batch (platform_id);
CREATE INDEX wb_u_idx ON w_batch (user_id, platform_id);

