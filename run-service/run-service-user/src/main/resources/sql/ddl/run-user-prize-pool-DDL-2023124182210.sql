DROP TABLE IF EXISTS g_coin;
CREATE TABLE g_coin
(
    `id`          BIGINT NOT NULL COMMENT 'id主键',
    `platform_id` BIGINT COMMENT '平台id/租户id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `user_id`     BIGINT NOT NULL COMMENT '用户id',
    `user_code`   BIGINT NOT NULL COMMENT '用户code',
    `username`    VARCHAR(32) COMMENT '用户名',
    `total_coin`  DECIMAL(24, 6) COMMENT '用户当前总金币',
    `status_eum`  INT    NOT NULL COMMENT '状态',
    `remark`      VARCHAR(255) COMMENT '备注',
    PRIMARY KEY (id)
) COMMENT = '全球币';


CREATE INDEX gc_piuiuc_idx ON g_coin (platform_id, user_id, user_code);
CREATE INDEX gc_pise_idx ON g_coin (platform_id, status_eum);

DROP TABLE IF EXISTS g_coin_record;
CREATE TABLE g_coin_record
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `coin_source_type_eum`   INT    NOT NULL COMMENT '金币改变类型',
    `bet_amount`             DECIMAL(24, 6) COMMENT '打码金额',
    `exchange_ratio`         VARCHAR(255) COMMENT '转换比例',
    `change_coin`            DECIMAL(24, 6) COMMENT '本次所得币',
    `before_change_coin`     DECIMAL(24, 6) COMMENT '改变前货币',
    `after_change_coin`      DECIMAL(24, 6) COMMENT '改变后货币',
    `remark`                 VARCHAR(900) COMMENT '备注',
    `user_id`                BIGINT NOT NULL COMMENT '用户id',
    `user_code`              BIGINT NOT NULL COMMENT '用户code',
    `username`               VARCHAR(32) COMMENT '用户名',
    `source_id`              BIGINT COMMENT '来源id',
    `in_or_out_eum`          INT COMMENT '出入账标识',
    `balance_change_amount`  DECIMAL(24, 6) COMMENT '账本余额变动金额',
    `sub_type_eum`           INT COMMENT '小类-子游戏（游戏内奖池币快速兑换余额&上分',
    `coin_exchange_type_eum` INT COMMENT '全球币兑换类型',
    PRIMARY KEY (id)
) COMMENT = '全球币--改变记录';


CREATE INDEX gcr_pict_idx ON g_coin_record (platform_id, create_time);
CREATE INDEX gcr_piduid_idx ON g_coin_record (platform_id, user_id);
CREATE INDEX gcr_picsteiooe_idx ON g_coin_record (platform_id, coin_source_type_eum, in_or_out_eum);

DROP TABLE IF EXISTS g_coin_exchange_control;
CREATE TABLE g_coin_exchange_control
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `currency_name`          VARCHAR(90) COMMENT '币种名称',
    `currency_icon`          VARCHAR(255) COMMENT '币种图标',
    `exchange_limit`         DECIMAL(24, 6) COMMENT '每日兑换上限（额度为0时无法兑换，需要等待刷新）',
    `refresh_time`           BIGINT COMMENT '兑换额度刷新时间(0-23点)',
    `today_exchange`         DECIMAL(24, 6) COMMENT '当日已兑换',
    `exchange_ratio`         VARCHAR(255) COMMENT '全球币兑换余额比例（例如：100:1）',
    `bet_multiple`           DECIMAL(24, 6) COMMENT '打码倍数',
    `is_open_exchange`       TINYINT(1) COMMENT '是否开启游戏便捷兑换',
    `coin_exchange_type_eum` INT COMMENT '到账类型',
    `effective_days`         INT COMMENT '有效天数, 当到账类型为[TO_DISCOUNT_WALLET]的设置选项，非必填（默认：0）',
    PRIMARY KEY (id)
) COMMENT = '全球币--兑换控制';


CREATE INDEX gcc_pid_idx ON g_coin_exchange_control (platform_id);

DROP TABLE IF EXISTS g_coin_source_config;
CREATE TABLE g_coin_source_config
(
    `id`                   BIGINT NOT NULL COMMENT '主键',
    `platform_id`          BIGINT NOT NULL COMMENT '平台id',
    `create_by`            VARCHAR(32) COMMENT '创建人',
    `create_time`          BIGINT NOT NULL COMMENT '创建时间',
    `update_by`            VARCHAR(32) COMMENT '更新人',
    `update_time`          BIGINT COMMENT '更新时间',
    `coin_source_type_eum` INT    NOT NULL COMMENT '金币来源类型',
    `exchange_ratio`       VARCHAR(255) COMMENT '转换比例（获得类型为比例时必要；如配置3:2，则充值或者打码900块即可获得600个币）',
    `coin_get_type_eum`    LONGTEXT COMMENT '金币获得类型（固定数量、范围随机、按比例、按规则）',
    `get_rules`            VARCHAR(255) COMMENT '获取规则(json String)',
    `start_time`           VARCHAR(255) COMMENT '有效期（开始）',
    `end_time`             VARCHAR(255) COMMENT '有效期（结束）',
    PRIMARY KEY (id)
) COMMENT = '全球币--来源配置';


CREATE INDEX gcsc_picste_idx ON g_coin_source_config (platform_id, coin_source_type_eum);

DROP TABLE IF EXISTS pp_control;
CREATE TABLE pp_control
(
    `id`                       BIGINT NOT NULL COMMENT '主键',
    `platform_id`              BIGINT NOT NULL COMMENT '平台id',
    `create_by`                VARCHAR(32) COMMENT '创建人',
    `create_time`              BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                VARCHAR(32) COMMENT '更新人',
    `update_time`              BIGINT COMMENT '更新时间',
    `prize_pool_type_eum`      INT COMMENT '奖池类型',
    `bet_to_pool_rate`         DECIMAL(24, 6) COMMENT '打码进入奖池比例',
    `base_amount`              DECIMAL(24, 6) COMMENT '奖池起始金额;奖池基础金额',
    `global_coin_start`        DECIMAL(24, 6) COMMENT '投币下限;默认1',
    `global_coin_limit`        DECIMAL(24, 6) COMMENT '投币上限;默认0，无限制',
    `pool_open`                TINYINT(1) COMMENT '奖池是否开启',
    `robot_open`               TINYINT(1) COMMENT '机器人是否开启',
    `big_prize_to_real`        TINYINT(1) COMMENT '大奖是否给真人',
    `robot_count_percent`      DECIMAL(24, 6) COMMENT '机器人中奖人数占比',
    `robot_win_amount_percent` DECIMAL(24, 6) COMMENT '机器人中奖金额占比;若为0，则不需要机器人',
    `bet_multiple`             DECIMAL(24, 6) COMMENT '打码倍数',
    `icon_url`                 VARCHAR(255) COMMENT '客户端展示图片',
    `real_shortlist_count`     INT COMMENT '真人候选名单数',
    `target_count`             INT COMMENT '每期开奖达标真人数(不达标则100%机器人)',
    `robot_lower_limit`        INT COMMENT '外部机器人下限',
    `robot_limit`              INT COMMENT '外部机器人上限',
    `robot_in_prize`           TINYINT(1) COMMENT '机器人是否计入奖池',
    `min_robot_bet_amount`     DECIMAL(24, 6) COMMENT '机器人打码最小值',
    `max_robot_bet_amount`     DECIMAL(24, 6) COMMENT '机器人打码最大值',
    PRIMARY KEY (id)
) COMMENT = '奖池控制';

DROP TABLE IF EXISTS pp_item_control;
CREATE TABLE pp_item_control
(
    `id`                    BIGINT NOT NULL COMMENT '主键',
    `platform_id`           BIGINT NOT NULL COMMENT '平台id',
    `create_by`             VARCHAR(32) COMMENT '创建人',
    `create_time`           BIGINT NOT NULL COMMENT '创建时间',
    `update_by`             VARCHAR(32) COMMENT '更新人',
    `update_time`           BIGINT COMMENT '更新时间',
    `prize_pool_control_id` BIGINT COMMENT '奖池控制ID',
    `prize_pool_type_eum`   INT COMMENT '奖池类型枚举',
    `prize_rate`            DECIMAL(24, 6) COMMENT '奖金比例',
    PRIMARY KEY (id)
) COMMENT = '奖项控制';

DROP TABLE IF EXISTS pp_period;
CREATE TABLE pp_period
(
    `id`                   BIGINT NOT NULL COMMENT '主键',
    `platform_id`          BIGINT NOT NULL COMMENT '平台id',
    `create_by`            VARCHAR(32) COMMENT '创建人',
    `create_time`          BIGINT NOT NULL COMMENT '创建时间',
    `update_by`            VARCHAR(32) COMMENT '更新人',
    `update_time`          BIGINT COMMENT '更新时间',
    `period`               VARCHAR(90) COMMENT '期数',
    `pp_control_id`        BIGINT COMMENT '奖池控制ID',
    `prize_pool_type_eum`  INT COMMENT '奖池类型',
    `base_amount`          DECIMAL(24, 6) COMMENT '奖池起始金额',
    `prize_pool_amount`    DECIMAL(24, 6) COMMENT '奖池金额',
    `robot_item_rate_list` LONGTEXT COMMENT '机器人奖项列表',
    `real_item_rate_list`  LONGTEXT COMMENT '真人奖项列表',
    `put_in_start_time`    BIGINT COMMENT '奖池投币开始时间',
    `put_in_end_time`      BIGINT COMMENT '奖池投币结束时间',
    `status`               INT COMMENT '期数状态',
    `real_amount`          DECIMAL(24, 6) COMMENT '真人中奖总金额',
    `robot_amount`         DECIMAL(24, 6) COMMENT '机器人中奖总金额',
    `real_win_count`       INT COMMENT '真人中奖人数',
    `robot_win_rate`       DECIMAL(24, 6) COMMENT '机器人中奖占比',
    `real_join_count`      INT COMMENT '真人参与人数',
    `real_join_coin`       DECIMAL(24, 6) COMMENT '真人参与币数',
    `robot_count`          INT COMMENT '外部机器人数量',
    `robot_in_prize`       TINYINT(1) COMMENT '机器人是否计入奖池',
    `min_robot_bet_amount` DECIMAL(24, 6) COMMENT '机器人打码最小值',
    `max_robot_bet_amount` DECIMAL(24, 6) COMMENT '机器人打码最大值',
    `robot_join_amount`    DECIMAL(24, 6) COMMENT '机器人贡献金额',
    PRIMARY KEY (id)
) COMMENT = '奖池期数';


CREATE INDEX pp_p_idx ON pp_period (platform_id);
CREATE INDEX pp_pp_idx ON pp_period (platform_id, period);
CREATE INDEX pp_ppc_idx ON pp_period (platform_id, period, pp_control_id);

DROP TABLE IF EXISTS pp_period_detail;
CREATE TABLE pp_period_detail
(
    `id`               BIGINT NOT NULL COMMENT '主键',
    `platform_id`      BIGINT NOT NULL COMMENT '平台id',
    `create_by`        VARCHAR(32) COMMENT '创建人',
    `create_time`      BIGINT NOT NULL COMMENT '创建时间',
    `update_by`        VARCHAR(32) COMMENT '更新人',
    `update_time`      BIGINT COMMENT '更新时间',
    `period_id`        BIGINT COMMENT '期数ID',
    `realer`           TINYINT(1) COMMENT '是否真人',
    `user_id`          BIGINT NOT NULL COMMENT '用户id',
    `user_code`        BIGINT NOT NULL COMMENT '用户code',
    `username`         VARCHAR(32) COMMENT '用户名',
    `put_in_coin`      DECIMAL(24, 6) COMMENT '投入币数',
    `win_amount`       DECIMAL(24, 6) COMMENT '中奖金额',
    `robot_bet_amount` DECIMAL(24, 6) COMMENT '机器人打码金额',
    PRIMARY KEY (id)
) COMMENT = '奖池期数明细';


CREATE INDEX ppd_p_idx ON pp_period_detail (platform_id);
CREATE INDEX ppd_pp_idx ON pp_period_detail (platform_id, period_id);
CREATE INDEX ppd_pu_idx ON pp_period_detail (platform_id, user_id);

DROP TABLE IF EXISTS g_coin_platform_end_day_summary;
CREATE TABLE g_coin_platform_end_day_summary
(
    `id`                     BIGINT NOT NULL COMMENT '主键',
    `platform_id`            BIGINT NOT NULL COMMENT '平台id',
    `create_by`              VARCHAR(32) COMMENT '创建人',
    `create_time`            BIGINT NOT NULL COMMENT '创建时间',
    `update_by`              VARCHAR(32) COMMENT '更新人',
    `update_time`            BIGINT COMMENT '更新时间',
    `end_day_time`           VARCHAR(255) COMMENT '日结时间（年-月-日）',
    `total_coins`            DECIMAL(24, 6) COMMENT '账面剩余总币数',
    `limit_exchange_amount`  DECIMAL(24, 6) COMMENT '当日兑换上限额度',
    `actual_exchange_amount` DECIMAL(24, 6) COMMENT '当日实际兑换金额',
    PRIMARY KEY (id)
) COMMENT = '全球币-平台日结汇总';


CREATE INDEX gcpeds_piedt_idx ON g_coin_platform_end_day_summary (platform_id, end_day_time);

DROP TABLE IF EXISTS g_coin_platform_end_day_summary_control;
CREATE TABLE g_coin_platform_end_day_summary_control
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `start_time`  VARCHAR(255) COMMENT '开始日结时间（时:分:秒）',
    PRIMARY KEY (id)
) COMMENT = '全球币-平台日结汇总控制';


CREATE INDEX gcpedsc_pidst_idx ON g_coin_platform_end_day_summary_control (platform_id, start_time);

