DROP TABLE IF EXISTS at_activity;
CREATE TABLE at_activity
(
    `id`                              BIGINT NOT NULL COMMENT 'id主键',
    `label_id`                        BIGINT COMMENT '标签id-可以为空',
    `activity_type_eum`               INT    NOT NULL COMMENT '活动类型',
    `award_amount`                    DECIMAL(24, 6) COMMENT '活动奖励金额',
    `title`                           VARCHAR(255) COMMENT '活动标题',
    `min_picture`                     VARCHAR(255) COMMENT '活动小图地址-可用于左侧等小图标地方',
    `h5_picture`                      VARCHAR(255) COMMENT '活动app/h5图地址',
    `pc_picture`                      VARCHAR(255) COMMENT '活动pc图地址',
    `home_window_picture`             VARCHAR(255) COMMENT '活动首页弹窗图地址',
    `click_show_texter`               TINYINT(1) COMMENT '点击是否显示富文本',
    `text`                            TEXT COMMENT '活动描述(富文本)-clickShowTexter为true使用',
    `click_jump_url`                  VARCHAR(255) COMMENT '点击跳转地址-clickShowTexter为false使用',
    `is_side`                         TINYINT(1) COMMENT '是否在客户端侧边栏展示',
    `is_home_window`                  TINYINT(1) COMMENT '是否在客户端首页弹窗展示',
    `must_recharger`                  TINYINT(1) COMMENT '是否必须充值',
    `must_recharge_amount`            DECIMAL(24, 6) COMMENT '必须充值金额',
    `must_beter`                      TINYINT(1) COMMENT '是否必须投注',
    `must_bet_amount`                 DECIMAL(24, 6) COMMENT '必须投注金额',
    `must_bind_phoner`                TINYINT(1) COMMENT '必须绑定手机',
    `must_bind_withdrawer`            TINYINT(1) COMMENT '必须绑定提款账号',
    `vip_limiter`                     TINYINT(1) COMMENT '是否限制指定vip可参与活动',
    `level_eum_list`                  VARCHAR(255) COMMENT '可参与活动等级枚举集合',
    `hierarchical_limiter`            TINYINT(1) COMMENT '是否限制指定层级可参与活动',
    `entry_activity_hierarchical_ids` VARCHAR(255) COMMENT '可参与活动层级id集合',
    `game_limiter`                    TINYINT(1) COMMENT '是否限制指定游戏可参与活动',
    `game_sub_type_eum_list`          TEXT COMMENT '可参与活动的游戏小类枚举集合-页面选择游戏大类或者游戏厂商的时候，级联查询出子游戏的集合传递存储',
    `needer`                          TINYINT(1) COMMENT '是否需要报名',
    `apply_num_type_eum`              INT COMMENT '活动申请类型',
    `apply_param`                     VARCHAR(1000) COMMENT '活动申请参数',
    `autoer`                          TINYINT(1) COMMENT '是否自动派发',
    `auto_settle`                     TINYINT(1) COMMENT '是否后台自动结算',
    `bet_multiple`                    DECIMAL(24, 6) COMMENT '赠送金额的打码倍数',
    `continuous_recharge_switch`      INT COMMENT '签到多少天内是否充值开关',
    `activity_rule`                   VARCHAR(255) COMMENT '活动规则文案',
    `start_time`                      BIGINT COMMENT '开始时间',
    `end_time`                        BIGINT COMMENT '结束时间',
    `sort`                            INT COMMENT '排序',
    `status_eum`                      INT    NOT NULL COMMENT '状态',
    `remark`                          VARCHAR(255) COMMENT '活动备注',
    `platform_id`                     BIGINT COMMENT '平台id/租户id',
    `create_by`                       VARCHAR(32) COMMENT '创建人',
    `create_time`                     BIGINT COMMENT '创建时间',
    `update_by`                       VARCHAR(32) COMMENT '更新人',
    `update_time`                     BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动';


CREATE INDEX at_type_uni_idx ON at_activity (platform_id, activity_type_eum);
CREATE INDEX at_pid_uni_idx ON at_activity (platform_id);
CREATE INDEX at_status_uni_idx ON at_activity (platform_id, status_eum);
CREATE INDEX at_crt_uni_idx ON at_activity (platform_id, create_time);
CREATE INDEX at_lb_uni_idx ON at_activity (platform_id, label_id);

DROP TABLE IF EXISTS at_entry_record;
CREATE TABLE at_entry_record
(
    `id`                        BIGINT NOT NULL COMMENT 'id主键',
    `parent_id`                 BIGINT COMMENT '活动id',
    `title`                     VARCHAR(255) COMMENT '活动标题',
    `apply_status`              INT COMMENT '处理状态',
    `apply_date`                VARCHAR(255) COMMENT '申请日期',
    `apply_param`               VARCHAR(1000) COMMENT '活动申请参数',
    `remark`                    VARCHAR(90) COMMENT '备注',
    `lock_backstage_account_id` BIGINT COMMENT '锁定的后台账号id',
    `user_id`                   BIGINT COMMENT '用户id',
    `user_code`                 BIGINT COMMENT '用户code',
    `username`                  VARCHAR(32) COMMENT '用户名',
    `platform_id`               BIGINT COMMENT '平台id/租户id',
    `create_by`                 VARCHAR(32) COMMENT '创建人',
    `create_time`               BIGINT COMMENT '创建时间',
    `update_by`                 VARCHAR(32) COMMENT '更新人',
    `update_time`               BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (id, update_time)
) COMMENT = '活动申请记录';


CREATE INDEX ae_auid_uni_idx ON at_entry_record (user_id, platform_id);
CREATE INDEX ae_status_uni_idx ON at_entry_record (apply_status, platform_id);
CREATE INDEX ae_pid_uni_idx ON at_entry_record (platform_id);
CREATE INDEX ae_crt_uni_idx ON at_entry_record (platform_id, create_time);

DROP TABLE IF EXISTS at_achieve_goals_record;
CREATE TABLE at_achieve_goals_record
(
    `id`                       BIGINT NOT NULL COMMENT 'id主键',
    `entry_record_id`          BIGINT COMMENT '活动申请记录id，可为空',
    `activity_id`              BIGINT COMMENT '活动id',
    `item_id`                  BIGINT COMMENT '明细Id',
    `title`                    VARCHAR(255) COMMENT '活动标题',
    `activity_type_eum`        INT COMMENT '活动类型',
    `bind_phone`               TINYINT(1) COMMENT '是否绑定手机',
    `bind_withdraw_card`       TINYINT(1) COMMENT '是否绑定提款账号',
    `current_level`            INT COMMENT '当前等级',
    `current_hierarchy_name`   VARCHAR(255) COMMENT '当前层级',
    `user_sum_recharge_amount` DECIMAL(24, 6) COMMENT '该用户活动已完成充值金额',
    `user_sum_bet_amount`      DECIMAL(24, 6) COMMENT '该用户活动已完成打码金额',
    `bet_multiple`             DECIMAL(24, 6) COMMENT '赠送金额的打码倍数',
    `remark`                   VARCHAR(255) COMMENT '备注',
    `status_type_eum`          INT COMMENT '状态',
    `user_id`                  BIGINT COMMENT '用户id',
    `user_code`                BIGINT COMMENT '用户code',
    `username`                 VARCHAR(32) COMMENT '用户名',
    `platform_id`              BIGINT COMMENT '平台id/租户id',
    `create_time`              BIGINT COMMENT '创建时间',
    `create_by`                VARCHAR(32) COMMENT '创建人',
    `update_by`                VARCHAR(90) COMMENT '更新人',
    `update_time`              VARCHAR(32) COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动达标记录-手动派发的活动需要传递用户id生成达标记录';


CREATE INDEX ag_userid_uni_idx ON at_achieve_goals_record (user_id, platform_id);
CREATE INDEX ag_pid_uni_idx ON at_achieve_goals_record (platform_id);
CREATE INDEX ag_crt_uni_idx ON at_achieve_goals_record (platform_id, create_time);

DROP TABLE IF EXISTS at_distribute_record;
CREATE TABLE at_distribute_record
(
    `id`                      BIGINT NOT NULL COMMENT 'id主键',
    `achieve_goals_record_id` BIGINT COMMENT '达标记录id',
    `activity_id`             BIGINT COMMENT '活动id',
    `activity_type_eum`       INT COMMENT '活动类型',
    `title`                   VARCHAR(255) COMMENT '标题',
    `item_id`                 BIGINT COMMENT '明细id',
    `pay_amount`              DECIMAL(24, 6) COMMENT '派发金额',
    `ext_info`                VARCHAR(900) COMMENT '活动扩展信息,json格式',
    `user_id`                 BIGINT COMMENT '用户id',
    `user_code`               BIGINT COMMENT '用户code',
    `username`                VARCHAR(32) COMMENT '用户名',
    `platform_id`             BIGINT COMMENT '平台id/租户id',
    `create_by`               VARCHAR(32) COMMENT '创建人',
    `create_time`             BIGINT COMMENT '创建时间',
    `update_by`               VARCHAR(32) COMMENT '更新人',
    `update_time`             BIGINT COMMENT '更新时间',
    `frequency`               INT COMMENT '周期红包投放次数',
    PRIMARY KEY (id)
) COMMENT = '活动增加金额的派发记录';


CREATE INDEX ad_aid_uni_idx ON at_distribute_record (activity_id, platform_id);
CREATE INDEX ad_iid_uni_idx ON at_distribute_record (item_id, platform_id);
CREATE INDEX adr_piuif_idx ON at_distribute_record (platform_id, user_id, frequency);
CREATE INDEX ad_crt_uni_idx ON at_distribute_record (platform_id, create_time);
CREATE INDEX ad_pid_uni_idx ON at_distribute_record (platform_id);
CREATE INDEX ad_type_uni_idx ON at_distribute_record (activity_type_eum, platform_id);

DROP TABLE IF EXISTS at_label;
CREATE TABLE at_label
(
    `id`            BIGINT NOT NULL COMMENT 'id主键',
    `label_name`    VARCHAR(32) COMMENT '标签名',
    `label_img_url` VARCHAR(255) COMMENT '标签图地址',
    `sort`          INT COMMENT '排序',
    `status_eum`    INT COMMENT '状态',
    `platform_id`   BIGINT COMMENT '平台id/租户id',
    `create_by`     VARCHAR(32) COMMENT '创建人',
    `create_time`   BIGINT COMMENT '创建时间',
    `update_by`     VARCHAR(32) COMMENT '更新人',
    `update_time`   BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动标签表-用以归类客户端展示';


CREATE INDEX al_pid_uni_idx ON at_label (platform_id);
CREATE INDEX al_crt_uni_idx ON at_label (platform_id, create_time);

DROP TABLE IF EXISTS at_egg_item;
CREATE TABLE at_egg_item
(
    `id`           BIGINT NOT NULL COMMENT 'id主键',
    `activity_id`  BIGINT NOT NULL COMMENT '活动id',
    `award_type`   INT COMMENT '奖品类型',
    `award_icon`   VARCHAR(255) COMMENT '奖品图标',
    `real_award`   VARCHAR(90) COMMENT '实物奖品',
    `award_amount` DECIMAL(24, 6) COMMENT '奖金',
    `award_rate`   DECIMAL(24, 6) COMMENT '中奖概率',
    `is_real`      TINYINT(1) COMMENT '是否实物',
    `award_num`    INT COMMENT '奖品数量',
    `award_desc`   VARCHAR(255) COMMENT '中奖提示',
    `platform_id`  BIGINT COMMENT '平台id/租户id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动幸运金蛋明细';


CREATE INDEX aeg_aid_uni_idx ON at_egg_item (activity_id, platform_id);
CREATE INDEX aeg_type_uni_idx ON at_egg_item (award_type, platform_id);
CREATE INDEX aeg_crt_uni_idx ON at_egg_item (platform_id, create_time);
CREATE INDEX aeg_pid_uni_idx ON at_egg_item (platform_id);

DROP TABLE IF EXISTS at_first_recharge_login_item;
CREATE TABLE at_first_recharge_login_item
(
    `id`           BIGINT NOT NULL COMMENT 'id主键',
    `activity_id`  BIGINT COMMENT '活动id',
    `sequence`     INT COMMENT '第几天',
    `award_amount` DECIMAL(24, 6) COMMENT '奖励金额',
    `bet_multiple` DECIMAL(24, 6) COMMENT '打码倍数',
    `platform_id`  BIGINT COMMENT '平台id/租户id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '首充登录活动奖励明细';


CREATE INDEX fr_pid_idx ON at_first_recharge_login_item (platform_id);

DROP TABLE IF EXISTS at_red_item;
CREATE TABLE at_red_item
(
    `id`                    BIGINT NOT NULL COMMENT 'id主键',
    `activity_id`           BIGINT NOT NULL COMMENT '活动id',
    `title`                 VARCHAR(90) COMMENT '红包标题',
    `red_type`              INT COMMENT '红包类型',
    `timing_send_time`      BIGINT COMMENT '定时发放时间',
    `amount`                DECIMAL(24, 6) COMMENT '红包金额',
    `red_num`               INT COMMENT '红包数量',
    `integerer`             TINYINT(1) COMMENT '是否整数金额',
    `appoint_users`         VARCHAR(255) COMMENT '指定用户集合(用户code)',
    `bet_multiple`          DECIMAL(24, 6) COMMENT '打码倍数',
    `remark`                VARCHAR(255) COMMENT '备注',
    `platform_id`           BIGINT COMMENT '平台id/租户id',
    `create_by`             VARCHAR(32) COMMENT '创建人',
    `create_time`           BIGINT COMMENT '创建时间',
    `update_by`             VARCHAR(32) COMMENT '更新人',
    `update_time`           BIGINT COMMENT '更新时间',
    `send_type`             INT COMMENT '红包投放类型',
    `interval_time`         INT COMMENT '间隔时间',
    `interval_unit`         INT COMMENT '间隔单位',
    `begin_time`            BIGINT COMMENT '开始时间点 -- 当投放类型为' 周期发放 '该字段必填;当投放类型为' 周期发放 '该字段必填',
    `end_time`              BIGINT COMMENT '结束时间点 -- 当投放类型为' 周期发放 '该字段必填;当投放类型为' 周期发放 '该字段必填',
    `send_time`             INT COMMENT '发放时间点 -- 当投放类型为' 周期发放 '且间隔单位为' 天 '时该字段必填，范围[0-23]',
    `next_send_time`        BIGINT COMMENT '下次发放时间（自动生成）',
    `status_eum`            INT COMMENT '状态',
    `must_recharger`        TINYINT(1) COMMENT '是否必须充值',
    `must_recharger_amount` DECIMAL(24, 6) COMMENT '必须充值累计金额',
    `must_beter`            TINYINT(1) COMMENT '是否必须投注',
    `must_bet_amount`       DECIMAL(24, 6) COMMENT '必须投注金额',
    `frequency`             INT COMMENT '周期红包投放次数（自动生成）',
    `display_time`          INT COMMENT '红包持续显示时间(单位：分钟)',
    PRIMARY KEY (id)
) COMMENT = '活动红包明细';


CREATE INDEX ar_aid_uni_idx ON at_red_item (activity_id, platform_id);
CREATE INDEX ar_type_uni_idx ON at_red_item (red_type, platform_id);
CREATE INDEX ar_pid_uni_idx ON at_red_item (platform_id);
CREATE INDEX ar_crt_uni_idx ON at_red_item (platform_id, create_time);

DROP TABLE IF EXISTS at_return_item;
CREATE TABLE at_return_item
(
    `id`          BIGINT NOT NULL COMMENT 'id主键',
    `activity_id` BIGINT NOT NULL COMMENT '活动id',
    `level_eum`   INT    NOT NULL COMMENT '等级枚举',
    `need_days`   INT COMMENT '触发回归的天数',
    `day1_amount` DECIMAL(24, 6) COMMENT '回归第1天签到金币',
    `day2_amount` DECIMAL(24, 6) COMMENT '回归第2天签到金币',
    `day3_amount` DECIMAL(24, 6) COMMENT '回归第3天签到金币',
    `day4_amount` DECIMAL(24, 6) COMMENT '回归第4天签到金币',
    `day5_amount` DECIMAL(24, 6) COMMENT '回归第5天签到金币',
    `day6_amount` DECIMAL(24, 6) COMMENT '回归第6天签到金币',
    `day7_amount` DECIMAL(24, 6) COMMENT '回归第7天签到金币',
    `platform_id` BIGINT COMMENT '平台id/租户id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动回归活动明细表';


CREATE INDEX are_aid_uni_idx ON at_return_item (activity_id, platform_id);
CREATE INDEX are_pid_uni_idx ON at_return_item (platform_id);
CREATE INDEX are_crt_uni_idx ON at_return_item (platform_id, create_time);
CREATE INDEX are_lev_uni_idx ON at_return_item (level_eum, platform_id);

DROP TABLE IF EXISTS at_sign_item;
CREATE TABLE at_sign_item
(
    `id`          BIGINT NOT NULL COMMENT 'id主键',
    `activity_id` BIGINT NOT NULL COMMENT '活动id',
    `level_eum`   INT    NOT NULL COMMENT '等级枚举',
    `day1_amount` DECIMAL(24, 6) COMMENT '第1天签到金额',
    `day2_amount` DECIMAL(24, 6) COMMENT '第2天签到金额',
    `day3_amount` DECIMAL(24, 6) COMMENT '第3天签到金额',
    `day4_amount` DECIMAL(24, 6) COMMENT '第4天签到金额',
    `day5_amount` DECIMAL(24, 6) COMMENT '第5天签到金额',
    `day6_amount` DECIMAL(24, 6) COMMENT '第6天签到金额',
    `day7_amount` DECIMAL(24, 6) COMMENT '第7天签到金额',
    `platform_id` BIGINT COMMENT '平台id/租户id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动签到明细表';


CREATE INDEX as_aid_uni_idx ON at_sign_item (platform_id, activity_id);
CREATE INDEX as_pid_uni_idx ON at_sign_item (platform_id);
CREATE INDEX as_crt_uni_idx ON at_sign_item (platform_id, create_time);

DROP TABLE IF EXISTS at_turn_plate_item;
CREATE TABLE at_turn_plate_item
(
    `id`           BIGINT NOT NULL COMMENT 'id主键',
    `activity_id`  BIGINT NOT NULL COMMENT '活动id',
    `award_type`   INT COMMENT '奖品类型',
    `award_icon`   VARCHAR(255) COMMENT '奖品图标',
    `real_award`   VARCHAR(32) COMMENT '实物奖品',
    `award_amount` DECIMAL(24, 6) COMMENT '奖金',
    `award_rate`   DECIMAL(24, 6) COMMENT '中奖概率',
    `is_real`      TINYINT(1) COMMENT '是否实物',
    `award_num`    INT COMMENT '奖品数量',
    `award_desc`   VARCHAR(900) COMMENT '中奖提示',
    `platform_id`  BIGINT COMMENT '平台id/租户id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT COMMENT '创建时间',
    `update_time`  BIGINT COMMENT '更新时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    PRIMARY KEY (id)
) COMMENT = '活动转盘明细表';


CREATE INDEX ats_aid_uni_idx ON at_turn_plate_item (activity_id, platform_id);
CREATE INDEX ats_type_uni_idx ON at_turn_plate_item (award_type, platform_id);
CREATE INDEX ats_pid_uni_idx ON at_turn_plate_item (platform_id);
CREATE INDEX ats_crt_uni_idx ON at_turn_plate_item (platform_id, create_time);

DROP TABLE IF EXISTS at_turn_plate_control;
CREATE TABLE at_turn_plate_control
(
    `id`                            BIGINT NOT NULL COMMENT 'id主键',
    `must_gold_recharge_amount`     DECIMAL(24, 6) COMMENT '金转盘必须充值金额',
    `must_gold_bet_amount`          DECIMAL(24, 6) COMMENT '金转盘必须投注金额',
    `gold_rules`                    TEXT COMMENT '金转盘规则描述',
    `must_platinum_recharge_amount` DECIMAL(24, 6) COMMENT '白金转盘必须充值金额',
    `must_platinum_bet_amount`      DECIMAL(24, 6) COMMENT '白金转盘必须投注金额',
    `platinum_rules`                TEXT COMMENT '白金转盘规则描述',
    `must_diamond_recharge_amount`  DECIMAL(24, 6) COMMENT '钻石转盘必须充值金额',
    `must_diamond_bet_amount`       DECIMAL(24, 6) COMMENT '钻石转盘必须投注金额',
    `diamond_rules`                 TEXT COMMENT '钻石转盘规则描述',
    `platform_id`                   BIGINT COMMENT '平台id/租户id',
    `create_by`                     VARCHAR(32) COMMENT '创建人',
    `create_time`                   BIGINT COMMENT '创建时间',
    `update_time`                   BIGINT COMMENT '更新时间',
    `update_by`                     VARCHAR(32) COMMENT '更新人',
    `gold_vip_rules`                LONGTEXT COMMENT '黄金权益规则次数：json格式',
    `platina_vip_rules`             LONGTEXT COMMENT '白金权益规则次数：json格式',
    `diamond_vip_rules`             LONGTEXT COMMENT '钻石权益规则次数：json格式',
    PRIMARY KEY (id)
) COMMENT = '活动转盘控制表';


CREATE INDEX atc_pid_uni_idx ON at_turn_plate_control (platform_id);
CREATE INDEX atc_crt_uni_idx ON at_turn_plate_control (platform_id, create_time);

DROP TABLE IF EXISTS at_egg_control;
CREATE TABLE at_egg_control
(
    `id`                            BIGINT NOT NULL COMMENT 'id主键',
    `must_gold_recharge_amount`     DECIMAL(24, 6) COMMENT '金蛋必须充值金额',
    `must_gold_bet_amount`          DECIMAL(24, 6) COMMENT '金蛋必须投注金额',
    `gold_rules`                    TEXT COMMENT '金蛋规则描述',
    `must_platinum_recharge_amount` DECIMAL(24, 6) COMMENT '银蛋必须充值金额',
    `must_platinum_bet_amount`      DECIMAL(24, 6) COMMENT '银蛋必须投注金额',
    `platinum_rules`                TEXT COMMENT '银蛋规则描述',
    `must_copper_recharge_amount`   DECIMAL(24, 6) COMMENT '铜蛋必须充值金额',
    `must_copper_bet_amount`        DECIMAL(24, 6) COMMENT '铜蛋必须投注金额',
    `copper_rules`                  TEXT COMMENT '铜蛋规则描述',
    `platform_id`                   BIGINT COMMENT '平台id/租户id',
    `create_by`                     VARCHAR(32) COMMENT '创建人',
    `create_time`                   BIGINT COMMENT '创建时间',
    `update_time`                   BIGINT COMMENT '更新时间',
    `update_by`                     VARCHAR(32) COMMENT '更新人',
    `goal_vip_rules`                LONGTEXT COMMENT '金蛋权益规则次数：json格式 对每个档次区间可设定每天可转的次数上限，可根据VIP等级段配置',
    `platinum_vip_rules`            LONGTEXT COMMENT '银蛋权益规则次数',
    `copper_vip_rules`              LONGTEXT COMMENT '铜蛋权益规则次数',
    PRIMARY KEY (id)
) COMMENT = '幸运金蛋控制表';


CREATE INDEX aec_pid_uni_idx ON at_egg_control (platform_id);
CREATE INDEX aec_crt_uni_idx ON at_egg_control (platform_id, create_time);

DROP TABLE IF EXISTS at_red_packet_pick_up_record;
CREATE TABLE at_red_packet_pick_up_record
(
    `id`           BIGINT NOT NULL COMMENT '主键',
    `platform_id`  BIGINT NOT NULL COMMENT '平台id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    `red_id`       BIGINT COMMENT '红包ID',
    `title`        VARCHAR(255) COMMENT '标题',
    `amount`       DECIMAL(24, 6) COMMENT '领取金额',
    `user_id`      BIGINT COMMENT '用户id',
    `user_code`    BIGINT COMMENT '用户code',
    `username`     VARCHAR(255) COMMENT '用户名称',
    `red_type`     INT COMMENT '红包类型',
    `bet_multiple` DECIMAL(24, 6) COMMENT '打码倍数',
    `frequency`    INT COMMENT '周期/定时红包投放期数（自动生成）',
    PRIMARY KEY (id)
) COMMENT = '红包领取记录';


CREATE INDEX arppur_idx ON at_red_packet_pick_up_record (platform_id);
CREATE INDEX arppur_piri_idx ON at_red_packet_pick_up_record (platform_id, red_id);
CREATE INDEX arppur_puf_idx ON at_red_packet_pick_up_record (platform_id, user_id, frequency);

DROP TABLE IF EXISTS at_red_packet;
CREATE TABLE at_red_packet
(
    `title`                 VARCHAR(90) COMMENT '红包标题',
    `red_type`              INT COMMENT '红包类型',
    `amount`                DECIMAL(24, 6) COMMENT '红包总金额',
    `red_num`               INT COMMENT '红包数量',
    `integerer`             TINYINT(1) COMMENT '是否整数金额',
    `appoint_users`         VARCHAR(255) COMMENT '指定用户集合(用户code)',
    `bet_multiple`          DECIMAL(24, 6) COMMENT '打码倍数',
    `begin_time`            BIGINT COMMENT '领取开始时间',
    `end_time`              BIGINT COMMENT '领取结束时间',
    `must_recharger`        TINYINT(1) COMMENT '是否必须充值',
    `must_recharger_amount` DECIMAL(24, 6) COMMENT '必须充值累计金额',
    `must_beter`            TINYINT(1) COMMENT '是否必须投注',
    `must_bet_amount`       DECIMAL(24, 6) COMMENT '必须投注金额',
    `must_bind_bank_card`   TINYINT(1) COMMENT '需要绑定银行卡',
    `must_bind_phone`       TINYINT(1) COMMENT '需要绑定手机号',
    `id`                    BIGINT NOT NULL COMMENT '主键',
    `platform_id`           BIGINT NOT NULL COMMENT '平台id',
    `create_by`             VARCHAR(32) COMMENT '创建人',
    `create_time`           BIGINT NOT NULL COMMENT '创建时间',
    `update_by`             VARCHAR(32) COMMENT '更新人',
    `update_time`           BIGINT COMMENT '更新时间',
    `single_red_amount`     DECIMAL(24, 6) COMMENT '单个红包金额',
    `lowest_level`          INT COMMENT '参与会员VIP等级（最低等级）',
    `highest_level`         INT COMMENT '参与会员VIP等级（最高等级）',
    `hierarchical_id`       BIGINT COMMENT '参与会员层级id',
    `hierarchical_name`     VARCHAR(255) COMMENT '参与会员层级名称',
    `min_amount`            DECIMAL(24, 6) COMMENT '最小金额',
    `max_amount`            DECIMAL(24, 6) COMMENT '最大金额',
    `remark`                VARCHAR(255) COMMENT '备注',
    `send_type`             INT COMMENT '红包投放类型',
    `interval_time`         INT COMMENT '间隔时间 -- 定时红包时此字段必填',
    `interval_unit`         INT COMMENT '间隔单位 -- 定时红包时此字段必填',
    `star_point_time`       INT COMMENT '开始时间点',
    `end_point_time`        INT COMMENT '结束时间点',
    `effective_time`        INT COMMENT '红包有效期(单位：分钟)',
    `next_send_time`        BIGINT COMMENT '下次发放时间（自动生成）',
    `frequency`             INT COMMENT '周期/定时红包投放期数（自动生成）',
    `status_eum`            INT COMMENT '状态',
    PRIMARY KEY (id)
) COMMENT = '红包';


CREATE INDEX arp_pid_idx ON at_red_packet (platform_id);
CREATE INDEX arp_bep_idx ON at_red_packet (begin_time, end_time, platform_id);
CREATE INDEX arp_prsb_idx ON at_red_packet (platform_id, send_type, status_eum, begin_time, end_time);
CREATE INDEX arp_pssee_idx ON at_red_packet (platform_id, send_type, status_eum, effective_time, end_time);

DROP TABLE IF EXISTS at_red_packet_send_record;
CREATE TABLE at_red_packet_send_record
(
    `id`           BIGINT NOT NULL COMMENT '主键',
    `platform_id`  BIGINT NOT NULL COMMENT '平台id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    `red_id`       BIGINT COMMENT '红包ID',
    `title`        VARCHAR(90) COMMENT '红包标题',
    `red_type`     INT COMMENT '红包类型',
    `total_amount` DECIMAL(24, 6) COMMENT '红包红包总金额',
    `red_num`      INT COMMENT '红包数量',
    `send_time`    BIGINT COMMENT '发放时间',
    PRIMARY KEY (id)
) COMMENT = '红包发放记录表';


CREATE INDEX arpsr_piri_idx ON at_red_packet_send_record (platform_id, red_id);
CREATE INDEX arpsr_pid_idx ON at_red_packet_send_record (platform_id);

DROP TABLE IF EXISTS at_welfare_code;
CREATE TABLE at_welfare_code
(
    `id`                       BIGINT         NOT NULL COMMENT '主键',
    `welfare_code`             VARCHAR(32)    NOT NULL COMMENT '福利码',
    `amount_remark`            VARCHAR(255) COMMENT '金额备注',
    `quantity`                 BIGINT         NOT NULL COMMENT '数量',
    `recharge_limit_type_eum`  INT            NOT NULL COMMENT '福利码充值金额限制类型',
    `recharge_limit`           DECIMAL(24, 6) NOT NULL DEFAULT 0 COMMENT '充值金额限制',
    `start_time`               BIGINT         NOT NULL COMMENT '生效时间',
    `expire_time`              BIGINT         NOT NULL COMMENT '过期时间',
    `bet_multiple`             DECIMAL(24, 6) NOT NULL COMMENT '打码倍数',
    `platform_id`              BIGINT         NOT NULL COMMENT '平台id',
    `create_by`                VARCHAR(32) COMMENT '创建人',
    `create_time`              BIGINT         NOT NULL COMMENT '创建时间',
    `update_by`                VARCHAR(32) COMMENT '更新人',
    `update_time`              BIGINT COMMENT '更新时间',
    `bet_limit_type_eum`       INT            NOT NULL COMMENT '福利码打码金额限制类型',
    `bet_limit`                DECIMAL(24, 6) NOT NULL DEFAULT 0 COMMENT '打码金额限制',
    `book_record_category_eum` INT COMMENT '到账类型',
    `valid_date`               INT COMMENT '优惠钱包有效天数',
    PRIMARY KEY (id)
) COMMENT = '福利码';


CREATE INDEX wc_p_idx ON at_welfare_code (platform_id);
CREATE INDEX wc_pc_idx ON at_welfare_code (platform_id, create_time);
CREATE INDEX wc_pcw_idx ON at_welfare_code (platform_id, create_time, welfare_code);

DROP TABLE IF EXISTS at_welfare_code_detail;
CREATE TABLE at_welfare_code_detail
(
    `id`                  BIGINT         NOT NULL COMMENT '主键',
    `platform_id`         BIGINT         NOT NULL COMMENT '平台id',
    `create_by`           VARCHAR(32) COMMENT '创建人',
    `create_time`         BIGINT         NOT NULL COMMENT '创建时间',
    `update_by`           VARCHAR(32) COMMENT '更新人',
    `update_time`         BIGINT COMMENT '更新时间',
    `welfare_code_id`     BIGINT         NOT NULL COMMENT '福利码ID',
    `min_level_limit`     INT            NOT NULL COMMENT '最小等级限制',
    `max_level_limit`     INT            NOT NULL COMMENT '最大等级限制',
    `min_exchange_amount` DECIMAL(24, 6) NOT NULL COMMENT '区间兑换金额最小值',
    `max_exchange_amount` DECIMAL(24, 6) NOT NULL COMMENT '区间兑换金额最大值',
    `detail_quantity`     BIGINT COMMENT '具体数量',
    PRIMARY KEY (id)
) COMMENT = '福利码明细';


CREATE INDEX wcd_pw_idx ON at_welfare_code_detail (platform_id, welfare_code_id);

DROP TABLE IF EXISTS at_welfare_code_exchange_record;
CREATE TABLE at_welfare_code_exchange_record
(
    `id`              BIGINT         NOT NULL COMMENT '主键',
    `welfare_code_id` BIGINT         NOT NULL COMMENT '福利码ID',
    `exchange_amount` DECIMAL(24, 6) NOT NULL COMMENT '兑换金额',
    `exchange_time`   BIGINT         NOT NULL COMMENT '兑换时间',
    `level_eum`       INT COMMENT '兑换时等级',
    `user_id`         BIGINT COMMENT '用户id',
    `user_code`       BIGINT COMMENT '用户code',
    `username`        VARCHAR(255) COMMENT '用户名称',
    `platform_id`     BIGINT         NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT         NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '福利码兑换记录';


CREATE INDEX wcer_p_idx ON at_welfare_code_exchange_record (platform_id);
CREATE INDEX wcer_pc_idx ON at_welfare_code_exchange_record (platform_id, create_time);
CREATE INDEX wcer_pcwu_idx ON at_welfare_code_exchange_record (platform_id, create_time, welfare_code_id, user_id);

DROP TABLE IF EXISTS at_goals_amount_item;
CREATE TABLE at_goals_amount_item
(
    `id`              BIGINT NOT NULL COMMENT 'id主键',
    `activity_id`     BIGINT COMMENT '活动id',
    `recharge_amount` DECIMAL(24, 6) COMMENT '需要完成的充值金额',
    `bet_amount`      DECIMAL(24, 6) COMMENT '需要完成的投注金额',
    `pay_amount`      DECIMAL(24, 6) COMMENT '奖励金额',
    `bet_multiple`    DECIMAL(24, 6) COMMENT '赠送金额的打码倍数',
    `platform_id`     BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '活动目标区间金额明细';


CREATE INDEX agi_pid_idx ON at_goals_amount_item (platform_id);
CREATE INDEX agi_crt_idx ON at_goals_amount_item (platform_id, create_time);
CREATE INDEX agi_aid_idx ON at_goals_amount_item (activity_id, platform_id);

DROP TABLE IF EXISTS at_goals_item_entry_record;
CREATE TABLE at_goals_item_entry_record
(
    `id`                       BIGINT NOT NULL COMMENT 'id主键',
    `activity_id`              BIGINT COMMENT '活动id',
    `complete_recharge_amount` DECIMAL(24, 6) COMMENT '完成的充值金额',
    `complete_bet_amount`      DECIMAL(24, 6) COMMENT '完成的投注金额',
    `entry_date`               VARCHAR(255) COMMENT '参与日期',
    `user_id`                  BIGINT COMMENT '用户id',
    `user_code`                BIGINT COMMENT '用户code',
    `username`                 VARCHAR(32) COMMENT '用户名',
    `platform_id`              BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                VARCHAR(32) COMMENT '创建人',
    `create_time`              BIGINT COMMENT '创建时间',
    `update_by`                VARCHAR(32) COMMENT '更新人',
    `update_time`              BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '目标区间活动参与记录';


CREATE INDEX agr_pid_idx ON at_goals_item_entry_record (platform_id);
CREATE INDEX agr_crt_idx ON at_goals_item_entry_record (platform_id, create_time);
CREATE INDEX agr_aid_idx ON at_goals_item_entry_record (activity_id, user_id, entry_date, platform_id);

DROP TABLE IF EXISTS at_download_gift_control;
CREATE TABLE at_download_gift_control
(
    `id`                           BIGINT NOT NULL COMMENT 'id主键',
    `app_name`                     VARCHAR(255) COMMENT '应用下载名',
    `terminal_type_eum_list`       VARCHAR(255) COMMENT '设置可参与的终端',
    `parent_id`                    BIGINT COMMENT '必须是该推广代理下属',
    `ip_limit`                     INT COMMENT '同ip会员限制数量',
    `device_limit`                 INT COMMENT '同ip限制设备数量',
    `need_history_recharge_amount` DECIMAL(24, 6) COMMENT '领奖需要满足历史充值额',
    `need_reg_day`                 INT COMMENT '领奖需要满足注册天数',
    `cover_pc_img_url`             VARCHAR(255) COMMENT '活动列表pc展示图',
    `cover_h5_img_url`             VARCHAR(255) COMMENT '活动列表h5展示图',
    `h5_img_url`                   VARCHAR(255) COMMENT 'h5弹窗图片地址',
    `app_img_url`                  VARCHAR(255) COMMENT 'app弹窗图片地址',
    `detail_img_url`               VARCHAR(255) COMMENT '详情页顶部展示图',
    `home_h5_img_url`              VARCHAR(255) COMMENT 'h5首页下载logo图',
    `h5_text`                      TEXT COMMENT 'h5规则描述',
    `app_text`                     TEXT COMMENT 'app规则描述',
    `no_login_text`                TEXT COMMENT '未登录规则描述',
    `min_picture`                  VARCHAR(255) COMMENT '活动小图地址-可用于左侧等小图标地方',
    `is_side`                      TINYINT(1) COMMENT '是否在客户端侧边栏展示',
    `start_time`                   BIGINT COMMENT '活动开始时间',
    `end_time`                     BIGINT COMMENT '活动开始时间',
    `sort`                         INT COMMENT '展示序号',
    `status_eum`                   INT COMMENT '是否开启',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '下载有礼活动控制';


CREATE INDEX adg_c_pid_idx ON at_download_gift_control (platform_id);
CREATE INDEX adg_c_crt_idx ON at_download_gift_control (platform_id, create_time);

DROP TABLE IF EXISTS at_download_gift_item;
CREATE TABLE at_download_gift_item
(
    `id`                          BIGINT NOT NULL COMMENT 'id主键',
    `sequence`                    INT COMMENT '第几天',
    `download_gift_task_type_eum` INT COMMENT '下载有礼任务类型',
    `award_amount`                DECIMAL(24, 6) COMMENT '奖励金额',
    `bet_multiple`                DECIMAL(24, 6) COMMENT '打码倍数',
    `target_amount`               DECIMAL(24, 6) COMMENT '需要完成的任务金额',
    `platform_id`                 BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                   VARCHAR(32) COMMENT '创建人',
    `create_time`                 BIGINT COMMENT '创建时间',
    `update_by`                   VARCHAR(32) COMMENT '更新人',
    `update_time`                 BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '下载有礼活动明细';


CREATE INDEX adg_i_pid_idx ON at_download_gift_item (platform_id);
CREATE INDEX adg_i_crt_idx ON at_download_gift_item (platform_id, create_time);
CREATE INDEX adg_i_seq_idx ON at_download_gift_item (sequence, platform_id);

DROP TABLE IF EXISTS at_download_gift_entry_record;
CREATE TABLE at_download_gift_entry_record
(
    `id`                           BIGINT NOT NULL COMMENT 'id主键',
    `sequence`                     INT COMMENT '第几天',
    `item_id`                      BIGINT COMMENT '明细id',
    `parent_id`                    BIGINT COMMENT '上级id',
    `task_date`                    VARCHAR(255) COMMENT '任务时间区间',
    `download_gift_status_eum`     INT COMMENT '下载有礼活动完成状态',
    `download_gift_task_type_eum`  INT COMMENT '下载有礼任务类型',
    `ip_address`                   VARCHAR(255) COMMENT 'ip地址',
    `device_id`                    VARCHAR(255) COMMENT '设备id',
    `terminal_type_eum`            INT COMMENT '首次登录设备',
    `complete_amount`              DECIMAL(24, 6) COMMENT '完成金额',
    `is_satisfy_reg`               TINYINT(1) COMMENT '是否满足注册时长要求',
    `is_satisfy_recharge`          TINYINT(1) COMMENT '是否满足充值要求',
    `user_reg_time`                BIGINT COMMENT '用户注册时间',
    `need_history_recharge_amount` DECIMAL(24, 6) COMMENT '领奖需要满足历史充值额',
    `need_reg_day`                 INT COMMENT '领奖需要满足注册天数',
    `award_amount`                 DECIMAL(24, 6) COMMENT '奖励金额',
    `bet_multiple`                 DECIMAL(24, 6) COMMENT '打码倍数',
    `target_amount`                DECIMAL(24, 6) COMMENT '需要完成的任务金额',
    `user_id`                      BIGINT COMMENT '用户id',
    `user_code`                    BIGINT COMMENT '用户code',
    `username`                     VARCHAR(255) COMMENT '用户名称',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '下载有礼活动参与记录';


CREATE INDEX adg_e_pid_idx ON at_download_gift_entry_record (platform_id);
CREATE INDEX adg_e_crt_idx ON at_download_gift_entry_record (platform_id, create_time);
CREATE INDEX adg_uid_pid_idx ON at_download_gift_entry_record (user_id, platform_id);
CREATE INDEX adg_itemid_uid_pid_idx ON at_download_gift_entry_record (item_id, user_id, platform_id);
CREATE INDEX adg_ip_pid_idx ON at_download_gift_entry_record (ip_address, platform_id);
CREATE INDEX adg_did_pid_idx ON at_download_gift_entry_record (device_id, platform_id);

DROP TABLE IF EXISTS at_download_gift_distribute_record;
CREATE TABLE at_download_gift_distribute_record
(
    `id`           BIGINT NOT NULL COMMENT 'id主键',
    `sequence`     INT COMMENT '第几天',
    `item_id`      BIGINT COMMENT '明细id',
    `award_amount` DECIMAL(24, 6) COMMENT '奖励金额',
    `bet_multiple` DECIMAL(24, 6) COMMENT '打码倍数',
    `user_id`      BIGINT COMMENT '用户id',
    `user_code`    BIGINT COMMENT '用户code',
    `username`     VARCHAR(255) COMMENT '用户名称',
    `platform_id`  BIGINT NOT NULL COMMENT '平台id/租户id',
    `create_by`    VARCHAR(32) COMMENT '创建人',
    `create_time`  BIGINT COMMENT '创建时间',
    `update_by`    VARCHAR(32) COMMENT '更新人',
    `update_time`  BIGINT COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '下载有礼活动派奖记录';


CREATE INDEX adg_d_pid_idx ON at_download_gift_distribute_record (platform_id);
CREATE INDEX adg_d_crt_idx ON at_download_gift_distribute_record (platform_id, create_time);

DROP TABLE IF EXISTS at_recharge_discount_control;
CREATE TABLE at_recharge_discount_control
(
    `id`                                      BIGINT NOT NULL COMMENT '主键',
    `platform_id`                             BIGINT NOT NULL COMMENT '平台id',
    `create_by`                               VARCHAR(32) COMMENT '创建人',
    `create_time`                             BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                               VARCHAR(32) COMMENT '更新人',
    `update_time`                             BIGINT COMMENT '更新时间',
    `activity_id`                             BIGINT COMMENT '活动ID',
    `manual_receive`                          TINYINT(1) DEFAULT 1 COMMENT '是否手动领取奖励：默认手动领取',
    `first_recharge_discounter`               INT            DEFAULT 0 COMMENT '是否开启新会员首充赠送',
    `first_recharge_discount_rate`            DECIMAL(24, 6) COMMENT '新会员首充赠送比例',
    `first_recharge_discount_bet_multiple`    DECIMAL(24, 6) COMMENT '新会员首充赠送金额的需求打码倍数',
    `first_recharge_principal_bet_multiple`   DECIMAL(24, 6) DEFAULT 0 COMMENT '新会员首充本金的打码倍数',
    `first_recharge_discount_bet_mode`        INT COMMENT '首充打码方式',
    `first_recharge_discount_max`             DECIMAL(24, 6) COMMENT '新会员首充赠送上限',
    `first_recharge_discount_effective_days`  INT COMMENT '新会员首充赠送有效天数',
    `first_recharge_discount_activate_days`   INT COMMENT '新会员首充赠送激活天数',
    `first_recharge_discount_rule_desc`       TEXT COMMENT '新会员首充优惠规则描述',
    `second_recharge_discounter`              INT            DEFAULT 0 COMMENT '是否开启新会员二充赠送',
    `second_recharge_discount_rate`           DECIMAL(24, 6) COMMENT '新会员二充赠送比例',
    `second_recharge_discount_bet_multiple`   DECIMAL(24, 6) COMMENT '新会员二充赠送金额的需求打码倍数',
    `second_recharge_principal_bet_multiple`  DECIMAL(24, 6) DEFAULT 0 COMMENT '新会员二充本金的打码倍数',
    `second_recharge_discount_bet_mode`       INT COMMENT '二充打码方式',
    `second_recharge_discount_max`            DECIMAL(24, 6) COMMENT '新会员二充赠送上限',
    `second_recharge_discount_effective_days` INT COMMENT '新会员二充赠送有效天数',
    `second_recharge_discount_activate_days`  INT COMMENT '新会员二充赠送激活天数',
    `second_recharge_discount_rule_desc`      TEXT COMMENT '新会员二充赠送规则描述',
    `third_recharge_discounter`               INT            DEFAULT 0 COMMENT '是否开启新会员三充赠送',
    `third_recharge_discount_rate`            DECIMAL(24, 6) COMMENT '新会员三充赠送比例',
    `third_recharge_discount_bet_multiple`    DECIMAL(24, 6) COMMENT '新会员三充赠送金额的需求打码倍数',
    `third_recharge_principal_bet_multiple`   DECIMAL(24, 6) DEFAULT 0 COMMENT '新会员三充本金的打码倍数',
    `third_recharge_discount_bet_mode`        INT COMMENT '新会员三充打码方式',
    `third_recharge_discount_max`             DECIMAL(24, 6) COMMENT '新会员三充赠送上限',
    `third_recharge_discount_effective_days`  INT COMMENT '新会员三充赠送有效天数',
    `third_recharge_discount_activate_days`   INT COMMENT '新会员三充赠送激活天数',
    `third_recharge_discount_rule_desc`       TEXT COMMENT '新会员三充赠送规则描述',
    `first_recharge_min_amount`               DECIMAL(24, 6) DEFAULT 0 COMMENT '新会员首充最小充值金额',
    `second_recharge_min_amount`              DECIMAL(24, 6) DEFAULT 0 COMMENT '新会员二充最小充值金额',
    `third_recharge_min_amount`               DECIMAL(24, 6) DEFAULT 0 COMMENT '新会员三充最小充值金额',
    PRIMARY KEY (id)
) COMMENT = '活动入款优惠控制';

DROP TABLE IF EXISTS at_turn_table_control;
CREATE TABLE at_turn_table_control
(
    `id`                           BIGINT NOT NULL COMMENT '主键',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    `activity_id`                  BIGINT COMMENT '活动ID',
    `turn_table_discount_type_eum` INT COMMENT '转盘赠送金额类型',
    `start_time`                   BIGINT COMMENT '生效时间',
    `expire_time`                  BIGINT COMMENT '失效时间',
    `discount_amount`              DECIMAL(24, 6) COMMENT '奖励金额',
    `bet_multiple`                 DECIMAL(24, 6) COMMENT '打码倍数',
    `min_first_schedule`           DECIMAL(24, 6) COMMENT '最小首次进度',
    `max_first_schedule`           DECIMAL(24, 6) COMMENT '最大首次进度',
    `free_draw_count`              INT COMMENT '免费摇奖次数',
    `invite_draw_count`            INT COMMENT '邀请摇奖次数',
    `win_rate`                     DECIMAL(24, 6) COMMENT '中奖占比（剩余占比为谢谢参与）',
    `remark`                       LONGTEXT COMMENT '备注',
    `valid_date`                   INT COMMENT '优惠钱包有效天数',
    `under_recharge_limit`         DECIMAL(24, 6) COMMENT '下级累计充值限制',
    `under_bet_limit`              DECIMAL(24, 6) COMMENT '下级累计打码限制',
    `invite_member_count`          INT COMMENT '满值摇奖次数',
    `icon`                         VARCHAR(255) COMMENT '图标',
    `record_valid_days`            INT COMMENT '用户转盘有效天数',
    `top_icon`                     VARCHAR(255) COMMENT '转盘弹窗顶部图标',
    `turn_table_model_eum`         INT COMMENT '转盘模式类型',
    `register_count`               INT COMMENT '满值注册摇奖次数',
    `recharge_count`               INT COMMENT '满值充值摇奖次数',
    `three_times_progress`         DECIMAL(24, 6) COMMENT '前n次进度',
    `meeting_progress_times`       INT COMMENT '满足进度次数',
    `visitor_can_play`             TINYINT(1) COMMENT '游客可玩',
    `deleter`                      TINYINT(1) COMMENT '是否删除',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘控制';


CREATE INDEX ttc_pd_idx ON at_turn_table_control (platform_id, start_time, expire_time);

DROP TABLE IF EXISTS at_turn_table_item;
CREATE TABLE at_turn_table_item
(
    `id`              BIGINT NOT NULL COMMENT '主键',
    `platform_id`     BIGINT NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    `control_id`      BIGINT COMMENT '转盘配置ID',
    `min_draw_amount` DECIMAL(24, 6) COMMENT '最小摇奖金额',
    `max_draw_amount` DECIMAL(24, 6) COMMENT '最大摇奖金额',
    `draw_rate`       DECIMAL(24, 6) COMMENT '摇奖几率',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘配置奖项';


CREATE INDEX tti_pc_idx ON at_turn_table_item (platform_id, control_id);

DROP TABLE IF EXISTS at_turn_table_project;
CREATE TABLE at_turn_table_project
(
    `id`                          BIGINT NOT NULL COMMENT '主键',
    `platform_id`                 BIGINT NOT NULL COMMENT '平台id',
    `create_by`                   VARCHAR(32) COMMENT '创建人',
    `create_time`                 BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                   VARCHAR(32) COMMENT '更新人',
    `update_time`                 BIGINT COMMENT '更新时间',
    `control_id`                  BIGINT NOT NULL COMMENT '转盘配置ID',
    `turn_table_draw_typee_eum`   INT COMMENT '转盘中奖类型',
    `turn_table_project_type_eum` INT COMMENT '转盘选项显示类型',
    `content`                     TEXT COMMENT '显示内容',
    `icon_address`                TEXT COMMENT '显示图标',
    `sort`                        INT COMMENT '排序',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘中奖内容';


CREATE INDEX attp_pi_idx ON at_turn_table_project (platform_id, control_id);

DROP TABLE IF EXISTS at_turn_table_record;
CREATE TABLE at_turn_table_record
(
    `id`                           BIGINT NOT NULL COMMENT '主键',
    `platform_id`                  BIGINT NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    `user_id`                      BIGINT COMMENT '用户id',
    `user_code`                    BIGINT COMMENT '用户code',
    `username`                     VARCHAR(255) COMMENT '用户名称',
    `control_id`                   BIGINT NOT NULL COMMENT '转盘配置ID',
    `turn_table_discount_type_eum` INT COMMENT '转盘赠送金额类型',
    `start_time`                   BIGINT COMMENT '生效时间',
    `expire_time`                  BIGINT COMMENT '失效时间',
    `status`                       INT COMMENT '状态',
    `draw_count`                   INT COMMENT '剩余摇奖次数',
    `invite_draw_count`            INT COMMENT '邀请摇奖次数',
    `lottery_amount`               DECIMAL(24, 6) COMMENT '目标中奖金额',
    `now_amount`                   DECIMAL(24, 6) COMMENT '当前金额',
    `bet_multiple`                 DECIMAL(24, 6) COMMENT '打码倍数',
    `win_rate`                     DECIMAL(24, 6) COMMENT '中奖占比（剩余占比为谢谢参与）',
    `valid_date`                   INT COMMENT '优惠钱包有效天数',
    `under_recharge_limit`         DECIMAL(24, 6) COMMENT '下级累计充值限制',
    `under_bet_limit`              DECIMAL(24, 6) COMMENT '下级累计打码限制',
    `free_draw_count`              INT COMMENT '免费摇奖次数',
    `min_first_schedule`           DECIMAL(24, 6) COMMENT '最小首次进度',
    `max_first_schedule`           DECIMAL(24, 6) COMMENT '最大首次进度',
    `invite_member_count`          INT COMMENT '满值摇奖次数',
    `max_value`                    DECIMAL(24, 6) COMMENT '最大随机分配值',
    `drawed_count`                 INT COMMENT '已摇奖次数',
    `turn_table_model_eum`         INT COMMENT '转盘模式类型',
    `register_count`               INT COMMENT '满值注册摇奖次数',
    `recharge_count`               INT COMMENT '满值充值摇奖次数',
    `registered_count`             INT COMMENT '已消耗注册摇奖次数',
    `recharged_count`              INT COMMENT '已消耗充值摇奖次数',
    `register_amount`              DECIMAL(24, 6) COMMENT '注册占比金额',
    `recharge_amount`              DECIMAL(24, 6) COMMENT '充值占比金额',
    `three_times_progress`         DECIMAL(24, 6) COMMENT '前n次进度',
    `meeting_progress_times`       INT COMMENT '满足进度次数',
    `device_identy`                VARCHAR(255) COMMENT '设备标识',
    `now_level`                    BIGINT COMMENT '当前转盘等级',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘参与记录(个人转盘)';


CREATE INDEX ttr_pu_idx ON at_turn_table_record (platform_id, user_id);
CREATE INDEX ttr_pd_idx ON at_turn_table_record (platform_id, start_time, expire_time);
CREATE INDEX ttr_pc_idx ON at_turn_table_record (platform_id, control_id);
CREATE INDEX ttr_pdi_idx ON at_turn_table_record (platform_id, device_identy);

DROP TABLE IF EXISTS at_turn_table_record_item;
CREATE TABLE at_turn_table_record_item
(
    `id`              BIGINT NOT NULL COMMENT '主键',
    `platform_id`     BIGINT NOT NULL COMMENT '平台id',
    `create_by`       VARCHAR(32) COMMENT '创建人',
    `create_time`     BIGINT NOT NULL COMMENT '创建时间',
    `update_by`       VARCHAR(32) COMMENT '更新人',
    `update_time`     BIGINT COMMENT '更新时间',
    `record_id`       BIGINT NOT NULL COMMENT '参与记录ID',
    `min_draw_amount` DECIMAL(24, 6) COMMENT '最小摇奖金额',
    `max_draw_amount` DECIMAL(24, 6) COMMENT '最大摇奖金额',
    `draw_rate`       DECIMAL(24, 6) COMMENT '摇奖几率',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘参与者配置奖项';


CREATE INDEX attri_pr_idx ON at_turn_table_record_item (platform_id, record_id);

DROP TABLE IF EXISTS at_turn_table_record_project;
CREATE TABLE at_turn_table_record_project
(
    `id`                          BIGINT NOT NULL COMMENT '主键',
    `platform_id`                 BIGINT NOT NULL COMMENT '平台id',
    `create_by`                   VARCHAR(32) COMMENT '创建人',
    `create_time`                 BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                   VARCHAR(32) COMMENT '更新人',
    `update_time`                 BIGINT COMMENT '更新时间',
    `record_id`                   BIGINT NOT NULL COMMENT '参与记录ID',
    `turn_table_draw_typee_eum`   INT COMMENT '转盘中奖类型',
    `turn_table_project_type_eum` INT COMMENT '转盘选项显示类型',
    `content`                     TEXT COMMENT '显示内容',
    `icon_address`                TEXT COMMENT '显示图标',
    `sort`                        INT COMMENT '排序',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘参与者配置中奖内容';


CREATE INDEX attrp_pi_idx ON at_turn_table_record_project (platform_id, record_id);

DROP TABLE IF EXISTS at_turn_table_draw_record;
CREATE TABLE at_turn_table_draw_record
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `user_id`     BIGINT COMMENT '用户id',
    `user_code`   BIGINT COMMENT '用户code',
    `username`    VARCHAR(255) COMMENT '用户名称',
    `record_id`   BIGINT NOT NULL COMMENT '参与记录ID',
    `draw_amount` DECIMAL(24, 6) COMMENT '摇奖金额',
    `level`       BIGINT COMMENT '转盘等级',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘摇奖记录';


CREATE INDEX ttdr_pu_idx ON at_turn_table_draw_record (platform_id, user_id);
CREATE INDEX ttdr_pr_idx ON at_turn_table_draw_record (platform_id, record_id);

DROP TABLE IF EXISTS at_turn_table_invite_record;
CREATE TABLE at_turn_table_invite_record
(
    `id`                                BIGINT NOT NULL COMMENT '主键',
    `platform_id`                       BIGINT NOT NULL COMMENT '平台id',
    `create_by`                         VARCHAR(32) COMMENT '创建人',
    `create_time`                       BIGINT NOT NULL COMMENT '创建时间',
    `update_by`                         VARCHAR(32) COMMENT '更新人',
    `update_time`                       BIGINT COMMENT '更新时间',
    `user_id`                           BIGINT COMMENT '用户id',
    `user_code`                         BIGINT COMMENT '用户code',
    `username`                          VARCHAR(255) COMMENT '用户名称',
    `invite_user_id`                    BIGINT COMMENT '邀请用户id',
    `invite_user_code`                  BIGINT COMMENT '邀请用户code',
    `invite_username`                   VARCHAR(255) COMMENT '邀请用户名称',
    `record_id`                         BIGINT NOT NULL COMMENT '参与记录ID',
    `discount_draw_count`               INT COMMENT '赠送摇奖次数',
    `under_recharge_limit`              DECIMAL(24, 6) COMMENT '下级累计充值限制',
    `now_recharge_amount`               DECIMAL(24, 6) COMMENT '当前充值金额',
    `successer`                         TINYINT(1) COMMENT '是否成功赠送邀请次数',
    `under_bet_limit`                   DECIMAL(24, 6) COMMENT '下级累计打码限制',
    `now_bet_amount`                    DECIMAL(24, 6) COMMENT '当前打码金额',
    `turn_table_model_eum`              INT COMMENT '转盘模式类型',
    `turn_table_invite_source_type_eum` INT COMMENT '转盘次数赠送来源类型',
    `drawed_count`                      INT COMMENT '已使用次数',
    `used`                              TINYINT(1) COMMENT '是否已被使用',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘邀请记录';


CREATE INDEX ttir_pu_idx ON at_turn_table_invite_record (platform_id, user_id, invite_user_id);
CREATE INDEX ttir_pr_idx ON at_turn_table_invite_record (platform_id, record_id);

DROP TABLE IF EXISTS at_turn_table_job_record;
CREATE TABLE at_turn_table_job_record
(
    `id`          BIGINT NOT NULL COMMENT '主键',
    `platform_id` BIGINT NOT NULL COMMENT '平台id',
    `create_by`   VARCHAR(32) COMMENT '创建人',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_by`   VARCHAR(32) COMMENT '更新人',
    `update_time` BIGINT COMMENT '更新时间',
    `job_status`  INT COMMENT '任务状态',
    `job_date`    VARCHAR(90) COMMENT '任务日期-年-月-日',
    PRIMARY KEY (id)
) COMMENT = '裂变转盘JOB执行记录表';


CREATE INDEX attjr_pd_idx ON at_turn_table_job_record (platform_id, job_status, job_date);

DROP TABLE IF EXISTS at_turn_table_device;
CREATE TABLE at_turn_table_device
(
    `id`             BIGINT NOT NULL COMMENT '主键',
    `platform_id`    BIGINT NOT NULL COMMENT '平台id',
    `create_by`      VARCHAR(32) COMMENT '创建人',
    `create_time`    BIGINT NOT NULL COMMENT '创建时间',
    `update_by`      VARCHAR(32) COMMENT '更新人',
    `update_time`    BIGINT COMMENT '更新时间',
    `record_id`      BIGINT COMMENT '参与记录ID',
    `device_identy`  VARCHAR(255) COMMENT '设备标识',
    `under_user_ids` LONGTEXT COMMENT '下级用户ID列表',
    `user_id`        BIGINT COMMENT '用户id',
    `user_code`      BIGINT COMMENT '用户code',
    `username`       VARCHAR(255) COMMENT '用户名称',
    `control_id`     BIGINT COMMENT '转盘控制ID',
    `start_time`     BIGINT COMMENT '生效时间',
    `expire_time`    BIGINT COMMENT '失效时间',
    PRIMARY KEY (id)
) COMMENT = '转盘设备绑定表';


CREATE INDEX attd_rd_idx ON at_turn_table_device (record_id, device_identy);
CREATE INDEX attd_ru_idx ON at_turn_table_device (record_id, user_id);

DROP TABLE IF EXISTS at_turn_table_level;
CREATE TABLE at_turn_table_level
(
    `id`                           BIGINT         NOT NULL COMMENT '主键',
    `platform_id`                  BIGINT         NOT NULL COMMENT '平台id',
    `create_by`                    VARCHAR(32) COMMENT '创建人',
    `create_time`                  BIGINT         NOT NULL COMMENT '创建时间',
    `update_by`                    VARCHAR(32) COMMENT '更新人',
    `update_time`                  BIGINT COMMENT '更新时间',
    `control_id`                   BIGINT COMMENT '转盘控制ID',
    `level`                        BIGINT         NOT NULL COMMENT '等级',
    `lottery_amount`               DECIMAL(24, 6) NOT NULL COMMENT '奖励金额',
    `turn_table_discount_type_eum` INT COMMENT '转盘赠送金额类型',
    `bet_multiple`                 DECIMAL(24, 6) COMMENT '打码倍数',
    `min_first_schedule`           DECIMAL(24, 6) COMMENT '最小首次进度',
    `max_first_schedule`           DECIMAL(24, 6) COMMENT '最大首次进度',
    `free_draw_count`              INT COMMENT '免费摇奖次数',
    `under_recharge_limit`         DECIMAL(24, 6) COMMENT '下级累计充值限制',
    `invite_draw_count`            INT COMMENT '邀请摇奖次数',
    `register_count`               INT COMMENT '满值注册摇奖次数',
    `recharge_count`               INT COMMENT '满值充值摇奖次数',
    `three_times_progress`         DECIMAL(24, 6) COMMENT '前n次进度',
    `meeting_progress_times`       INT COMMENT '满足进度次数',
    `record_valid_days`            INT COMMENT '用户转盘有效天数',
    `valid_date`                   INT COMMENT '优惠钱包有效天数',
    PRIMARY KEY (id)
) COMMENT = '转盘等级配置';


CREATE INDEX attl_pl_idx ON at_turn_table_level (platform_id, level);
CREATE INDEX attl_pc_idx ON at_turn_table_level (platform_id, control_id);

