<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">

    <springProperty scope="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>
    <springProperty scope="context" name="xiaoyu.logstash.url" source="xiaoyu.logstash.url" defaultValue=""/>

    <!-- Console output -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder>
            <pattern>
                %-4relative  %red(%d{yyyy-MM-dd HH:mm:ss.SSS})  %highlight(%level)  %cyan([%thread])  %red(${spring.application.name})  o-zone-id=>[%X{o-zone-id}]  o-data-language=>[%X{o-data-language}]  o-page-language=>[%X{o-page-language}]  o-platform-id=>[%X{o-platform-id}]  o-platform-domain-name=>[%X{o-platform-domain-name}]  o-user-id=>[%X{o-user-id}]   o-user-tok=>[%X{o-user-tok}]  User-Agent=>[%X{User-Agent}]  o-device-id=>[%X{o-device-id}]  o-version=>[%X{o-version}]  o-ts=>[%X{o-ts}]  o-nonce=>[%X{o-nonce}]  o-system-type=>[%X{o-system-type}]  o-terminal-type=>[%X{o-terminal-type}]  o-separate-package-id=>[%X{o-separate-package-id}]  o-browser-fingerprint=>[%X{o-browser-fingerprint}]  o-browser-clientjs=>[%X{o-browser-clientjs}]  biz-trace-id=>[%X{biz-trace-id}]  o-i-ip=>[%X{o-i-ip}]  o-i-amount-cny=>[%X{o-i-amount-cny}]  i-amount-symbol=>[%X{i-amount-symbol}]  i-amount-format=>[%X{i-amount-format}]  span-id=>[%X{span-id}]  n-d-service-group=>[%X{n-d-service-group}] n-d-service-id=> [%X{n-d-service-id}]  n-d-service-address=>[%X{n-d-service-address}] %green(%logger{36})  [%file:%line] - %boldYellow(%msg%n)</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- Only log level WARN and above -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- For loggers in the these namespaces, log at all levels. -->
    <logger name="pedestal" level="ALL"/>
    <logger name="hammock-cafe" level="ALL"/>
    <logger name="user" level="ALL"/>

    <!-- dev的时候激活-->
    <springProfile name="dev">
        <root level="INFO">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
        </root>
        <logger name="cn.xiaoyu" level="DEBUG" additivity="false">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
        </logger>
    </springProfile>

    <!-- test的时候激活-->
    <springProfile name="test">
        <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            <destination>${xiaoyu.logstash.url}</destination>
            <!-- encoder必须配置,有多种可选 -->
            <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
                <customFields>{"service":"run-service-user"}</customFields>
                <includeMdcKeyName>spring.application.name</includeMdcKeyName>
                <includeMdcKeyName>o-zone-id</includeMdcKeyName>
                <includeMdcKeyName>o-data-language</includeMdcKeyName>
                <includeMdcKeyName>o-page-language</includeMdcKeyName>
                <includeMdcKeyName>o-platform-id</includeMdcKeyName>
                <includeMdcKeyName>o-platform-domain-name</includeMdcKeyName>
                <includeMdcKeyName>o-user-id</includeMdcKeyName>
                <includeMdcKeyName>o-user-tok</includeMdcKeyName>
                <includeMdcKeyName>User-Agent</includeMdcKeyName>
                <includeMdcKeyName>o-device-id</includeMdcKeyName>
                <includeMdcKeyName>o-version</includeMdcKeyName>
                <includeMdcKeyName>o-ts</includeMdcKeyName>
                <includeMdcKeyName>o-nonce</includeMdcKeyName>
                <includeMdcKeyName>o-system-type</includeMdcKeyName>
                <includeMdcKeyName>o-terminal-type</includeMdcKeyName>
                <includeMdcKeyName>o-separate-package-id</includeMdcKeyName>
                <includeMdcKeyName>o-browser-fingerprint</includeMdcKeyName>
                <includeMdcKeyName>o-browser-clientjs</includeMdcKeyName>
                <includeMdcKeyName>biz-trace-id</includeMdcKeyName>
                <includeMdcKeyName>o-i-ip</includeMdcKeyName>
                <includeMdcKeyName>o-i-amount-cny</includeMdcKeyName>
                <includeMdcKeyName>i-amount-symbol</includeMdcKeyName>
                <includeMdcKeyName>i-amount-format</includeMdcKeyName>
                <includeMdcKeyName>span-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-group</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-address</includeMdcKeyName>
            </encoder>
            <connectionStrategy>
                <roundRobin>
                    <connectionTTL>5 minutes</connectionTTL>
                </roundRobin>
            </connectionStrategy>
        </appender>
        <root level="INFO">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </root>
        <logger name="cn.xiaoyu" level="DEBUG" additivity="false">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </logger>
    </springProfile>

    <!-- uat的时候激活-->
    <springProfile name="uat">
        <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            <destination>${xiaoyu.logstash.url}</destination>
            <!-- encoder必须配置,有多种可选 -->
            <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
                <customFields>{"service":"run-service-user"}</customFields>
                <includeMdcKeyName>spring.application.name</includeMdcKeyName>
                <includeMdcKeyName>o-zone-id</includeMdcKeyName>
                <includeMdcKeyName>o-data-language</includeMdcKeyName>
                <includeMdcKeyName>o-page-language</includeMdcKeyName>
                <includeMdcKeyName>o-platform-id</includeMdcKeyName>
                <includeMdcKeyName>o-platform-domain-name</includeMdcKeyName>
                <includeMdcKeyName>o-user-id</includeMdcKeyName>
                <includeMdcKeyName>o-user-tok</includeMdcKeyName>
                <includeMdcKeyName>User-Agent</includeMdcKeyName>
                <includeMdcKeyName>o-device-id</includeMdcKeyName>
                <includeMdcKeyName>o-version</includeMdcKeyName>
                <includeMdcKeyName>o-ts</includeMdcKeyName>
                <includeMdcKeyName>o-nonce</includeMdcKeyName>
                <includeMdcKeyName>o-system-type</includeMdcKeyName>
                <includeMdcKeyName>o-terminal-type</includeMdcKeyName>
                <includeMdcKeyName>o-separate-package-id</includeMdcKeyName>
                <includeMdcKeyName>o-browser-fingerprint</includeMdcKeyName>
                <includeMdcKeyName>o-browser-clientjs</includeMdcKeyName>
                <includeMdcKeyName>biz-trace-id</includeMdcKeyName>
                <includeMdcKeyName>o-i-ip</includeMdcKeyName>
                <includeMdcKeyName>o-i-amount-cny</includeMdcKeyName>
                <includeMdcKeyName>i-amount-symbol</includeMdcKeyName>
                <includeMdcKeyName>i-amount-format</includeMdcKeyName>
                <includeMdcKeyName>span-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-group</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-address</includeMdcKeyName>
            </encoder>
            <connectionStrategy>
                <roundRobin>
                    <connectionTTL>5 minutes</connectionTTL>
                </roundRobin>
            </connectionStrategy>
        </appender>
        <root level="INFO">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </root>
        <logger name="cn.xiaoyu" level="DEBUG" additivity="false">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </logger>
    </springProfile>

    <!-- pre的时候激活-->
    <springProfile name="pre">
        <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            <destination>${xiaoyu.logstash.url}</destination>
            <!-- encoder必须配置,有多种可选 -->
            <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
                <customFields>{"service":"run-service-user"}</customFields>
                <includeMdcKeyName>spring.application.name</includeMdcKeyName>
                <includeMdcKeyName>o-zone-id</includeMdcKeyName>
                <includeMdcKeyName>o-data-language</includeMdcKeyName>
                <includeMdcKeyName>o-page-language</includeMdcKeyName>
                <includeMdcKeyName>o-platform-id</includeMdcKeyName>
                <includeMdcKeyName>o-platform-domain-name</includeMdcKeyName>
                <includeMdcKeyName>o-user-id</includeMdcKeyName>
                <includeMdcKeyName>o-user-tok</includeMdcKeyName>
                <includeMdcKeyName>User-Agent</includeMdcKeyName>
                <includeMdcKeyName>o-device-id</includeMdcKeyName>
                <includeMdcKeyName>o-version</includeMdcKeyName>
                <includeMdcKeyName>o-ts</includeMdcKeyName>
                <includeMdcKeyName>o-nonce</includeMdcKeyName>
                <includeMdcKeyName>o-system-type</includeMdcKeyName>
                <includeMdcKeyName>o-terminal-type</includeMdcKeyName>
                <includeMdcKeyName>o-separate-package-id</includeMdcKeyName>
                <includeMdcKeyName>o-browser-fingerprint</includeMdcKeyName>
                <includeMdcKeyName>o-browser-clientjs</includeMdcKeyName>
                <includeMdcKeyName>biz-trace-id</includeMdcKeyName>
                <includeMdcKeyName>o-i-ip</includeMdcKeyName>
                <includeMdcKeyName>o-i-amount-cny</includeMdcKeyName>
                <includeMdcKeyName>i-amount-symbol</includeMdcKeyName>
                <includeMdcKeyName>i-amount-format</includeMdcKeyName>
                <includeMdcKeyName>span-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-group</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-address</includeMdcKeyName>
            </encoder>
            <connectionStrategy>
                <roundRobin>
                    <connectionTTL>5 minutes</connectionTTL>
                </roundRobin>
            </connectionStrategy>
        </appender>
        <root level="INFO">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </root>
        <logger name="cn.xiaoyu" level="DEBUG" additivity="false">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </logger>
    </springProfile>

    <!-- prod的时候激活-->
    <springProfile name="prod">
        <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            <destination>${xiaoyu.logstash.url}</destination>
            <!-- encoder必须配置,有多种可选 -->
            <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
                <customFields>{"service":"run-service-user"}</customFields>
                <includeMdcKeyName>spring.application.name</includeMdcKeyName>
                <includeMdcKeyName>o-zone-id</includeMdcKeyName>
                <includeMdcKeyName>o-data-language</includeMdcKeyName>
                <includeMdcKeyName>o-page-language</includeMdcKeyName>
                <includeMdcKeyName>o-platform-id</includeMdcKeyName>
                <includeMdcKeyName>o-platform-domain-name</includeMdcKeyName>
                <includeMdcKeyName>o-user-id</includeMdcKeyName>
                <includeMdcKeyName>o-user-tok</includeMdcKeyName>
                <includeMdcKeyName>User-Agent</includeMdcKeyName>
                <includeMdcKeyName>o-device-id</includeMdcKeyName>
                <includeMdcKeyName>o-version</includeMdcKeyName>
                <includeMdcKeyName>o-ts</includeMdcKeyName>
                <includeMdcKeyName>o-nonce</includeMdcKeyName>
                <includeMdcKeyName>o-system-type</includeMdcKeyName>
                <includeMdcKeyName>o-terminal-type</includeMdcKeyName>
                <includeMdcKeyName>o-separate-package-id</includeMdcKeyName>
                <includeMdcKeyName>o-browser-fingerprint</includeMdcKeyName>
                <includeMdcKeyName>o-browser-clientjs</includeMdcKeyName>
                <includeMdcKeyName>biz-trace-id</includeMdcKeyName>
                <includeMdcKeyName>o-i-ip</includeMdcKeyName>
                <includeMdcKeyName>o-i-amount-cny</includeMdcKeyName>
                <includeMdcKeyName>i-amount-symbol</includeMdcKeyName>
                <includeMdcKeyName>i-amount-format</includeMdcKeyName>
                <includeMdcKeyName>span-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-group</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-id</includeMdcKeyName>
                <includeMdcKeyName>n-d-service-address</includeMdcKeyName>
            </encoder>
            <connectionStrategy>
                <roundRobin>
                    <connectionTTL>5 minutes</connectionTTL>
                </roundRobin>
            </connectionStrategy>
        </appender>
        <root level="INFO">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </root>
        <logger name="cn.xiaoyu" level="DEBUG" additivity="false">
            <!-- 打印控制台 -->
            <appender-ref ref="STDOUT"/>
            <!-- 输出到logstash -->
            <appender-ref ref="LOGSTASH"/>
        </logger>
    </springProfile>

</configuration>
