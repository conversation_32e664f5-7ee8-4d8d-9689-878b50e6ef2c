#gobrs:
#  async:
#    config:
#      ## 如果规则没有制定 线程池 则使用 统一的线程池配置 如果通过 API 的方式动态更新了线程池 则使用动态更新 替换配置文件线程池配置 参见： ThreadPoolConfig
#      #            thread-pool:
#      #                core-pool-size: 1000
#      #                max-pool-size: 2000
#      rules:
#        # 规则 是数组类型的 多组规则
#        - name: "queryAgentTeamInfo"
#          content: "QueryTeamBetInfoTask,QueryTeamChangeInfoTask,QueryTeamCommissionInfoTask,QueryTeamRechargeAndWithdrawInfoTask,QueryTeamTaskDistributeInfoTask,QueryTeamWashInfoTask"
#
